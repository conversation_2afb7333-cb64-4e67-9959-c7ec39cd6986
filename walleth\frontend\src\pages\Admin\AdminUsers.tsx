import React, { useEffect, useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  InputAdornment,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Grid,
  Card,
  CardContent,
  Avatar,
} from '@mui/material';
import {
  Search as SearchIcon,
  MoreVert as MoreIcon,
  Visibility as ViewIcon,
  Block as BlockIcon,
  CheckCircle as ApproveIcon,
  Person as PersonIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  CalendarToday as DateIcon,
} from '@mui/icons-material';

// Services
import { adminAPI } from '@/services/api';

interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  phone: string;
  kycStatus: string;
  isEmailVerified: boolean;
  isPhoneVerified: boolean;
  is2FAEnabled: boolean;
  status: string;
  createdAt: string;
  lastLoginAt: string;
}

const AdminUsers: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [totalUsers, setTotalUsers] = useState(0);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [userDialog, setUserDialog] = useState(false);
  const [actionMenu, setActionMenu] = useState<{ anchorEl: HTMLElement | null; user: User | null }>({
    anchorEl: null,
    user: null,
  });

  useEffect(() => {
    fetchUsers();
  }, [page, rowsPerPage, searchTerm]);

  const fetchUsers = async () => {
    try {
      const response = await adminAPI.getUsers({
        page: page + 1,
        limit: rowsPerPage,
        search: searchTerm,
      });
      setUsers(response.data.users);
      setTotalUsers(response.data.total);
    } catch (error) {
      console.error('Failed to fetch users:', error);
    }
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
    setPage(0);
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleActionClick = (event: React.MouseEvent<HTMLElement>, user: User) => {
    setActionMenu({ anchorEl: event.currentTarget, user });
  };

  const handleActionClose = () => {
    setActionMenu({ anchorEl: null, user: null });
  };

  const handleViewUser = (user: User) => {
    setSelectedUser(user);
    setUserDialog(true);
    handleActionClose();
  };

  const handleUpdateUserStatus = async (userId: string, status: string) => {
    try {
      await adminAPI.updateUserStatus(userId, status);
      fetchUsers();
      handleActionClose();
    } catch (error) {
      console.error('Failed to update user status:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'success';
      case 'SUSPENDED':
        return 'error';
      case 'PENDING':
        return 'warning';
      default:
        return 'default';
    }
  };

  const getKycStatusColor = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return 'success';
      case 'REJECTED':
        return 'error';
      case 'SUBMITTED':
        return 'info';
      default:
        return 'warning';
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom>
          User Management
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Manage CryptoNest users and their account status
        </Typography>
      </Box>

      {/* Search and Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              placeholder="Search users by email, name, or ID..."
              value={searchTerm}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
              <Chip label={`Total: ${totalUsers}`} color="primary" />
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {/* Users Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>User</TableCell>
              <TableCell>Contact</TableCell>
              <TableCell>KYC Status</TableCell>
              <TableCell>Account Status</TableCell>
              <TableCell>Security</TableCell>
              <TableCell>Joined</TableCell>
              <TableCell>Last Login</TableCell>
              <TableCell align="center">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {users.map((user) => (
              <TableRow key={user.id} hover>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Avatar sx={{ mr: 2, bgcolor: 'primary.main' }}>
                      {user.firstName ? user.firstName[0] : user.email[0]}
                    </Avatar>
                    <Box>
                      <Typography variant="body2" fontWeight={600}>
                        {user.firstName && user.lastName 
                          ? `${user.firstName} ${user.lastName}`
                          : 'No name'
                        }
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {user.email}
                      </Typography>
                    </Box>
                  </Box>
                </TableCell>
                <TableCell>
                  <Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                      <EmailIcon sx={{ fontSize: 16, mr: 0.5, color: 'text.secondary' }} />
                      <Chip
                        label={user.isEmailVerified ? 'Verified' : 'Not Verified'}
                        size="small"
                        color={user.isEmailVerified ? 'success' : 'error'}
                        variant="outlined"
                      />
                    </Box>
                    {user.phone && (
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <PhoneIcon sx={{ fontSize: 16, mr: 0.5, color: 'text.secondary' }} />
                        <Chip
                          label={user.isPhoneVerified ? 'Verified' : 'Not Verified'}
                          size="small"
                          color={user.isPhoneVerified ? 'success' : 'error'}
                          variant="outlined"
                        />
                      </Box>
                    )}
                  </Box>
                </TableCell>
                <TableCell>
                  <Chip
                    label={user.kycStatus}
                    color={getKycStatusColor(user.kycStatus)}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <Chip
                    label={user.status}
                    color={getStatusColor(user.status)}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <Chip
                    label={user.is2FAEnabled ? '2FA On' : '2FA Off'}
                    size="small"
                    color={user.is2FAEnabled ? 'success' : 'warning'}
                    variant="outlined"
                  />
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {new Date(user.createdAt).toLocaleDateString()}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {user.lastLoginAt 
                      ? new Date(user.lastLoginAt).toLocaleDateString()
                      : 'Never'
                    }
                  </Typography>
                </TableCell>
                <TableCell align="center">
                  <IconButton
                    size="small"
                    onClick={(e) => handleActionClick(e, user)}
                  >
                    <MoreIcon />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
        
        <TablePagination
          rowsPerPageOptions={[10, 25, 50, 100]}
          component="div"
          count={totalUsers}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </TableContainer>

      {/* Action Menu */}
      <Menu
        anchorEl={actionMenu.anchorEl}
        open={Boolean(actionMenu.anchorEl)}
        onClose={handleActionClose}
      >
        <MenuItem onClick={() => actionMenu.user && handleViewUser(actionMenu.user)}>
          <ViewIcon sx={{ mr: 1 }} />
          View Details
        </MenuItem>
        {actionMenu.user?.status === 'ACTIVE' && (
          <MenuItem 
            onClick={() => actionMenu.user && handleUpdateUserStatus(actionMenu.user.id, 'SUSPENDED')}
            sx={{ color: 'error.main' }}
          >
            <BlockIcon sx={{ mr: 1 }} />
            Suspend User
          </MenuItem>
        )}
        {actionMenu.user?.status === 'SUSPENDED' && (
          <MenuItem 
            onClick={() => actionMenu.user && handleUpdateUserStatus(actionMenu.user.id, 'ACTIVE')}
            sx={{ color: 'success.main' }}
          >
            <ApproveIcon sx={{ mr: 1 }} />
            Activate User
          </MenuItem>
        )}
      </Menu>

      {/* User Details Dialog */}
      <Dialog open={userDialog} onClose={() => setUserDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>User Details</DialogTitle>
        <DialogContent>
          {selectedUser && (
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Personal Information
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Avatar sx={{ mr: 2, bgcolor: 'primary.main', width: 56, height: 56 }}>
                        {selectedUser.firstName ? selectedUser.firstName[0] : selectedUser.email[0]}
                      </Avatar>
                      <Box>
                        <Typography variant="h6">
                          {selectedUser.firstName && selectedUser.lastName 
                            ? `${selectedUser.firstName} ${selectedUser.lastName}`
                            : 'No name provided'
                          }
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {selectedUser.email}
                        </Typography>
                      </Box>
                    </Box>
                    
                    <Box sx={{ mt: 2 }}>
                      <Typography variant="body2" color="text.secondary">Phone:</Typography>
                      <Typography variant="body1">
                        {selectedUser.phone || 'Not provided'}
                      </Typography>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>

              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Account Status
                    </Typography>
                    
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" color="text.secondary">KYC Status:</Typography>
                      <Chip
                        label={selectedUser.kycStatus}
                        color={getKycStatusColor(selectedUser.kycStatus)}
                        size="small"
                        sx={{ mt: 0.5 }}
                      />
                    </Box>

                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" color="text.secondary">Account Status:</Typography>
                      <Chip
                        label={selectedUser.status}
                        color={getStatusColor(selectedUser.status)}
                        size="small"
                        sx={{ mt: 0.5 }}
                      />
                    </Box>

                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" color="text.secondary">Security:</Typography>
                      <Box sx={{ display: 'flex', gap: 1, mt: 0.5 }}>
                        <Chip
                          label={selectedUser.isEmailVerified ? 'Email Verified' : 'Email Not Verified'}
                          size="small"
                          color={selectedUser.isEmailVerified ? 'success' : 'error'}
                          variant="outlined"
                        />
                        <Chip
                          label={selectedUser.is2FAEnabled ? '2FA Enabled' : '2FA Disabled'}
                          size="small"
                          color={selectedUser.is2FAEnabled ? 'success' : 'warning'}
                          variant="outlined"
                        />
                      </Box>
                    </Box>

                    <Box>
                      <Typography variant="body2" color="text.secondary">Joined:</Typography>
                      <Typography variant="body1">
                        {new Date(selectedUser.createdAt).toLocaleString()}
                      </Typography>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setUserDialog(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AdminUsers;
