import express from 'express';
import { body, query, validationResult } from 'express-validator';
import { PrismaClient } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';
import { asyncHandler } from '../middleware/errorHandler';
import { ValidationError, NotFoundError, AuthorizationError } from '../middleware/errorHandler';
import { adminMiddleware, superAdminMiddleware } from '../middleware/auth';
import { logger } from '../utils/logger';
import { NotificationService } from '../services/notificationService';

const router = express.Router();
const prisma = new PrismaClient();
const notificationService = new NotificationService();

// All admin routes require admin access
router.use(adminMiddleware);

// Dashboard statistics
router.get('/dashboard',
  asyncHandler(async (req, res) => {
    const [
      totalUsers,
      activeUsers,
      totalTrades24h,
      totalVolume24h,
      pendingKYC,
      pendingWithdrawals,
      totalRevenue
    ] = await Promise.all([
      prisma.user.count(),
      prisma.user.count({
        where: {
          status: 'ACTIVE',
          lastLoginAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
          }
        }
      }),
      prisma.trade.count({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000)
          }
        }
      }),
      prisma.trade.aggregate({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000)
          }
        },
        _sum: {
          amount: true
        }
      }),
      prisma.user.count({
        where: { kycStatus: 'SUBMITTED' }
      }),
      prisma.withdrawal.count({
        where: { status: 'PENDING' }
      }),
      prisma.trade.aggregate({
        _sum: {
          buyerFee: true,
          sellerFee: true
        }
      })
    ]);

    const stats = {
      users: {
        total: totalUsers,
        active: activeUsers
      },
      trading: {
        trades24h: totalTrades24h,
        volume24h: totalVolume24h._sum.amount?.toString() || '0'
      },
      pending: {
        kyc: pendingKYC,
        withdrawals: pendingWithdrawals
      },
      revenue: {
        totalFees: new Decimal(totalRevenue._sum.buyerFee || 0)
          .plus(totalRevenue._sum.sellerFee || 0)
          .toString()
      }
    };

    res.json({
      success: true,
      data: { stats }
    });
  })
);

// User management
router.get('/users',
  query('page').optional().isInt({ min: 1 }),
  query('limit').optional().isInt({ min: 1, max: 100 }),
  query('search').optional().isString(),
  query('status').optional().isIn(['ACTIVE', 'SUSPENDED', 'BANNED', 'PENDING_VERIFICATION']),
  query('kycStatus').optional().isIn(['PENDING', 'SUBMITTED', 'APPROVED', 'REJECTED', 'EXPIRED']),
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError(errors.array().map(err => err.msg).join(', '));
    }

    const {
      page = 1,
      limit = 20,
      search,
      status,
      kycStatus
    } = req.query;

    const skip = (Number(page) - 1) * Number(limit);

    // Build where clause
    const where: any = {};
    
    if (search) {
      where.OR = [
        { email: { contains: search as string, mode: 'insensitive' } },
        { firstName: { contains: search as string, mode: 'insensitive' } },
        { lastName: { contains: search as string, mode: 'insensitive' } }
      ];
    }
    
    if (status) where.status = status;
    if (kycStatus) where.kycStatus = kycStatus;

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        select: {
          id: true,
          email: true,
          firstName: true,
          lastName: true,
          role: true,
          status: true,
          kycStatus: true,
          kycLevel: true,
          isEmailVerified: true,
          isPhoneVerified: true,
          is2FAEnabled: true,
          lastLoginAt: true,
          createdAt: true
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: Number(limit)
      }),
      prisma.user.count({ where })
    ]);

    res.json({
      success: true,
      data: {
        users,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit))
        }
      }
    });
  })
);

// Get specific user details
router.get('/users/:userId',
  asyncHandler(async (req, res) => {
    const { userId } = req.params;

    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: {
        wallets: {
          include: {
            currency: {
              select: {
                symbol: true,
                name: true
              }
            }
          }
        },
        kycDocuments: true,
        loginHistory: {
          orderBy: { createdAt: 'desc' },
          take: 10
        }
      }
    });

    if (!user) {
      throw new NotFoundError('User not found');
    }

    // Get user statistics
    const [totalTrades, totalVolume, totalFees] = await Promise.all([
      prisma.trade.count({
        where: {
          OR: [
            { buyUserId: userId },
            { sellUserId: userId }
          ]
        }
      }),
      prisma.trade.aggregate({
        where: {
          OR: [
            { buyUserId: userId },
            { sellUserId: userId }
          ]
        },
        _sum: {
          amount: true
        }
      }),
      prisma.trade.aggregate({
        where: {
          OR: [
            { buyUserId: userId },
            { sellUserId: userId }
          ]
        },
        _sum: {
          buyerFee: true,
          sellerFee: true
        }
      })
    ]);

    const userWithStats = {
      ...user,
      password: undefined, // Remove password from response
      twoFactorSecret: undefined, // Remove 2FA secret
      stats: {
        totalTrades,
        totalVolume: totalVolume._sum.amount?.toString() || '0',
        totalFees: new Decimal(totalFees._sum.buyerFee || 0)
          .plus(totalFees._sum.sellerFee || 0)
          .toString()
      }
    };

    res.json({
      success: true,
      data: { user: userWithStats }
    });
  })
);

// Update user status
router.put('/users/:userId/status',
  body('status').isIn(['ACTIVE', 'SUSPENDED', 'BANNED']).withMessage('Invalid status'),
  body('reason').optional().isString(),
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError(errors.array().map(err => err.msg).join(', '));
    }

    const { userId } = req.params;
    const { status, reason } = req.body;
    const adminId = req.user!.id;

    const user = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      throw new NotFoundError('User not found');
    }

    // Update user status
    await prisma.user.update({
      where: { id: userId },
      data: { status }
    });

    // Send notification to user
    let notificationMessage = '';
    switch (status) {
      case 'SUSPENDED':
        notificationMessage = `Your account has been suspended. ${reason ? `Reason: ${reason}` : ''}`;
        break;
      case 'BANNED':
        notificationMessage = `Your account has been banned. ${reason ? `Reason: ${reason}` : ''}`;
        break;
      case 'ACTIVE':
        notificationMessage = 'Your account has been reactivated.';
        break;
    }

    await notificationService.sendNotification({
      userId,
      type: 'SECURITY_ALERT',
      title: 'Account Status Updated',
      message: notificationMessage,
      sendEmail: true
    });

    logger.info('User status updated by admin', {
      userId,
      adminId,
      newStatus: status,
      reason
    });

    res.json({
      success: true,
      message: 'User status updated successfully'
    });
  })
);

// KYC Management
router.get('/kyc/pending',
  asyncHandler(async (req, res) => {
    const { page = 1, limit = 20 } = req.query;
    const skip = (Number(page) - 1) * Number(limit);

    const [kycSubmissions, total] = await Promise.all([
      prisma.user.findMany({
        where: { kycStatus: 'SUBMITTED' },
        include: {
          kycDocuments: {
            orderBy: { submittedAt: 'desc' }
          }
        },
        orderBy: { updatedAt: 'asc' },
        skip,
        take: Number(limit)
      }),
      prisma.user.count({
        where: { kycStatus: 'SUBMITTED' }
      })
    ]);

    res.json({
      success: true,
      data: {
        submissions: kycSubmissions,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit))
        }
      }
    });
  })
);

// Approve/Reject KYC
router.put('/kyc/:userId/:action',
  body('reason').optional().isString(),
  asyncHandler(async (req, res) => {
    const { userId, action } = req.params;
    const { reason } = req.body;
    const adminId = req.user!.id;

    if (!['approve', 'reject'].includes(action)) {
      throw new ValidationError('Invalid action. Must be approve or reject');
    }

    const user = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      throw new NotFoundError('User not found');
    }

    if (user.kycStatus !== 'SUBMITTED') {
      throw new ValidationError('KYC is not in submitted status');
    }

    const newStatus = action === 'approve' ? 'APPROVED' : 'REJECTED';
    const newLevel = action === 'approve' ? 1 : 0;

    // Update user KYC status
    await prisma.user.update({
      where: { id: userId },
      data: {
        kycStatus: newStatus,
        kycLevel: newLevel
      }
    });

    // Update KYC documents status
    await prisma.kycDocument.updateMany({
      where: { userId },
      data: {
        status: action === 'approve' ? 'APPROVED' : 'REJECTED',
        rejectionReason: action === 'reject' ? reason : null,
        reviewedAt: new Date(),
        reviewedBy: adminId
      }
    });

    // Send notification
    await notificationService.sendNotification({
      userId,
      type: action === 'approve' ? 'KYC_APPROVED' : 'KYC_REJECTED',
      title: `KYC ${action === 'approve' ? 'Approved' : 'Rejected'}`,
      message: action === 'approve' 
        ? 'Your KYC verification has been approved. You now have access to all platform features.'
        : `Your KYC verification has been rejected. ${reason ? `Reason: ${reason}` : 'Please review and resubmit your documents.'}`,
      sendEmail: true
    });

    logger.info(`KYC ${action}d by admin`, {
      userId,
      adminId,
      reason
    });

    res.json({
      success: true,
      message: `KYC ${action}d successfully`
    });
  })
);

// Withdrawal management
router.get('/withdrawals/pending',
  asyncHandler(async (req, res) => {
    const { page = 1, limit = 20 } = req.query;
    const skip = (Number(page) - 1) * Number(limit);

    const [withdrawals, total] = await Promise.all([
      prisma.withdrawal.findMany({
        where: { status: 'PENDING' },
        include: {
          user: {
            select: {
              email: true,
              firstName: true,
              lastName: true,
              kycStatus: true
            }
          },
          currency: {
            select: {
              symbol: true,
              name: true
            }
          }
        },
        orderBy: { createdAt: 'asc' },
        skip,
        take: Number(limit)
      }),
      prisma.withdrawal.count({
        where: { status: 'PENDING' }
      })
    ]);

    res.json({
      success: true,
      data: {
        withdrawals,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit))
        }
      }
    });
  })
);

// Approve/Reject withdrawal
router.put('/withdrawals/:withdrawalId/:action',
  body('reason').optional().isString(),
  body('txHash').optional().isString(),
  asyncHandler(async (req, res) => {
    const { withdrawalId, action } = req.params;
    const { reason, txHash } = req.body;
    const adminId = req.user!.id;

    if (!['approve', 'reject'].includes(action)) {
      throw new ValidationError('Invalid action. Must be approve or reject');
    }

    const withdrawal = await prisma.withdrawal.findUnique({
      where: { id: withdrawalId },
      include: {
        user: true,
        currency: true,
        wallet: true
      }
    });

    if (!withdrawal) {
      throw new NotFoundError('Withdrawal not found');
    }

    if (withdrawal.status !== 'PENDING') {
      throw new ValidationError('Withdrawal is not in pending status');
    }

    if (action === 'approve') {
      // Approve withdrawal
      await prisma.withdrawal.update({
        where: { id: withdrawalId },
        data: {
          status: 'APPROVED',
          txHash: txHash || null,
          processedAt: new Date()
        }
      });

      // Deduct from locked balance and actual balance
      const totalAmount = new Decimal(withdrawal.amount).plus(withdrawal.fee);
      await prisma.wallet.update({
        where: { id: withdrawal.walletId },
        data: {
          balance: new Decimal(withdrawal.wallet.balance).minus(totalAmount),
          lockedBalance: new Decimal(withdrawal.wallet.lockedBalance).minus(totalAmount)
        }
      });

      // Send notification
      await notificationService.sendNotification({
        userId: withdrawal.userId,
        type: 'WITHDRAWAL_COMPLETED',
        title: 'Withdrawal Approved',
        message: `Your withdrawal of ${withdrawal.amount} ${withdrawal.currency.symbol} has been approved and processed.`,
        sendEmail: true
      });
    } else {
      // Reject withdrawal
      await prisma.withdrawal.update({
        where: { id: withdrawalId },
        data: {
          status: 'REJECTED',
          rejectionReason: reason,
          processedAt: new Date()
        }
      });

      // Release locked funds
      const totalAmount = new Decimal(withdrawal.amount).plus(withdrawal.fee);
      await prisma.wallet.update({
        where: { id: withdrawal.walletId },
        data: {
          lockedBalance: new Decimal(withdrawal.wallet.lockedBalance).minus(totalAmount)
        }
      });

      // Send notification
      await notificationService.sendNotification({
        userId: withdrawal.userId,
        type: 'SECURITY_ALERT',
        title: 'Withdrawal Rejected',
        message: `Your withdrawal of ${withdrawal.amount} ${withdrawal.currency.symbol} has been rejected. ${reason ? `Reason: ${reason}` : ''}`,
        sendEmail: true
      });
    }

    logger.info(`Withdrawal ${action}d by admin`, {
      withdrawalId,
      adminId,
      userId: withdrawal.userId,
      amount: withdrawal.amount.toString(),
      currency: withdrawal.currency.symbol,
      reason
    });

    res.json({
      success: true,
      message: `Withdrawal ${action}d successfully`
    });
  })
);

// Trading pair management (Super Admin only)
router.post('/trading-pairs',
  superAdminMiddleware,
  body('baseCurrencySymbol').notEmpty(),
  body('quoteCurrencySymbol').notEmpty(),
  body('minTradeAmount').isDecimal(),
  body('maxTradeAmount').optional().isDecimal(),
  body('makerFee').optional().isDecimal(),
  body('takerFee').optional().isDecimal(),
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError(errors.array().map(err => err.msg).join(', '));
    }

    const {
      baseCurrencySymbol,
      quoteCurrencySymbol,
      minTradeAmount,
      maxTradeAmount = 0,
      makerFee = 0.001,
      takerFee = 0.001
    } = req.body;

    // Get currencies
    const [baseCurrency, quoteCurrency] = await Promise.all([
      prisma.currency.findUnique({ where: { symbol: baseCurrencySymbol.toUpperCase() } }),
      prisma.currency.findUnique({ where: { symbol: quoteCurrencySymbol.toUpperCase() } })
    ]);

    if (!baseCurrency || !quoteCurrency) {
      throw new NotFoundError('One or both currencies not found');
    }

    const symbol = `${baseCurrency.symbol}${quoteCurrency.symbol}`;

    // Check if trading pair already exists
    const existingPair = await prisma.tradingPair.findUnique({
      where: { symbol }
    });

    if (existingPair) {
      throw new ValidationError('Trading pair already exists');
    }

    // Create trading pair
    const tradingPair = await prisma.tradingPair.create({
      data: {
        symbol,
        baseCurrencyId: baseCurrency.id,
        quoteCurrencyId: quoteCurrency.id,
        minTradeAmount: new Decimal(minTradeAmount),
        maxTradeAmount: new Decimal(maxTradeAmount),
        makerFee: new Decimal(makerFee),
        takerFee: new Decimal(takerFee)
      },
      include: {
        baseCurrency: true,
        quoteCurrency: true
      }
    });

    logger.info('Trading pair created', {
      symbol,
      adminId: req.user!.id
    });

    res.status(201).json({
      success: true,
      message: 'Trading pair created successfully',
      data: { tradingPair }
    });
  })
);

// System settings (Super Admin only)
router.get('/settings',
  superAdminMiddleware,
  asyncHandler(async (req, res) => {
    // Return system settings
    const settings = {
      fees: {
        defaultMakerFee: '0.001',
        defaultTakerFee: '0.001'
      },
      limits: {
        maxUnverifiedWithdrawal: process.env.MAX_UNVERIFIED_WITHDRAWAL || '1000',
        maxVerifiedWithdrawal: process.env.MAX_VERIFIED_WITHDRAWAL || '50000'
      },
      kyc: {
        required: process.env.KYC_REQUIRED_FOR_WITHDRAWAL === 'true'
      }
    };

    res.json({
      success: true,
      data: { settings }
    });
  })
);

export default router;
