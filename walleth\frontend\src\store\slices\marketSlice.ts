import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { 
  MarketState, 
  TradingPair, 
  Ticker, 
  OrderBook, 
  MarketTrade, 
  CandlestickData,
  ChartInterval 
} from '@/types';
import { marketAPI } from '@/services/api';
import { WebSocketService } from '@/services/websocket';

// Initial state
const initialState: MarketState = {
  tradingPairs: [],
  currentPair: 'BTCUSDT',
  tickers: {},
  orderBooks: {},
  recentTrades: {},
  chartData: {},
  chartInterval: '1m',
  isLoading: false,
  error: null,
};

// Async thunks
export const fetchTradingPairs = createAsyncThunk(
  'market/fetchTradingPairs',
  async (_, { rejectWithValue }) => {
    try {
      const response = await marketAPI.getTradingPairs();
      return response.data.pairs;
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to fetch trading pairs';
      return rejectWithValue(message);
    }
  }
);

export const fetchTickers = createAsyncThunk(
  'market/fetchTickers',
  async (symbol?: string, { rejectWithValue }) => {
    try {
      const response = await marketAPI.getTicker(symbol);
      return { symbol, data: response.data };
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to fetch tickers';
      return rejectWithValue(message);
    }
  }
);

export const fetchOrderBook = createAsyncThunk(
  'market/fetchOrderBook',
  async (symbol: string, { rejectWithValue }) => {
    try {
      const response = await marketAPI.getOrderBook(symbol);
      return { symbol, orderBook: response.data };
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to fetch order book';
      return rejectWithValue(message);
    }
  }
);

export const fetchRecentTrades = createAsyncThunk(
  'market/fetchRecentTrades',
  async (symbol: string, { rejectWithValue }) => {
    try {
      const response = await marketAPI.getRecentTrades(symbol);
      return { symbol, trades: response.data.trades };
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to fetch recent trades';
      return rejectWithValue(message);
    }
  }
);

export const fetchCandlestickData = createAsyncThunk(
  'market/fetchCandlestickData',
  async ({ symbol, interval, limit }: { symbol: string; interval: ChartInterval; limit?: number }, { rejectWithValue }) => {
    try {
      const response = await marketAPI.getCandlestickData(symbol, interval, limit);
      return { symbol, interval, data: response.data.klines };
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to fetch candlestick data';
      return rejectWithValue(message);
    }
  }
);

export const connectWebSocket = createAsyncThunk(
  'market/connectWebSocket',
  async (_, { dispatch, getState }) => {
    try {
      const state = getState() as { market: MarketState };
      const currentPair = state.market.currentPair;
      
      // Initialize WebSocket connection
      WebSocketService.connect();
      
      // Set up event listeners
      WebSocketService.on('connected', () => {
        console.log('WebSocket connected');
        
        // Subscribe to market data
        WebSocketService.subscribe(['ticker:all']);
        WebSocketService.subscribeToOrderbook(currentPair);
        WebSocketService.subscribeToTrades(currentPair);
      });
      
      WebSocketService.on('ticker_update', (data: any) => {
        dispatch(updateTicker(data));
      });
      
      WebSocketService.on('orderbook_update', (data: any) => {
        dispatch(updateOrderBook(data));
      });
      
      WebSocketService.on('trade_update', (data: any) => {
        dispatch(addRecentTrade(data));
      });
      
      return true;
    } catch (error: any) {
      console.error('WebSocket connection failed:', error);
      return false;
    }
  }
);

// Market slice
const marketSlice = createSlice({
  name: 'market',
  initialState,
  reducers: {
    setCurrentPair: (state, action: PayloadAction<string>) => {
      const newPair = action.payload;
      if (state.currentPair !== newPair) {
        state.currentPair = newPair;
        
        // Subscribe to new pair data via WebSocket
        if (WebSocketService.isConnected()) {
          WebSocketService.subscribeToOrderbook(newPair);
          WebSocketService.subscribeToTrades(newPair);
        }
      }
    },
    
    setChartInterval: (state, action: PayloadAction<ChartInterval>) => {
      state.chartInterval = action.payload;
    },
    
    updateTicker: (state, action: PayloadAction<{ tradingPair: string; ticker: Ticker }>) => {
      const { tradingPair, ticker } = action.payload;
      state.tickers[tradingPair] = ticker;
    },
    
    updateOrderBook: (state, action: PayloadAction<{ tradingPair: string; orderbook: OrderBook }>) => {
      const { tradingPair, orderbook } = action.payload;
      state.orderBooks[tradingPair] = orderbook;
    },
    
    addRecentTrade: (state, action: PayloadAction<{ tradingPair: string; trade: MarketTrade }>) => {
      const { tradingPair, trade } = action.payload;
      
      if (!state.recentTrades[tradingPair]) {
        state.recentTrades[tradingPair] = [];
      }
      
      // Add new trade to the beginning and keep only last 50 trades
      state.recentTrades[tradingPair] = [trade, ...state.recentTrades[tradingPair].slice(0, 49)];
    },
    
    updateChartData: (state, action: PayloadAction<{ symbol: string; interval: ChartInterval; data: CandlestickData[] }>) => {
      const { symbol, interval, data } = action.payload;
      const key = `${symbol}_${interval}`;
      state.chartData[key] = data;
    },
    
    clearError: (state) => {
      state.error = null;
    },
    
    resetMarketData: (state) => {
      state.tickers = {};
      state.orderBooks = {};
      state.recentTrades = {};
      state.chartData = {};
    },
  },
  extraReducers: (builder) => {
    // Fetch Trading Pairs
    builder
      .addCase(fetchTradingPairs.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchTradingPairs.fulfilled, (state, action) => {
        state.isLoading = false;
        state.tradingPairs = action.payload;
        state.error = null;
      })
      .addCase(fetchTradingPairs.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch Tickers
    builder
      .addCase(fetchTickers.fulfilled, (state, action) => {
        const { symbol, data } = action.payload;
        
        if (symbol) {
          // Single ticker
          state.tickers[symbol] = data.ticker;
        } else {
          // All tickers
          data.tickers.forEach((ticker: Ticker) => {
            state.tickers[ticker.symbol] = ticker;
          });
        }
      });

    // Fetch Order Book
    builder
      .addCase(fetchOrderBook.fulfilled, (state, action) => {
        const { symbol, orderBook } = action.payload;
        state.orderBooks[symbol] = orderBook;
      });

    // Fetch Recent Trades
    builder
      .addCase(fetchRecentTrades.fulfilled, (state, action) => {
        const { symbol, trades } = action.payload;
        state.recentTrades[symbol] = trades;
      });

    // Fetch Candlestick Data
    builder
      .addCase(fetchCandlestickData.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(fetchCandlestickData.fulfilled, (state, action) => {
        state.isLoading = false;
        const { symbol, interval, data } = action.payload;
        const key = `${symbol}_${interval}`;
        state.chartData[key] = data;
      })
      .addCase(fetchCandlestickData.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  setCurrentPair,
  setChartInterval,
  updateTicker,
  updateOrderBook,
  addRecentTrade,
  updateChartData,
  clearError,
  resetMarketData,
} = marketSlice.actions;

export default marketSlice.reducer;
