import { io, Socket } from 'socket.io-client';

interface WebSocketEventHandlers {
  [event: string]: (data: any) => void;
}

class WebSocketManager {
  private socket: Socket | null = null;
  private eventHandlers: WebSocketEventHandlers = {};
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private isConnecting = false;

  constructor() {
    this.setupEventHandlers();
  }

  connect(): void {
    if (this.socket?.connected || this.isConnecting) {
      return;
    }

    this.isConnecting = true;
    const wsUrl = import.meta.env.VITE_WS_URL || 'ws://localhost:3001';
    const token = localStorage.getItem('accessToken');

    this.socket = io(wsUrl, {
      auth: {
        token,
      },
      transports: ['websocket'],
      upgrade: false,
      rememberUpgrade: false,
      timeout: 20000,
      forceNew: true,
    });

    this.setupSocketEventListeners();
  }

  disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.isConnecting = false;
    this.reconnectAttempts = 0;
  }

  isConnected(): boolean {
    return this.socket?.connected || false;
  }

  on(event: string, handler: (data: any) => void): void {
    this.eventHandlers[event] = handler;
    if (this.socket) {
      this.socket.on(event, handler);
    }
  }

  off(event: string): void {
    delete this.eventHandlers[event];
    if (this.socket) {
      this.socket.off(event);
    }
  }

  emit(event: string, data?: any): void {
    if (this.socket?.connected) {
      this.socket.emit(event, data);
    }
  }

  subscribe(channels: string[]): void {
    this.emit('subscribe', { channels });
  }

  unsubscribe(channels: string[]): void {
    this.emit('unsubscribe', { channels });
  }

  subscribeToOrderbook(tradingPair: string): void {
    this.emit('subscribe_orderbook', { tradingPair });
  }

  subscribeToTrades(tradingPair: string): void {
    this.emit('subscribe_trades', { tradingPair });
  }

  subscribeToTicker(tradingPair?: string): void {
    this.emit('subscribe_ticker', { tradingPair });
  }

  subscribeToUserOrders(): void {
    this.emit('subscribe_user_orders');
  }

  subscribeToUserTrades(): void {
    this.emit('subscribe_user_trades');
  }

  subscribeToUserWallet(): void {
    this.emit('subscribe_user_wallet');
  }

  private setupSocketEventListeners(): void {
    if (!this.socket) return;

    this.socket.on('connect', () => {
      console.log('WebSocket connected');
      this.isConnecting = false;
      this.reconnectAttempts = 0;
      
      // Re-register all event handlers
      Object.entries(this.eventHandlers).forEach(([event, handler]) => {
        this.socket?.on(event, handler);
      });

      // Trigger connected event
      this.triggerEvent('connected', { timestamp: Date.now() });
    });

    this.socket.on('disconnect', (reason) => {
      console.log('WebSocket disconnected:', reason);
      this.isConnecting = false;
      
      // Trigger disconnected event
      this.triggerEvent('disconnected', { reason, timestamp: Date.now() });

      // Attempt to reconnect if not manually disconnected
      if (reason !== 'io client disconnect') {
        this.attemptReconnect();
      }
    });

    this.socket.on('connect_error', (error) => {
      console.error('WebSocket connection error:', error);
      this.isConnecting = false;
      
      // Trigger error event
      this.triggerEvent('error', { error: error.message, timestamp: Date.now() });
      
      this.attemptReconnect();
    });

    this.socket.on('error', (error) => {
      console.error('WebSocket error:', error);
      this.triggerEvent('error', { error, timestamp: Date.now() });
    });

    // Market data events
    this.socket.on('ticker_update', (data) => {
      this.triggerEvent('ticker_update', data);
    });

    this.socket.on('orderbook_update', (data) => {
      this.triggerEvent('orderbook_update', data);
    });

    this.socket.on('trade_update', (data) => {
      this.triggerEvent('trade_update', data);
    });

    // User-specific events
    this.socket.on('order_update', (data) => {
      this.triggerEvent('order_update', data);
    });

    this.socket.on('wallet_update', (data) => {
      this.triggerEvent('wallet_update', data);
    });

    this.socket.on('notification', (data) => {
      this.triggerEvent('notification', data);
    });

    // System events
    this.socket.on('heartbeat', (data) => {
      this.triggerEvent('heartbeat', data);
    });

    this.socket.on('pong', (data) => {
      this.triggerEvent('pong', data);
    });
  }

  private setupEventHandlers(): void {
    // Default event handlers
    this.eventHandlers.connected = () => {
      console.log('WebSocket service connected');
    };

    this.eventHandlers.disconnected = (data) => {
      console.log('WebSocket service disconnected:', data.reason);
    };

    this.eventHandlers.error = (data) => {
      console.error('WebSocket service error:', data.error);
    };
  }

  private triggerEvent(event: string, data: any): void {
    const handler = this.eventHandlers[event];
    if (handler) {
      try {
        handler(data);
      } catch (error) {
        console.error(`Error in WebSocket event handler for ${event}:`, error);
      }
    }
  }

  private attemptReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);

    console.log(`Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

    setTimeout(() => {
      if (!this.socket?.connected) {
        this.connect();
      }
    }, delay);
  }

  // Utility methods
  ping(): void {
    this.emit('ping', { timestamp: Date.now() });
  }

  getConnectionStatus(): {
    connected: boolean;
    reconnectAttempts: number;
    isConnecting: boolean;
  } {
    return {
      connected: this.isConnected(),
      reconnectAttempts: this.reconnectAttempts,
      isConnecting: this.isConnecting,
    };
  }

  // Update authentication token
  updateAuth(token: string): void {
    if (this.socket) {
      this.socket.auth = { token };
      if (this.socket.connected) {
        // Reconnect with new token
        this.disconnect();
        this.connect();
      }
    }
  }
}

// Create singleton instance
export const WebSocketService = new WebSocketManager();

// Export for use in components
export default WebSocketService;
