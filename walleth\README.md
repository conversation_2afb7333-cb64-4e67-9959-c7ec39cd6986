# 🚀 WALLETH - Cryptocurrency Exchange Platform

## 🌟 Overview
WALLETH is a professional-grade cryptocurrency exchange platform built with modern technologies, designed to compete with industry leaders like Binance.

## 🏗️ Architecture

### Frontend
- **Framework**: React 18 + TypeScript + Vite
- **UI Library**: Material-UI + Custom Components
- **Charts**: TradingView Charting Library
- **State Management**: Redux Toolkit + RTK Query
- **Real-time**: Socket.IO Client
- **Styling**: Styled-components + CSS Modules

### Backend
- **Runtime**: Node.js + TypeScript
- **Framework**: Express.js
- **Database**: PostgreSQL + Redis + MongoDB
- **Authentication**: JWT + Passport.js
- **Real-time**: Socket.IO
- **API**: RESTful + GraphQL
- **Queue**: Bull Queue (Redis)

### Infrastructure
- **Containerization**: Docker + Docker Compose
- **Process Manager**: PM2
- **Reverse Proxy**: Nginx
- **SSL**: Let's Encrypt
- **Monitoring**: Prometheus + Grafana

## 🚀 Features

### Core Trading Features
- ✅ Spot Trading
- ✅ Advanced Order Types (Market, Limit, Stop-Loss, Take-Profit)
- ✅ Real-time Order Book
- ✅ Trading Charts with Technical Indicators
- ✅ Portfolio Management
- ✅ Transaction History

### Wallet Management
- ✅ Multi-currency Wallets
- ✅ Deposit/Withdrawal System
- ✅ Cold Storage Integration
- ✅ Address Generation
- ✅ Transaction Monitoring

### Security Features
- ✅ Two-Factor Authentication (2FA)
- ✅ KYC/AML Compliance
- ✅ IP Whitelisting
- ✅ Withdrawal Limits
- ✅ Suspicious Activity Detection

### Admin Panel
- ✅ User Management
- ✅ Trading Pair Management
- ✅ Fee Configuration
- ✅ System Monitoring
- ✅ Analytics Dashboard

### Revenue Model
- ✅ Trading Fees (Maker/Taker)
- ✅ Withdrawal Fees
- ✅ Listing Fees
- ✅ Premium Features

## 📁 Project Structure

```
walleth/
├── frontend/                 # React Frontend Application
│   ├── src/
│   │   ├── components/      # Reusable UI Components
│   │   ├── pages/          # Page Components
│   │   ├── hooks/          # Custom React Hooks
│   │   ├── store/          # Redux Store
│   │   ├── services/       # API Services
│   │   ├── utils/          # Utility Functions
│   │   └── types/          # TypeScript Types
│   ├── public/
│   └── package.json
├── backend/                  # Node.js Backend API
│   ├── src/
│   │   ├── controllers/    # Route Controllers
│   │   ├── models/         # Database Models
│   │   ├── services/       # Business Logic
│   │   ├── middleware/     # Express Middleware
│   │   ├── routes/         # API Routes
│   │   ├── utils/          # Utility Functions
│   │   └── types/          # TypeScript Types
│   └── package.json
├── shared/                   # Shared Types and Utilities
├── docker/                   # Docker Configuration
├── scripts/                  # Deployment and Utility Scripts
└── docs/                     # Documentation
```

## 🛠️ Quick Start

### Prerequisites
- Node.js 18+
- PostgreSQL 14+
- Redis 6+
- MongoDB 5+
- Docker (optional)

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd walleth
```

2. **Install dependencies**
```bash
# Install backend dependencies
cd backend && npm install

# Install frontend dependencies
cd ../frontend && npm install
```

3. **Environment Setup**
```bash
# Copy environment files
cp backend/.env.example backend/.env
cp frontend/.env.example frontend/.env
```

4. **Database Setup**
```bash
# Run database migrations
cd backend && npm run migrate

# Seed initial data
npm run seed
```

5. **Start Development Servers**
```bash
# Start backend (Terminal 1)
cd backend && npm run dev

# Start frontend (Terminal 2)
cd frontend && npm run dev
```

## 🔧 Configuration

### Environment Variables

#### Backend (.env)
```env
NODE_ENV=development
PORT=3001
DATABASE_URL=postgresql://username:password@localhost:5432/walleth
REDIS_URL=redis://localhost:6379
MONGODB_URL=mongodb://localhost:27017/walleth
JWT_SECRET=your-super-secret-jwt-key
ENCRYPTION_KEY=your-encryption-key
```

#### Frontend (.env)
```env
VITE_API_URL=http://localhost:3001/api
VITE_WS_URL=ws://localhost:3001
VITE_APP_NAME=WALLETH
```

## 📊 Trading Pairs

Initial supported trading pairs:
- BTC/USDT
- ETH/USDT
- BNB/USDT
- ADA/USDT
- SOL/USDT
- DOT/USDT

## 💰 Fee Structure

### Trading Fees
- **Maker Fee**: 0.1%
- **Taker Fee**: 0.1%
- **VIP Levels**: Reduced fees based on trading volume

### Withdrawal Fees
- **Bitcoin**: 0.0005 BTC
- **Ethereum**: 0.005 ETH
- **USDT**: 1 USDT

## 🔐 Security

- End-to-end encryption
- Cold storage for 95% of funds
- Multi-signature wallets
- Regular security audits
- DDoS protection
- Rate limiting

## 📈 Roadmap

### Phase 1 (Current)
- [x] Core trading functionality
- [x] Wallet management
- [x] User authentication
- [x] Basic admin panel

### Phase 2
- [ ] Mobile applications
- [ ] Advanced trading features
- [ ] Margin trading
- [ ] Futures trading

### Phase 3
- [ ] DeFi integration
- [ ] NFT marketplace
- [ ] Staking services
- [ ] Lending platform

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📞 Support

For support and questions:
- Email: <EMAIL>
- Discord: [WALLETH Community](https://discord.gg/walleth)
- Telegram: [@walleth_support](https://t.me/walleth_support)

---

**Built with ❤️ by the WALLETH Team**
