import React, { useState, useEffect } from 'react';
import ReactDOM from 'react-dom/client';

// Complete Binance-like Exchange
const CryptoExchange = () => {
  const [prices, setPrices] = useState({});
  const [balance, setBalance] = useState({});
  const [activeTab, setActiveTab] = useState('spot');
  const [selectedPair, setSelectedPair] = useState('BTCUSDT');
  const [orderType, setOrderType] = useState('market');
  const [orderSide, setOrderSide] = useState('buy');

  // Fetch real-time data from backend
  useEffect(() => {
    const fetchData = async () => {
      try {
        const [pricesRes, balanceRes] = await Promise.all([
          fetch('http://localhost:3001/api/prices'),
          fetch('http://localhost:3001/api/balance')
        ]);

        if (pricesRes.ok) setPrices(await pricesRes.json());
        if (balanceRes.ok) setBalance(await balanceRes.json());
      } catch (error) {
        console.log('Backend not available, using mock data');
        setPrices({
          BTC: { price: 45000, change: 2.5 },
          ETH: { price: 2800, change: -1.2 },
          USDT: { price: 1.00, change: 0.0 },
          BNB: { price: 300, change: 3.1 }
        });
        setBalance({
          BTC: 0.00234567,
          ETH: 1.45678901,
          USDT: 1250.00,
          BNB: 5.67890123
        });
      }
    };

    fetchData();
    const interval = setInterval(fetchData, 5000);
    return () => clearInterval(interval);
  }, []);
  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)',
      fontFamily: 'Inter, sans-serif',
      color: 'white'
    }}>
      {/* Header */}
      <header style={{
        background: 'rgba(255, 255, 255, 0.1)',
        backdropFilter: 'blur(20px)',
        borderBottom: '1px solid rgba(255, 255, 255, 0.2)',
        padding: '1rem 2rem',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <h1 style={{ margin: 0, fontSize: '1.8rem', fontWeight: 700 }}>
          🏠 CryptoNest Exchange
        </h1>
        <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
          <span style={{ fontSize: '0.9rem', opacity: 0.8 }}>
            Balance Total: ${Object.entries(balance).reduce((total, [symbol, amount]) =>
              total + (amount * (prices[symbol]?.price || 0)), 0).toLocaleString()}
          </span>
          <div style={{
            background: 'rgba(255, 255, 255, 0.2)',
            padding: '0.5rem 1rem',
            borderRadius: '8px',
            fontSize: '0.9rem'
          }}>
            👤 Usuario Demo
          </div>
        </div>
      </header>

      <div style={{ display: 'flex', height: 'calc(100vh - 80px)' }}>
        {/* Sidebar - Market List */}
        <div style={{
          width: '300px',
          background: 'rgba(255, 255, 255, 0.05)',
          borderRight: '1px solid rgba(255, 255, 255, 0.1)',
          padding: '1rem'
        }}>
          <h3 style={{ margin: '0 0 1rem 0', fontSize: '1.2rem' }}>📊 Mercados</h3>

          {Object.entries(prices).map(([symbol, data]) => (
            <div
              key={symbol}
              onClick={() => setSelectedPair(symbol + 'USDT')}
              style={{
                padding: '0.75rem',
                background: selectedPair === symbol + 'USDT' ? 'rgba(255, 255, 255, 0.1)' : 'transparent',
                borderRadius: '8px',
                cursor: 'pointer',
                marginBottom: '0.5rem',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
              }}
            >
              <div>
                <div style={{ fontWeight: 600 }}>{symbol}/USDT</div>
                <div style={{ fontSize: '0.8rem', opacity: 0.7 }}>
                  ${data.price?.toLocaleString()}
                </div>
              </div>
              <div style={{
                color: data.change >= 0 ? '#4ade80' : '#f87171',
                fontSize: '0.9rem',
                fontWeight: 600
              }}>
                {data.change >= 0 ? '+' : ''}{data.change?.toFixed(2)}%
              </div>
            </div>
          ))}
        </div>

        {/* Main Trading Area */}
        <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
          {/* Price Header */}
          <div style={{
            background: 'rgba(255, 255, 255, 0.05)',
            padding: '1rem 2rem',
            borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
            display: 'flex',
            alignItems: 'center',
            gap: '2rem'
          }}>
            <h2 style={{ margin: 0, fontSize: '1.5rem' }}>{selectedPair}</h2>
            <div style={{ fontSize: '1.8rem', fontWeight: 700 }}>
              ${prices[selectedPair.replace('USDT', '')]?.price?.toLocaleString()}
            </div>
            <div style={{
              color: prices[selectedPair.replace('USDT', '')]?.change >= 0 ? '#4ade80' : '#f87171',
              fontSize: '1.2rem',
              fontWeight: 600
            }}>
              {prices[selectedPair.replace('USDT', '')]?.change >= 0 ? '+' : ''}
              {prices[selectedPair.replace('USDT', '')]?.change?.toFixed(2)}%
            </div>
          </div>

          {/* Chart Area */}
          <div style={{
            flex: 1,
            background: 'rgba(255, 255, 255, 0.02)',
            margin: '1rem',
            borderRadius: '12px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            border: '1px solid rgba(255, 255, 255, 0.1)'
          }}>
            <div style={{ textAlign: 'center', opacity: 0.7 }}>
              <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>📈</div>
              <h3>Gráfico de Trading</h3>
              <p>Aquí iría el gráfico de TradingView</p>
              <p style={{ fontSize: '0.9rem' }}>
                Precio actual: ${prices[selectedPair.replace('USDT', '')]?.price?.toLocaleString()}
              </p>
            </div>
          </div>
        </div>

        {/* Trading Panel */}
        <div style={{
          width: '350px',
          background: 'rgba(255, 255, 255, 0.05)',
          borderLeft: '1px solid rgba(255, 255, 255, 0.1)',
          padding: '1rem'
        }}>
          {/* Order Form */}
          <div style={{
            background: 'rgba(255, 255, 255, 0.1)',
            borderRadius: '12px',
            padding: '1.5rem',
            marginBottom: '1rem'
          }}>
            <h3 style={{ margin: '0 0 1rem 0' }}>💰 Crear Orden</h3>

            {/* Buy/Sell Tabs */}
            <div style={{ display: 'flex', marginBottom: '1rem' }}>
              {['buy', 'sell'].map(side => (
                <button
                  key={side}
                  onClick={() => setOrderSide(side)}
                  style={{
                    flex: 1,
                    padding: '0.75rem',
                    background: orderSide === side ?
                      (side === 'buy' ? '#4ade80' : '#f87171') :
                      'rgba(255, 255, 255, 0.1)',
                    border: 'none',
                    color: 'white',
                    fontWeight: 600,
                    cursor: 'pointer',
                    borderRadius: side === 'buy' ? '8px 0 0 8px' : '0 8px 8px 0'
                  }}
                >
                  {side === 'buy' ? '🟢 Comprar' : '🔴 Vender'}
                </button>
              ))}
            </div>

            {/* Order Type */}
            <div style={{ marginBottom: '1rem' }}>
              <label style={{ display: 'block', marginBottom: '0.5rem', fontSize: '0.9rem' }}>
                Tipo de Orden:
              </label>
              <select
                value={orderType}
                onChange={(e) => setOrderType(e.target.value)}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  background: 'rgba(255, 255, 255, 0.1)',
                  border: '1px solid rgba(255, 255, 255, 0.2)',
                  borderRadius: '8px',
                  color: 'white'
                }}
              >
                <option value="market">Mercado</option>
                <option value="limit">Límite</option>
              </select>
            </div>

            {/* Price Input (for limit orders) */}
            {orderType === 'limit' && (
              <div style={{ marginBottom: '1rem' }}>
                <label style={{ display: 'block', marginBottom: '0.5rem', fontSize: '0.9rem' }}>
                  Precio:
                </label>
                <input
                  type="number"
                  placeholder={prices[selectedPair.replace('USDT', '')]?.price?.toString()}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    background: 'rgba(255, 255, 255, 0.1)',
                    border: '1px solid rgba(255, 255, 255, 0.2)',
                    borderRadius: '8px',
                    color: 'white'
                  }}
                />
              </div>
            )}

            {/* Quantity Input */}
            <div style={{ marginBottom: '1rem' }}>
              <label style={{ display: 'block', marginBottom: '0.5rem', fontSize: '0.9rem' }}>
                Cantidad:
              </label>
              <input
                type="number"
                placeholder="0.001"
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  background: 'rgba(255, 255, 255, 0.1)',
                  border: '1px solid rgba(255, 255, 255, 0.2)',
                  borderRadius: '8px',
                  color: 'white'
                }}
              />
            </div>

            {/* Place Order Button */}
            <button style={{
              width: '100%',
              padding: '1rem',
              background: orderSide === 'buy' ?
                'linear-gradient(45deg, #4ade80, #22c55e)' :
                'linear-gradient(45deg, #f87171, #ef4444)',
              border: 'none',
              borderRadius: '8px',
              color: 'white',
              fontSize: '1.1rem',
              fontWeight: 600,
              cursor: 'pointer'
            }}>
              {orderSide === 'buy' ? '🟢 Comprar' : '🔴 Vender'} {selectedPair.replace('USDT', '')}
            </button>
          </div>

          {/* Balance */}
          <div style={{
            background: 'rgba(255, 255, 255, 0.1)',
            borderRadius: '12px',
            padding: '1.5rem'
          }}>
            <h3 style={{ margin: '0 0 1rem 0' }}>💼 Balance</h3>
            {Object.entries(balance).map(([symbol, amount]) => (
              <div key={symbol} style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                padding: '0.5rem 0',
                borderBottom: '1px solid rgba(255, 255, 255, 0.1)'
              }}>
                <span style={{ fontWeight: 600 }}>{symbol}</span>
                <div style={{ textAlign: 'right' }}>
                  <div>{amount.toLocaleString()}</div>
                  <div style={{ fontSize: '0.8rem', opacity: 0.7 }}>
                    ≈ ${(amount * (prices[symbol]?.price || 0)).toLocaleString()}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <CryptoExchange />
  </React.StrictMode>
);
