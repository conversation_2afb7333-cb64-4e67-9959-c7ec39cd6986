import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App-simple';



// Toast configuration
const toastOptions = {
  duration: 4000,
  style: {
    background: '#1a1a2e',
    color: '#ffffff',
    border: '1px solid rgba(255, 255, 255, 0.12)',
  },
  success: {
    iconTheme: {
      primary: '#4caf50',
      secondary: '#ffffff',
    },
  },
  error: {
    iconTheme: {
      primary: '#f44336',
      secondary: '#ffffff',
    },
  },
};

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <Provider store={store}>
      <BrowserRouter>
        <ThemeProvider theme={cryptoNestTheme}>
          <CssBaseline />
          <App />
          <Toaster 
            position="top-right"
            toastOptions={toastOptions}
          />
        </ThemeProvider>
      </BrowserRouter>
    </Provider>
  </React.StrictMode>
);

// Performance monitoring
if (import.meta.env.DEV) {
  // Development mode performance monitoring
  const observer = new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      if (entry.entryType === 'measure') {
        console.log(`${entry.name}: ${entry.duration}ms`);
      }
    }
  });
  observer.observe({ entryTypes: ['measure'] });
}

// Error boundary for unhandled errors
window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason);
  // You can send this to your error tracking service
});

window.addEventListener('error', (event) => {
  console.error('Unhandled error:', event.error);
  // You can send this to your error tracking service
});
