import React from 'react';
import ReactDOM from 'react-dom/client';

// Simple Walleth App
const Walleth = () => {
  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)',
      fontFamily: 'Inter, sans-serif',
      color: 'white',
      padding: '2rem'
    }}>
      <div style={{
        maxWidth: '1200px',
        margin: '0 auto'
      }}>
        {/* Header */}
        <header style={{
          textAlign: 'center',
          marginBottom: '3rem'
        }}>
          <h1 style={{
            fontSize: '3rem',
            fontWeight: 700,
            margin: 0,
            marginBottom: '0.5rem'
          }}>
            💳 Walleth
          </h1>
          <p style={{
            fontSize: '1.2rem',
            opacity: 0.9,
            margin: 0
          }}>
            Tu Wallet de Criptomonedas Personal
          </p>
        </header>

        {/* Balance Cards */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
          gap: '1.5rem',
          marginBottom: '3rem'
        }}>
          {[
            { coin: 'Bitcoin', symbol: 'BTC', amount: '0.00234567', value: '$105.43', icon: '₿' },
            { coin: 'Ethereum', symbol: 'ETH', amount: '1.45678901', value: '$4,078.61', icon: 'Ξ' },
            { coin: 'Tether', symbol: 'USDT', amount: '1,250.00', value: '$1,250.00', icon: '$' },
            { coin: 'Binance Coin', symbol: 'BNB', amount: '5.67890123', value: '$1,703.67', icon: '🔸' }
          ].map((crypto, index) => (
            <div key={index} style={{
              background: 'rgba(255, 255, 255, 0.15)',
              backdropFilter: 'blur(20px)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              borderRadius: '16px',
              padding: '1.5rem',
              transition: 'transform 0.3s ease'
            }}>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                marginBottom: '1rem'
              }}>
                <h3 style={{ margin: 0, fontSize: '1.1rem' }}>{crypto.coin}</h3>
                <span style={{ fontSize: '1.5rem' }}>{crypto.icon}</span>
              </div>
              <p style={{
                margin: 0,
                fontSize: '1.5rem',
                fontWeight: 600,
                marginBottom: '0.5rem'
              }}>
                {crypto.amount} {crypto.symbol}
              </p>
              <p style={{
                margin: 0,
                fontSize: '1rem',
                opacity: 0.8
              }}>
                {crypto.value}
              </p>
            </div>
          ))}
        </div>

        {/* Action Buttons */}
        <div style={{
          display: 'flex',
          gap: '1rem',
          justifyContent: 'center',
          flexWrap: 'wrap',
          marginBottom: '3rem'
        }}>
          {[
            { label: '📤 Enviar', color: '#667eea' },
            { label: '📥 Recibir', color: '#764ba2' },
            { label: '🔄 Intercambiar', color: '#f093fb' },
            { label: '📊 Historial', color: '#667eea' }
          ].map((action, index) => (
            <button key={index} style={{
              background: `linear-gradient(45deg, ${action.color}, #764ba2)`,
              border: 'none',
              borderRadius: '12px',
              padding: '1rem 2rem',
              color: 'white',
              fontSize: '1rem',
              fontWeight: 600,
              cursor: 'pointer',
              transition: 'transform 0.3s ease',
              minWidth: '150px'
            }}>
              {action.label}
            </button>
          ))}
        </div>

        {/* Recent Transactions */}
        <div style={{
          background: 'rgba(255, 255, 255, 0.1)',
          backdropFilter: 'blur(20px)',
          border: '1px solid rgba(255, 255, 255, 0.2)',
          borderRadius: '16px',
          padding: '2rem'
        }}>
          <h2 style={{
            margin: 0,
            marginBottom: '1.5rem',
            fontSize: '1.5rem'
          }}>
            📜 Transacciones Recientes
          </h2>

          {[
            { type: 'Recibido', amount: '+0.001 BTC', from: '1A1zP1...', time: '2 min ago', status: 'Confirmado' },
            { type: 'Enviado', amount: '-0.5 ETH', to: '0x742d...', time: '1 hora ago', status: 'Confirmado' },
            { type: 'Recibido', amount: '+100 USDT', from: '0x8ba1...', time: '3 horas ago', status: 'Confirmado' }
          ].map((tx, index) => (
            <div key={index} style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              padding: '1rem',
              background: 'rgba(255, 255, 255, 0.05)',
              borderRadius: '8px',
              marginBottom: index < 2 ? '1rem' : 0
            }}>
              <div>
                <h4 style={{ margin: 0, marginBottom: '0.25rem' }}>{tx.type}</h4>
                <p style={{ margin: 0, fontSize: '0.9rem', opacity: 0.7 }}>
                  {tx.from ? `De: ${tx.from}` : `Para: ${tx.to}`}
                </p>
                <p style={{ margin: 0, fontSize: '0.8rem', opacity: 0.6 }}>{tx.time}</p>
              </div>
              <div style={{ textAlign: 'right' }}>
                <p style={{
                  margin: 0,
                  fontSize: '1.1rem',
                  fontWeight: 600,
                  color: tx.amount.startsWith('+') ? '#4ade80' : '#f87171'
                }}>
                  {tx.amount}
                </p>
                <p style={{ margin: 0, fontSize: '0.8rem', opacity: 0.7 }}>{tx.status}</p>
              </div>
            </div>
          ))}
        </div>

        {/* Footer */}
        <footer style={{
          textAlign: 'center',
          marginTop: '3rem',
          opacity: 0.7
        }}>
          <p style={{ margin: 0 }}>
            🔒 Walleth - Seguro, Rápido, Confiable
          </p>
        </footer>
      </div>
    </div>
  );
};

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <Walleth />
  </React.StrictMode>
);
