import React, { useState, useEffect } from 'react';
import ReactDOM from 'react-dom/client';

// Real Crypto Wallet
const RealWallet = () => {
  const [wallet, setWallet] = useState(null);
  const [balances, setBalances] = useState({});
  const [activeTab, setActiveTab] = useState('create');
  const [loading, setLoading] = useState(false);
  const [addresses, setAddresses] = useState({});
  const [transactions, setTransactions] = useState([]);

  // Create new wallet
  const createWallet = async () => {
    setLoading(true);
    try {
      const response = await fetch('http://localhost:3001/api/wallet/create', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      if (response.ok) {
        const walletData = await response.json();
        setWallet(walletData);
        setAddresses(walletData.addresses);
        setActiveTab('wallet');
        alert(`¡Wallet creada! Guarda tu frase mnemónica: ${walletData.mnemonic}`);
      }
    } catch (error) {
      alert('Error creando wallet: ' + error.message);
    }
    setLoading(false);
  };

  // Load wallet balances
  const loadBalances = async () => {
    if (!wallet) return;

    try {
      const response = await fetch(`http://localhost:3001/api/wallet/${wallet.walletId}/balance`);
      if (response.ok) {
        const data = await response.json();
        setBalances(data.balances);
      }
    } catch (error) {
      console.error('Error loading balances:', error);
    }
  };

  // Load wallet data on mount
  useEffect(() => {
    if (wallet) {
      loadBalances();
      const interval = setInterval(loadBalances, 10000); // Update every 10 seconds
      return () => clearInterval(interval);
    }
  }, [wallet]);
  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)',
      fontFamily: 'Inter, sans-serif',
      color: 'white'
    }}>
      {/* Header */}
      <header style={{
        background: 'rgba(255, 255, 255, 0.1)',
        backdropFilter: 'blur(20px)',
        borderBottom: '1px solid rgba(255, 255, 255, 0.2)',
        padding: '1rem 2rem',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <h1 style={{ margin: 0, fontSize: '1.8rem', fontWeight: 700 }}>
          💳 Walleth - Real Crypto Wallet
        </h1>
        <div style={{ display: 'flex', gap: '1rem', alignItems: 'center' }}>
          {wallet && (
            <span style={{ fontSize: '0.9rem', opacity: 0.8 }}>
              Balance Total: ${Object.entries(balances).reduce((total, [symbol, amount]) =>
                total + (amount * (symbol === 'BTC' ? 45000 : 2800)), 0).toFixed(2)}
            </span>
          )}
          <div style={{
            background: 'rgba(255, 255, 255, 0.2)',
            padding: '0.5rem 1rem',
            borderRadius: '8px',
            fontSize: '0.9rem'
          }}>
            {wallet ? '✅ Wallet Activa' : '❌ Sin Wallet'}
          </div>
        </div>
      </header>

      {/* Navigation */}
      <div style={{
        display: 'flex',
        padding: '1rem 2rem',
        gap: '1rem'
      }}>
        {['create', 'wallet', 'send', 'receive', 'history'].map(tab => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab)}
            disabled={tab !== 'create' && !wallet}
            style={{
              padding: '0.75rem 1.5rem',
              background: activeTab === tab ? 'rgba(255, 255, 255, 0.2)' : 'rgba(255, 255, 255, 0.1)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              borderRadius: '12px',
              color: (tab !== 'create' && !wallet) ? 'rgba(255, 255, 255, 0.5)' : 'white',
              cursor: (tab !== 'create' && !wallet) ? 'not-allowed' : 'pointer',
              textTransform: 'capitalize',
              fontWeight: activeTab === tab ? 600 : 400,
              backdropFilter: 'blur(10px)'
            }}
          >
            {tab === 'create' ? '🆕 Crear' :
             tab === 'wallet' ? '💼 Wallet' :
             tab === 'send' ? '📤 Enviar' :
             tab === 'receive' ? '📥 Recibir' : '📜 Historial'}
          </button>
        ))}
      </div>

      {/* Content */}
      <div style={{ padding: '0 2rem 2rem' }}>

        {/* Create Wallet Tab */}
        {activeTab === 'create' && !wallet && (
          <div style={{
            background: 'rgba(255, 255, 255, 0.1)',
            padding: '3rem',
            borderRadius: '16px',
            backdropFilter: 'blur(20px)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            maxWidth: '600px',
            margin: '0 auto',
            textAlign: 'center'
          }}>
            <h2 style={{ marginBottom: '2rem', fontSize: '2rem' }}>🚀 Crear Tu Wallet Real</h2>
            <p style={{ marginBottom: '2rem', fontSize: '1.1rem', opacity: 0.9 }}>
              Crea una wallet real de Bitcoin y Ethereum conectada a las redes testnet.
              Podrás recibir y enviar criptomonedas reales de prueba.
            </p>
            <div style={{ marginBottom: '2rem', padding: '1rem', background: 'rgba(255, 255, 255, 0.1)', borderRadius: '8px' }}>
              <h4>🔐 Características de seguridad:</h4>
              <ul style={{ textAlign: 'left', margin: '1rem 0' }}>
                <li>✅ Claves privadas reales generadas localmente</li>
                <li>✅ Frase mnemónica de 12 palabras</li>
                <li>✅ Direcciones Bitcoin y Ethereum reales</li>
                <li>✅ Conexión a redes testnet (Bitcoin Testnet, Ethereum Sepolia)</li>
              </ul>
            </div>
            <button
              onClick={createWallet}
              disabled={loading}
              style={{
                padding: '1.5rem 3rem',
                background: loading ? 'rgba(255, 255, 255, 0.3)' : 'linear-gradient(45deg, #667eea, #764ba2)',
                border: 'none',
                borderRadius: '12px',
                color: 'white',
                fontSize: '1.2rem',
                fontWeight: 600,
                cursor: loading ? 'not-allowed' : 'pointer',
                minWidth: '200px'
              }}
            >
              {loading ? '⏳ Creando...' : '🔥 Crear Wallet Real'}
            </button>
          </div>
        )}

        {/* Wallet Tab */}
        {activeTab === 'wallet' && wallet && (
          <div>
            <h2 style={{ marginBottom: '1.5rem' }}>💰 Mis Balances Reales</h2>
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '1.5rem' }}>
              {Object.entries(balances).map(([coin, amount]) => (
                <div key={coin} style={{
                  background: 'rgba(255, 255, 255, 0.1)',
                  padding: '2rem',
                  borderRadius: '16px',
                  backdropFilter: 'blur(20px)',
                  border: '1px solid rgba(255, 255, 255, 0.2)'
                }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
                    <h3 style={{ margin: 0, fontSize: '1.3rem' }}>{coin === 'BTC' ? 'Bitcoin' : 'Ethereum'}</h3>
                    <span style={{ fontSize: '2rem' }}>{coin === 'BTC' ? '₿' : 'Ξ'}</span>
                  </div>
                  <p style={{ margin: '0.5rem 0 0', fontSize: '1.8rem', fontWeight: 600 }}>
                    {amount} {coin}
                  </p>
                  <p style={{ margin: '0.5rem 0 0', fontSize: '1rem', opacity: 0.7 }}>
                    Red: {coin === 'BTC' ? 'Bitcoin Testnet' : 'Ethereum Sepolia'}
                  </p>
                  <p style={{ margin: '0.5rem 0 0', fontSize: '0.9rem', opacity: 0.6 }}>
                    Dirección: {addresses[coin === 'BTC' ? 'bitcoin' : 'ethereum']?.substring(0, 20)}...
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Send Tab */}
        {activeTab === 'send' && wallet && (
          <div style={{
            background: 'rgba(255, 255, 255, 0.1)',
            padding: '2rem',
            borderRadius: '16px',
            backdropFilter: 'blur(20px)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            maxWidth: '500px'
          }}>
            <h2 style={{ marginBottom: '1.5rem' }}>📤 Enviar Criptomonedas</h2>
            <div style={{ marginBottom: '1rem' }}>
              <label style={{ display: 'block', marginBottom: '0.5rem' }}>Moneda:</label>
              <select style={{
                width: '100%',
                padding: '0.75rem',
                borderRadius: '8px',
                border: '1px solid rgba(255, 255, 255, 0.3)',
                background: 'rgba(255, 255, 255, 0.1)',
                color: 'white'
              }}>
                <option>Bitcoin (BTC) - Testnet</option>
                <option>Ethereum (ETH) - Sepolia</option>
              </select>
            </div>
            <div style={{ marginBottom: '1rem' }}>
              <label style={{ display: 'block', marginBottom: '0.5rem' }}>Dirección destino:</label>
              <input
                type="text"
                placeholder="tb1q... (Bitcoin) o 0x... (Ethereum)"
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  borderRadius: '8px',
                  border: '1px solid rgba(255, 255, 255, 0.3)',
                  background: 'rgba(255, 255, 255, 0.1)',
                  color: 'white'
                }}
              />
            </div>
            <div style={{ marginBottom: '1.5rem' }}>
              <label style={{ display: 'block', marginBottom: '0.5rem' }}>Cantidad:</label>
              <input
                type="number"
                placeholder="0.001"
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  borderRadius: '8px',
                  border: '1px solid rgba(255, 255, 255, 0.3)',
                  background: 'rgba(255, 255, 255, 0.1)',
                  color: 'white'
                }}
              />
            </div>
            <button style={{
              width: '100%',
              padding: '1rem',
              background: 'linear-gradient(45deg, #667eea, #764ba2)',
              border: 'none',
              borderRadius: '12px',
              color: 'white',
              fontSize: '1.1rem',
              fontWeight: 600,
              cursor: 'pointer'
            }}>
              🚀 Enviar Transacción Real
            </button>
          </div>
        )}

        {/* Receive Tab */}
        {activeTab === 'receive' && wallet && (
          <div style={{
            background: 'rgba(255, 255, 255, 0.1)',
            padding: '2rem',
            borderRadius: '16px',
            backdropFilter: 'blur(20px)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            maxWidth: '600px'
          }}>
            <h2 style={{ marginBottom: '1.5rem' }}>📥 Recibir Criptomonedas</h2>

            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '2rem' }}>
              {/* Bitcoin */}
              <div style={{ textAlign: 'center' }}>
                <h3>₿ Bitcoin (Testnet)</h3>
                <div style={{
                  background: 'white',
                  padding: '1rem',
                  borderRadius: '12px',
                  marginBottom: '1rem'
                }}>
                  <div style={{
                    width: '120px',
                    height: '120px',
                    background: '#000',
                    margin: '0 auto',
                    borderRadius: '8px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                    fontSize: '0.8rem'
                  }}>
                    QR CODE
                  </div>
                </div>
                <div style={{
                  background: 'rgba(255, 255, 255, 0.1)',
                  padding: '1rem',
                  borderRadius: '8px',
                  fontSize: '0.8rem',
                  wordBreak: 'break-all',
                  marginBottom: '1rem'
                }}>
                  {addresses.bitcoin}
                </div>
                <button style={{
                  padding: '0.5rem 1rem',
                  background: 'linear-gradient(45deg, #667eea, #764ba2)',
                  border: 'none',
                  borderRadius: '8px',
                  color: 'white',
                  cursor: 'pointer'
                }}>
                  📋 Copiar
                </button>
              </div>

              {/* Ethereum */}
              <div style={{ textAlign: 'center' }}>
                <h3>Ξ Ethereum (Sepolia)</h3>
                <div style={{
                  background: 'white',
                  padding: '1rem',
                  borderRadius: '12px',
                  marginBottom: '1rem'
                }}>
                  <div style={{
                    width: '120px',
                    height: '120px',
                    background: '#000',
                    margin: '0 auto',
                    borderRadius: '8px',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                    fontSize: '0.8rem'
                  }}>
                    QR CODE
                  </div>
                </div>
                <div style={{
                  background: 'rgba(255, 255, 255, 0.1)',
                  padding: '1rem',
                  borderRadius: '8px',
                  fontSize: '0.8rem',
                  wordBreak: 'break-all',
                  marginBottom: '1rem'
                }}>
                  {addresses.ethereum}
                </div>
                <button style={{
                  padding: '0.5rem 1rem',
                  background: 'linear-gradient(45deg, #667eea, #764ba2)',
                  border: 'none',
                  borderRadius: '8px',
                  color: 'white',
                  cursor: 'pointer'
                }}>
                  📋 Copiar
                </button>
              </div>
            </div>
          </div>
        )}

        {/* History Tab */}
        {activeTab === 'history' && wallet && (
          <div>
            <h2 style={{ marginBottom: '1.5rem' }}>📜 Historial de Transacciones Reales</h2>
            <div style={{
              background: 'rgba(255, 255, 255, 0.1)',
              padding: '2rem',
              borderRadius: '16px',
              backdropFilter: 'blur(20px)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              textAlign: 'center'
            }}>
              <div style={{ fontSize: '3rem', marginBottom: '1rem' }}>📊</div>
              <h3>Historial de Transacciones</h3>
              <p style={{ opacity: 0.8 }}>
                Aquí aparecerán tus transacciones reales de Bitcoin y Ethereum
              </p>
              <p style={{ fontSize: '0.9rem', opacity: 0.6 }}>
                Las transacciones se verifican en blockchain real
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <RealWallet />
  </React.StrictMode>
);
