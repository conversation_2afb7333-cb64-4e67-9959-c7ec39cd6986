import { createTheme, ThemeOptions } from '@mui/material/styles';
import { colors } from './colors';

// Extend the Material-UI theme to include our custom colors
declare module '@mui/material/styles' {
  interface Palette {
    tertiary: Palette['primary'];
    trading: {
      buy: string;
      sell: string;
      buyGradient: string;
      sellGradient: string;
      neutral: string;
    };
    gradients: {
      primary: string;
      secondary: string;
      tertiary: string;
      background: string;
      card: string;
      button: string;
      buttonHover: string;
      text: string;
    };
  }

  interface PaletteOptions {
    tertiary?: PaletteOptions['primary'];
    trading?: {
      buy?: string;
      sell?: string;
      buyGradient?: string;
      sellGradient?: string;
      neutral?: string;
    };
    gradients?: {
      primary?: string;
      secondary?: string;
      tertiary?: string;
      background?: string;
      card?: string;
      button?: string;
      buttonHover?: string;
      text?: string;
    };
  }
}

const themeOptions: ThemeOptions = {
  palette: {
    mode: 'dark',
    primary: {
      main: colors.primary.main,
      light: colors.primary.light,
      dark: colors.primary.dark,
      contrastText: '#ffffff',
    },
    secondary: {
      main: colors.secondary.main,
      light: colors.secondary.light,
      dark: colors.secondary.dark,
      contrastText: '#ffffff',
    },
    tertiary: {
      main: colors.tertiary.main,
      light: colors.tertiary.light,
      dark: colors.tertiary.dark,
      contrastText: '#ffffff',
    },
    background: {
      default: colors.background.default,
      paper: colors.background.paper,
    },
    text: {
      primary: colors.text.primary,
      secondary: colors.text.secondary,
      disabled: colors.text.disabled,
    },
    divider: colors.divider,
    success: {
      main: colors.success.main,
      light: colors.success.light,
      dark: colors.success.dark,
      contrastText: '#ffffff',
    },
    error: {
      main: colors.error.main,
      light: colors.error.light,
      dark: colors.error.dark,
      contrastText: '#ffffff',
    },
    warning: {
      main: colors.warning.main,
      light: colors.warning.light,
      dark: colors.warning.dark,
      contrastText: '#ffffff',
    },
    info: {
      main: colors.info.main,
      light: colors.info.light,
      dark: colors.info.dark,
      contrastText: '#ffffff',
    },
    trading: {
      buy: colors.trading.buy,
      sell: colors.trading.sell,
      buyGradient: colors.trading.buyGradient,
      sellGradient: colors.trading.sellGradient,
      neutral: colors.trading.neutral,
    },
    gradients: {
      primary: colors.gradients.primary,
      secondary: colors.gradients.secondary,
      tertiary: colors.gradients.tertiary,
      background: colors.gradients.background,
      card: colors.gradients.card,
      button: colors.gradients.button,
      buttonHover: colors.gradients.buttonHover,
      text: colors.gradients.text,
    },
  },
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontSize: '2.5rem',
      fontWeight: 700,
      letterSpacing: '-0.01562em',
    },
    h2: {
      fontSize: '2rem',
      fontWeight: 600,
      letterSpacing: '-0.00833em',
    },
    h3: {
      fontSize: '1.75rem',
      fontWeight: 600,
      letterSpacing: '0em',
    },
    h4: {
      fontSize: '1.5rem',
      fontWeight: 600,
      letterSpacing: '0.00735em',
    },
    h5: {
      fontSize: '1.25rem',
      fontWeight: 600,
      letterSpacing: '0em',
    },
    h6: {
      fontSize: '1rem',
      fontWeight: 600,
      letterSpacing: '0.0075em',
    },
    body1: {
      fontSize: '1rem',
      lineHeight: 1.5,
      letterSpacing: '0.00938em',
    },
    body2: {
      fontSize: '0.875rem',
      lineHeight: 1.43,
      letterSpacing: '0.01071em',
    },
    button: {
      textTransform: 'none',
      fontWeight: 500,
      letterSpacing: '0.02857em',
    },
    caption: {
      fontSize: '0.75rem',
      lineHeight: 1.66,
      letterSpacing: '0.03333em',
    },
    overline: {
      fontSize: '0.75rem',
      fontWeight: 400,
      letterSpacing: '0.08333em',
      textTransform: 'uppercase',
    },
  },
  shape: {
    borderRadius: 12,
  },
  spacing: 8,
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          padding: '10px 24px',
          fontSize: '0.875rem',
          fontWeight: 500,
          textTransform: 'none',
          boxShadow: 'none',
          '&:hover': {
            boxShadow: '0 4px 12px rgba(102, 126, 234, 0.3)',
          },
        },
        contained: {
          background: colors.gradients.button,
          '&:hover': {
            background: colors.gradients.buttonHover,
          },
        },
        outlined: {
          borderColor: colors.primary.main,
          color: colors.primary.main,
          '&:hover': {
            borderColor: colors.primary.light,
            backgroundColor: `${colors.primary.main}15`,
          },
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          backgroundColor: colors.background.card,
          border: `1px solid ${colors.border}`,
          borderRadius: 16,
          backdropFilter: 'blur(20px)',
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)',
        },
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          backgroundColor: colors.background.paper,
          border: `1px solid ${colors.border}`,
          backdropFilter: 'blur(20px)',
        },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            backgroundColor: 'rgba(255, 255, 255, 0.05)',
            borderRadius: 12,
            '& fieldset': {
              borderColor: colors.border,
            },
            '&:hover fieldset': {
              borderColor: colors.primary.light,
            },
            '&.Mui-focused fieldset': {
              borderColor: colors.primary.main,
              borderWidth: 2,
            },
          },
        },
      },
    },
    MuiTableCell: {
      styleOverrides: {
        root: {
          borderBottom: `1px solid ${colors.divider}`,
        },
        head: {
          backgroundColor: 'rgba(255, 255, 255, 0.05)',
          fontWeight: 600,
          fontSize: '0.875rem',
        },
      },
    },
    MuiChip: {
      styleOverrides: {
        root: {
          backgroundColor: 'rgba(255, 255, 255, 0.1)',
          color: colors.text.primary,
          borderRadius: 8,
        },
        colorPrimary: {
          background: colors.gradients.primary,
          color: '#ffffff',
        },
        colorSecondary: {
          background: colors.gradients.secondary,
          color: '#ffffff',
        },
      },
    },
    MuiDrawer: {
      styleOverrides: {
        paper: {
          backgroundColor: colors.background.paper,
          borderRight: `1px solid ${colors.divider}`,
          backdropFilter: 'blur(20px)',
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          backgroundColor: colors.background.paper,
          borderBottom: `1px solid ${colors.divider}`,
          boxShadow: 'none',
          backdropFilter: 'blur(20px)',
        },
      },
    },
    MuiDialog: {
      styleOverrides: {
        paper: {
          backgroundColor: colors.background.paper,
          borderRadius: 16,
          border: `1px solid ${colors.border}`,
          backdropFilter: 'blur(20px)',
        },
      },
    },
    MuiMenu: {
      styleOverrides: {
        paper: {
          backgroundColor: colors.background.paper,
          border: `1px solid ${colors.border}`,
          borderRadius: 12,
          backdropFilter: 'blur(20px)',
        },
      },
    },
  },
};

export const cryptoNestTheme = createTheme(themeOptions);
export default cryptoNestTheme;
