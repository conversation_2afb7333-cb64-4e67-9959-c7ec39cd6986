// User Types
export interface User {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  dateOfBirth?: string;
  country?: string;
  role: 'USER' | 'ADMIN' | 'SUPER_ADMIN';
  kycStatus: 'PENDING' | 'SUBMITTED' | 'APPROVED' | 'REJECTED' | 'EXPIRED';
  kycLevel: number;
  isEmailVerified: boolean;
  isPhoneVerified: boolean;
  is2FAEnabled: boolean;
  referralCode?: string;
  createdAt: string;
  updatedAt: string;
}

// Authentication Types
export interface AuthState {
  user: User | null;
  accessToken: string | null;
  refreshToken: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface LoginCredentials {
  email: string;
  password: string;
  twoFactorCode?: string;
}

export interface RegisterData {
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
  referralCode?: string;
}

// Currency Types
export interface Currency {
  id: string;
  symbol: string;
  name: string;
  decimals: number;
  isFiat: boolean;
  network?: string;
  iconUrl?: string;
  minDeposit: string;
  minWithdrawal: string;
  withdrawalFee: string;
  confirmations: number;
}

// Trading Pair Types
export interface TradingPair {
  symbol: string;
  baseCurrency: Currency;
  quoteCurrency: Currency;
  minTradeAmount: string;
  maxTradeAmount: string;
  priceDecimals: number;
  amountDecimals: number;
  makerFee: string;
  takerFee: string;
  isActive: boolean;
}

// Order Types
export type OrderType = 'MARKET' | 'LIMIT' | 'STOP_LOSS' | 'TAKE_PROFIT' | 'STOP_LIMIT';
export type OrderSide = 'BUY' | 'SELL';
export type OrderStatus = 'PENDING' | 'PARTIAL_FILLED' | 'FILLED' | 'CANCELLED' | 'REJECTED' | 'EXPIRED';
export type TimeInForce = 'GTC' | 'IOC' | 'FOK';

export interface Order {
  id: string;
  tradingPair: string;
  baseCurrency: string;
  quoteCurrency: string;
  type: OrderType;
  side: OrderSide;
  amount: string;
  price?: string;
  stopPrice?: string;
  filledAmount: string;
  status: OrderStatus;
  timeInForce: TimeInForce;
  createdAt: string;
  updatedAt: string;
  trades?: Trade[];
}

export interface PlaceOrderData {
  tradingPair: string;
  type: OrderType;
  side: OrderSide;
  amount: string;
  price?: string;
  stopPrice?: string;
  timeInForce?: TimeInForce;
}

// Trade Types
export interface Trade {
  id: string;
  tradingPair: string;
  baseCurrency: string;
  quoteCurrency: string;
  side: OrderSide;
  amount: string;
  price: string;
  fee: string;
  orderId: string;
  orderType: OrderType;
  createdAt: string;
}

// Wallet Types
export interface Wallet {
  currency: Currency;
  balance: string;
  lockedBalance: string;
  availableBalance: string;
  address?: string;
}

// Transaction Types
export type TransactionType = 'DEPOSIT' | 'WITHDRAWAL';
export type TransactionStatus = 'PENDING' | 'CONFIRMING' | 'COMPLETED' | 'FAILED' | 'APPROVED' | 'PROCESSING' | 'REJECTED';

export interface Transaction {
  id: string;
  type: TransactionType;
  currency: Currency;
  amount: string;
  fee: string;
  address: string;
  txHash?: string;
  confirmations?: number;
  requiredConfirmations?: number;
  status: TransactionStatus;
  rejectionReason?: string;
  createdAt: string;
  updatedAt: string;
  processedAt?: string;
}

// Market Data Types
export interface Ticker {
  symbol: string;
  price: number;
  change24h: number;
  volume24h: number;
  high24h: number;
  low24h: number;
  bestBid?: number;
  bestAsk?: number;
  spread?: number;
  spreadPercentage?: number;
  timestamp: number;
}

export interface OrderBookEntry {
  price: string;
  amount: string;
}

export interface OrderBook {
  symbol: string;
  bids: OrderBookEntry[];
  asks: OrderBookEntry[];
  timestamp: number;
}

export interface MarketTrade {
  id: string;
  price: string;
  amount: string;
  side: 'buy' | 'sell';
  timestamp: number;
}

export interface CandlestickData {
  timestamp: number;
  open: string;
  high: string;
  low: string;
  close: string;
  volume: string;
}

// Chart Types
export interface ChartData {
  time: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

export type ChartInterval = '1m' | '5m' | '15m' | '30m' | '1h' | '4h' | '1d' | '1w';

// Notification Types
export type NotificationType = 
  | 'TRADE_EXECUTED'
  | 'ORDER_FILLED'
  | 'DEPOSIT_COMPLETED'
  | 'WITHDRAWAL_COMPLETED'
  | 'KYC_APPROVED'
  | 'KYC_REJECTED'
  | 'SECURITY_ALERT'
  | 'SYSTEM_MAINTENANCE';

export interface Notification {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  data?: any;
  isRead: boolean;
  createdAt: string;
}

// WebSocket Types
export interface WebSocketMessage {
  event: string;
  data: any;
  timestamp: number;
}

export interface OrderBookUpdate {
  tradingPair: string;
  orderbook: OrderBook;
  timestamp: number;
}

export interface TradeUpdate {
  tradingPair: string;
  trade: MarketTrade;
  timestamp: number;
}

export interface TickerUpdate {
  tradingPair: string;
  ticker: Ticker;
  timestamp: number;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  error?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

// Form Types
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'number' | 'select' | 'date' | 'tel';
  required?: boolean;
  placeholder?: string;
  options?: { value: string; label: string }[];
  validation?: {
    min?: number;
    max?: number;
    pattern?: RegExp;
    message?: string;
  };
}

// Theme Types
export interface ThemeConfig {
  mode: 'light' | 'dark';
  primaryColor: string;
  secondaryColor: string;
  backgroundColor: string;
  surfaceColor: string;
  textColor: string;
  borderColor: string;
}

// Settings Types
export interface UserSettings {
  theme: 'light' | 'dark' | 'auto';
  language: string;
  timezone: string;
  notifications: {
    email: boolean;
    push: boolean;
    trading: boolean;
    security: boolean;
  };
  trading: {
    defaultTradingPair: string;
    chartInterval: ChartInterval;
    orderConfirmation: boolean;
  };
}

// Error Types
export interface ApiError {
  message: string;
  statusCode: number;
  errors?: string[];
}

// Loading States
export interface LoadingState {
  isLoading: boolean;
  error: string | null;
}

// Redux Store Types
export interface RootState {
  auth: AuthState;
  market: MarketState;
  trading: TradingState;
  wallet: WalletState;
  ui: UIState;
}

export interface MarketState extends LoadingState {
  tradingPairs: TradingPair[];
  currentPair: string;
  tickers: Record<string, Ticker>;
  orderBooks: Record<string, OrderBook>;
  recentTrades: Record<string, MarketTrade[]>;
  chartData: Record<string, CandlestickData[]>;
  chartInterval: ChartInterval;
}

export interface TradingState extends LoadingState {
  orders: Order[];
  trades: Trade[];
  orderHistory: Order[];
  tradeHistory: Trade[];
  stats: {
    totalTrades: number;
    totalVolume: string;
    totalFees: {
      buyerFees: string;
      sellerFees: string;
    };
    activeOrders: number;
  };
}

export interface WalletState extends LoadingState {
  wallets: Wallet[];
  transactions: Transaction[];
  depositAddresses: Record<string, string>;
}

export interface UIState {
  sidebarOpen: boolean;
  theme: 'light' | 'dark';
  notifications: Notification[];
  unreadCount: number;
  isConnected: boolean;
  lastUpdate: number;
}
