{"version": 3, "file": "searchNumbers.js", "names": ["normalizeArguments", "PhoneNumberMatcher", "searchNumbers", "arguments", "text", "options", "metadata", "matcher", "Symbol", "iterator", "next", "hasNext", "done", "value"], "sources": ["../../source/legacy/searchNumbers.js"], "sourcesContent": ["import normalizeArguments from '../normalizeArguments.js'\r\nimport PhoneNumberMatcher from '../PhoneNumberMatcher.js'\r\n\r\n/**\r\n * @return ES6 `for ... of` iterator.\r\n */\r\nexport default function searchNumbers()\r\n{\r\n\tconst { text, options, metadata } = normalizeArguments(arguments)\r\n\r\n\tconst matcher = new PhoneNumberMatcher(text, options, metadata)\r\n\r\n\treturn  {\r\n\t\t[Symbol.iterator]() {\r\n\t\t\treturn {\r\n\t    \t\tnext: () => {\r\n\t    \t\t\tif (matcher.hasNext()) {\r\n\t\t\t\t\t\treturn {\r\n\t\t\t\t\t\t\tdone: false,\r\n\t\t\t\t\t\t\tvalue: matcher.next()\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tdone: true\r\n\t\t\t\t\t}\r\n\t    \t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n"], "mappings": ";;AAAA,OAAOA,kBAAP,MAA+B,0BAA/B;AACA,OAAOC,kBAAP,MAA+B,0BAA/B;AAEA;AACA;AACA;;AACA,eAAe,SAASC,aAAT,GACf;EACC,0BAAoCF,kBAAkB,CAACG,SAAD,CAAtD;EAAA,IAAQC,IAAR,uBAAQA,IAAR;EAAA,IAAcC,OAAd,uBAAcA,OAAd;EAAA,IAAuBC,QAAvB,uBAAuBA,QAAvB;;EAEA,IAAMC,OAAO,GAAG,IAAIN,kBAAJ,CAAuBG,IAAvB,EAA6BC,OAA7B,EAAsCC,QAAtC,CAAhB;EAEA,2BACEE,MAAM,CAACC,QADT,cACqB;IACnB,OAAO;MACHC,IAAI,EAAE,gBAAM;QACX,IAAIH,OAAO,CAACI,OAAR,EAAJ,EAAuB;UACzB,OAAO;YACNC,IAAI,EAAE,KADA;YAENC,KAAK,EAAEN,OAAO,CAACG,IAAR;UAFD,CAAP;QAIA;;QACD,OAAO;UACNE,IAAI,EAAE;QADA,CAAP;MAGG;IAXE,CAAP;EAaA,CAfF;AAiBA"}