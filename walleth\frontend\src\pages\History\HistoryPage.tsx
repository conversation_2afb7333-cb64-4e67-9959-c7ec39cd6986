import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  Box,
  Paper,
  Typography,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  TextField,
  MenuItem,
  Grid,
  Card,
  CardContent,
  Pagination,
  Avatar,
  Tooltip,
} from '@mui/material';
import {
  Visibility as ViewIcon,
  Download as DownloadIcon,
  FilterList as FilterIcon,
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';

// Store
import { RootState } from '@/types';
import { fetchOrders, fetchTrades } from '@/store/slices/tradingSlice';
import { fetchTransactions } from '@/store/slices/walletSlice';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`history-tabpanel-${index}`}
      aria-labelledby={`history-tab-${index}`}
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
}

const HistoryPage: React.FC = () => {
  const dispatch = useDispatch();
  
  const { orders, trades } = useSelector((state: RootState) => state.trading);
  const { transactions } = useSelector((state: RootState) => state.wallet);
  
  const [tabValue, setTabValue] = useState(0);
  const [page, setPage] = useState(1);
  const [filters, setFilters] = useState({
    status: '',
    type: '',
    currency: '',
    startDate: null as Date | null,
    endDate: null as Date | null,
  });

  useEffect(() => {
    dispatch(fetchOrders());
    dispatch(fetchTrades());
    dispatch(fetchTransactions());
  }, [dispatch]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
    setPage(1);
  };

  const handlePageChange = (event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const formatAmount = (amount: string) => {
    return parseFloat(amount).toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 8,
    });
  };

  const getOrderStatusColor = (status: string) => {
    switch (status) {
      case 'FILLED':
        return 'success';
      case 'PENDING':
      case 'PARTIAL_FILLED':
        return 'warning';
      case 'CANCELLED':
      case 'REJECTED':
        return 'error';
      default:
        return 'default';
    }
  };

  const getTransactionStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'success';
      case 'PENDING':
      case 'CONFIRMING':
        return 'warning';
      case 'FAILED':
      case 'REJECTED':
        return 'error';
      default:
        return 'default';
    }
  };

  const itemsPerPage = 20;
  const startIndex = (page - 1) * itemsPerPage;

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom>
          History
        </Typography>
        <Typography variant="body1" color="text.secondary">
          View your trading and transaction history
        </Typography>
      </Box>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Total Orders
              </Typography>
              <Typography variant="h4" color="primary.main">
                {orders.length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Total Trades
              </Typography>
              <Typography variant="h4" color="success.main">
                {trades.length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Transactions
              </Typography>
              <Typography variant="h4" color="info.main">
                {transactions.length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Active Orders
              </Typography>
              <Typography variant="h4" color="warning.main">
                {orders.filter(o => o.status === 'PENDING' || o.status === 'PARTIAL_FILLED').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs value={tabValue} onChange={handleTabChange}>
          <Tab label="Orders" />
          <Tab label="Trades" />
          <Tab label="Deposits & Withdrawals" />
        </Tabs>
      </Paper>

      {/* Orders Tab */}
      <TabPanel value={tabValue} index={0}>
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Date</TableCell>
                <TableCell>Pair</TableCell>
                <TableCell>Type</TableCell>
                <TableCell>Side</TableCell>
                <TableCell align="right">Amount</TableCell>
                <TableCell align="right">Price</TableCell>
                <TableCell align="right">Filled</TableCell>
                <TableCell>Status</TableCell>
                <TableCell align="center">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {orders.slice(startIndex, startIndex + itemsPerPage).map((order) => (
                <TableRow key={order.id}>
                  <TableCell>
                    <Typography variant="body2">
                      {formatDate(order.createdAt)}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" fontWeight={600}>
                      {order.tradingPair}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip label={order.type} size="small" variant="outlined" />
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={order.side}
                      size="small"
                      color={order.side === 'BUY' ? 'success' : 'error'}
                    />
                  </TableCell>
                  <TableCell align="right">
                    {formatAmount(order.amount)}
                  </TableCell>
                  <TableCell align="right">
                    {order.price ? formatAmount(order.price) : 'Market'}
                  </TableCell>
                  <TableCell align="right">
                    {formatAmount(order.filledAmount)}
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={order.status}
                      size="small"
                      color={getOrderStatusColor(order.status)}
                    />
                  </TableCell>
                  <TableCell align="center">
                    <Tooltip title="View Details">
                      <IconButton size="small">
                        <ViewIcon />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
        
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
          <Pagination
            count={Math.ceil(orders.length / itemsPerPage)}
            page={page}
            onChange={handlePageChange}
          />
        </Box>
      </TabPanel>

      {/* Trades Tab */}
      <TabPanel value={tabValue} index={1}>
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Date</TableCell>
                <TableCell>Pair</TableCell>
                <TableCell>Side</TableCell>
                <TableCell align="right">Amount</TableCell>
                <TableCell align="right">Price</TableCell>
                <TableCell align="right">Fee</TableCell>
                <TableCell align="right">Total</TableCell>
                <TableCell align="center">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {trades.slice(startIndex, startIndex + itemsPerPage).map((trade) => (
                <TableRow key={trade.id}>
                  <TableCell>
                    <Typography variant="body2">
                      {formatDate(trade.createdAt)}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" fontWeight={600}>
                      {trade.tradingPair}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={trade.side}
                      size="small"
                      color={trade.side === 'BUY' ? 'success' : 'error'}
                    />
                  </TableCell>
                  <TableCell align="right">
                    {formatAmount(trade.amount)}
                  </TableCell>
                  <TableCell align="right">
                    {formatAmount(trade.price)}
                  </TableCell>
                  <TableCell align="right">
                    {formatAmount(trade.fee)}
                  </TableCell>
                  <TableCell align="right">
                    {formatAmount((parseFloat(trade.amount) * parseFloat(trade.price)).toString())}
                  </TableCell>
                  <TableCell align="center">
                    <Tooltip title="View Details">
                      <IconButton size="small">
                        <ViewIcon />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
        
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
          <Pagination
            count={Math.ceil(trades.length / itemsPerPage)}
            page={page}
            onChange={handlePageChange}
          />
        </Box>
      </TabPanel>

      {/* Transactions Tab */}
      <TabPanel value={tabValue} index={2}>
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Date</TableCell>
                <TableCell>Type</TableCell>
                <TableCell>Asset</TableCell>
                <TableCell align="right">Amount</TableCell>
                <TableCell align="right">Fee</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>TX Hash</TableCell>
                <TableCell align="center">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {transactions.slice(startIndex, startIndex + itemsPerPage).map((transaction) => (
                <TableRow key={transaction.id}>
                  <TableCell>
                    <Typography variant="body2">
                      {formatDate(transaction.createdAt)}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={transaction.type}
                      size="small"
                      color={transaction.type === 'DEPOSIT' ? 'success' : 'warning'}
                    />
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      {transaction.currency.iconUrl && (
                        <Avatar
                          src={transaction.currency.iconUrl}
                          sx={{ width: 24, height: 24, mr: 1 }}
                        />
                      )}
                      <Typography variant="body2" fontWeight={600}>
                        {transaction.currency.symbol}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell align="right">
                    {formatAmount(transaction.amount)}
                  </TableCell>
                  <TableCell align="right">
                    {formatAmount(transaction.fee)}
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={transaction.status}
                      size="small"
                      color={getTransactionStatusColor(transaction.status)}
                    />
                  </TableCell>
                  <TableCell>
                    {transaction.txHash ? (
                      <Typography variant="caption">
                        {transaction.txHash.substring(0, 8)}...
                      </Typography>
                    ) : (
                      '-'
                    )}
                  </TableCell>
                  <TableCell align="center">
                    <Tooltip title="View Details">
                      <IconButton size="small">
                        <ViewIcon />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
        
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
          <Pagination
            count={Math.ceil(transactions.length / itemsPerPage)}
            page={page}
            onChange={handlePageChange}
          />
        </Box>
      </TabPanel>

      {/* Export Button */}
      <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 3 }}>
        <IconButton
          color="primary"
          sx={{
            border: '1px solid',
            borderColor: 'primary.main',
            borderRadius: 1,
            px: 2,
          }}
        >
          <DownloadIcon sx={{ mr: 1 }} />
          <Typography variant="body2">Export</Typography>
        </IconButton>
      </Box>
    </Box>
  );
};

export default HistoryPage;
