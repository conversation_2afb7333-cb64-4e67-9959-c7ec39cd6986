import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import {
  Box,
  Chip,
  Collapse,
  Typography,
  IconButton,
  Alert,
  AlertTitle,
} from '@mui/material';
import {
  Wifi as ConnectedIcon,
  WifiOff as DisconnectedIcon,
  Close as CloseIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';

// Store
import { RootState } from '@/types';

// Services
import { WebSocketService } from '@/services/websocket';

const ConnectionStatus: React.FC = () => {
  const { isConnected, lastUpdate } = useSelector((state: RootState) => state.ui);
  const [showAlert, setShowAlert] = useState(false);
  const [reconnecting, setReconnecting] = useState(false);

  useEffect(() => {
    // Show alert when connection is lost
    if (!isConnected) {
      setShowAlert(true);
    } else {
      setShowAlert(false);
      setReconnecting(false);
    }
  }, [isConnected]);

  const handleReconnect = () => {
    setReconnecting(true);
    WebSocketService.connect();
    
    // Hide reconnecting state after 5 seconds
    setTimeout(() => {
      setReconnecting(false);
    }, 5000);
  };

  const handleCloseAlert = () => {
    setShowAlert(false);
  };

  const getTimeSinceLastUpdate = () => {
    if (!lastUpdate) return 'Never';
    
    const now = Date.now();
    const diff = now - lastUpdate;
    
    if (diff < 60000) {
      return 'Just now';
    } else if (diff < 3600000) {
      return `${Math.floor(diff / 60000)}m ago`;
    } else {
      return `${Math.floor(diff / 3600000)}h ago`;
    }
  };

  return (
    <>
      {/* Connection Status Indicator */}
      <Box
        sx={{
          position: 'fixed',
          top: 70,
          right: 16,
          zIndex: 1200,
        }}
      >
        <Chip
          icon={isConnected ? <ConnectedIcon /> : <DisconnectedIcon />}
          label={
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Typography variant="caption" fontWeight={600}>
                {isConnected ? 'Connected' : 'Disconnected'}
              </Typography>
              {isConnected && (
                <Typography variant="caption" color="text.secondary">
                  • {getTimeSinceLastUpdate()}
                </Typography>
              )}
            </Box>
          }
          color={isConnected ? 'success' : 'error'}
          variant={isConnected ? 'filled' : 'outlined'}
          size="small"
          sx={{
            height: 28,
            '& .MuiChip-label': {
              px: 1,
            },
          }}
        />
      </Box>

      {/* Connection Lost Alert */}
      <Collapse in={showAlert && !isConnected}>
        <Alert
          severity="warning"
          action={
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <IconButton
                color="inherit"
                size="small"
                onClick={handleReconnect}
                disabled={reconnecting}
                sx={{
                  animation: reconnecting ? 'spin 1s linear infinite' : 'none',
                  '@keyframes spin': {
                    '0%': { transform: 'rotate(0deg)' },
                    '100%': { transform: 'rotate(360deg)' },
                  },
                }}
              >
                <RefreshIcon fontSize="small" />
              </IconButton>
              <IconButton
                color="inherit"
                size="small"
                onClick={handleCloseAlert}
              >
                <CloseIcon fontSize="small" />
              </IconButton>
            </Box>
          }
          sx={{
            mb: 0,
            borderRadius: 0,
            borderLeft: 'none',
            borderRight: 'none',
            borderTop: 'none',
          }}
        >
          <AlertTitle>Connection Lost</AlertTitle>
          Real-time data updates are currently unavailable. 
          {reconnecting ? ' Attempting to reconnect...' : ' Click refresh to reconnect.'}
        </Alert>
      </Collapse>

      {/* Reconnecting Indicator */}
      {reconnecting && (
        <Box
          sx={{
            position: 'fixed',
            top: 70,
            left: '50%',
            transform: 'translateX(-50%)',
            zIndex: 1200,
          }}
        >
          <Chip
            icon={<RefreshIcon sx={{ animation: 'spin 1s linear infinite' }} />}
            label="Reconnecting..."
            color="info"
            variant="filled"
            size="small"
          />
        </Box>
      )}
    </>
  );
};

export default ConnectionStatus;
