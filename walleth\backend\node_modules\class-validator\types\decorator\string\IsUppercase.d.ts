import { ValidationOptions } from '../ValidationOptions';
export declare const IS_UPPERCASE = "isUppercase";
/**
 * Checks if the string is uppercase.
 * If given value is not a string, then it returns false.
 */
export declare function isUppercase(value: unknown): boolean;
/**
 * Checks if the string is uppercase.
 * If given value is not a string, then it returns false.
 */
export declare function IsUppercase(validationOptions?: ValidationOptions): PropertyDecorator;
