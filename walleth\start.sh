#!/bin/bash

# CryptoNest - Complete Startup Script
# This script starts all services for the CryptoNest cryptocurrency exchange platform

echo "🏠 Starting CryptoNest - Your Secure Cryptocurrency Trading Haven"
echo "=================================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${PURPLE}$1${NC}"
}

# Check if required tools are installed
check_requirements() {
    print_header "🔍 Checking Requirements..."
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 18+ and try again."
        exit 1
    fi
    
    # Check npm
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed. Please install npm and try again."
        exit 1
    fi
    
    # Check Docker (optional)
    if ! command -v docker &> /dev/null; then
        print_warning "Docker is not installed. Database services will need to be started manually."
    fi
    
    print_success "All requirements met!"
}

# Install dependencies
install_dependencies() {
    print_header "📦 Installing Dependencies..."
    
    # Backend dependencies
    print_status "Installing backend dependencies..."
    cd backend
    if npm install; then
        print_success "Backend dependencies installed"
    else
        print_error "Failed to install backend dependencies"
        exit 1
    fi
    cd ..
    
    # Frontend dependencies
    print_status "Installing frontend dependencies..."
    cd frontend
    if npm install; then
        print_success "Frontend dependencies installed"
    else
        print_error "Failed to install frontend dependencies"
        exit 1
    fi
    cd ..
}

# Setup environment files
setup_environment() {
    print_header "⚙️  Setting up Environment..."
    
    # Backend environment
    if [ ! -f "backend/.env" ]; then
        print_status "Creating backend .env file..."
        cp backend/.env.example backend/.env
        print_success "Backend .env file created"
    else
        print_status "Backend .env file already exists"
    fi
    
    # Frontend environment
    if [ ! -f "frontend/.env" ]; then
        print_status "Creating frontend .env file..."
        cp frontend/.env.example frontend/.env
        print_success "Frontend .env file created"
    else
        print_status "Frontend .env file already exists"
    fi
}

# Start database services
start_databases() {
    print_header "🗄️  Starting Database Services..."
    
    if command -v docker &> /dev/null; then
        print_status "Starting PostgreSQL with Docker..."
        docker run -d \
            --name cryptonest-postgres \
            -e POSTGRES_USER=cryptonest_user \
            -e POSTGRES_PASSWORD=cryptonest_password \
            -e POSTGRES_DB=cryptonest_db \
            -p 5432:5432 \
            postgres:15 2>/dev/null || print_warning "PostgreSQL container already running or failed to start"
        
        print_status "Starting Redis with Docker..."
        docker run -d \
            --name cryptonest-redis \
            -p 6379:6379 \
            redis:7-alpine 2>/dev/null || print_warning "Redis container already running or failed to start"
        
        print_status "Starting MongoDB with Docker..."
        docker run -d \
            --name cryptonest-mongo \
            -p 27017:27017 \
            mongo:6 2>/dev/null || print_warning "MongoDB container already running or failed to start"
        
        print_success "Database services started"
    else
        print_warning "Docker not available. Please start PostgreSQL, Redis, and MongoDB manually."
    fi
}

# Setup database
setup_database() {
    print_header "🏗️  Setting up Database..."
    
    cd backend
    
    # Wait for database to be ready
    print_status "Waiting for database to be ready..."
    sleep 5
    
    # Generate Prisma client
    print_status "Generating Prisma client..."
    if npx prisma generate; then
        print_success "Prisma client generated"
    else
        print_error "Failed to generate Prisma client"
        exit 1
    fi
    
    # Run database migrations
    print_status "Running database migrations..."
    if npx prisma db push; then
        print_success "Database migrations completed"
    else
        print_error "Failed to run database migrations"
        exit 1
    fi
    
    # Seed database
    print_status "Seeding database with initial data..."
    if npx prisma db seed; then
        print_success "Database seeded successfully"
    else
        print_warning "Database seeding failed or already completed"
    fi
    
    cd ..
}

# Start backend server
start_backend() {
    print_header "🚀 Starting Backend Server..."
    
    cd backend
    print_status "Starting CryptoNest API server on port 3001..."
    npm run dev &
    BACKEND_PID=$!
    cd ..
    
    print_success "Backend server started (PID: $BACKEND_PID)"
}

# Start frontend server
start_frontend() {
    print_header "🎨 Starting Frontend Server..."
    
    cd frontend
    print_status "Starting CryptoNest web application on port 3000..."
    npm run dev &
    FRONTEND_PID=$!
    cd ..
    
    print_success "Frontend server started (PID: $FRONTEND_PID)"
}

# Display startup information
show_startup_info() {
    print_header "🎉 CryptoNest Started Successfully!"
    echo ""
    echo -e "${CYAN}🏠 CryptoNest - Your Secure Cryptocurrency Trading Haven${NC}"
    echo -e "${CYAN}================================================================${NC}"
    echo ""
    echo -e "${GREEN}✅ Frontend:${NC} http://localhost:3000"
    echo -e "${GREEN}✅ Backend API:${NC} http://localhost:3001"
    echo -e "${GREEN}✅ API Documentation:${NC} http://localhost:3001/api-docs"
    echo ""
    echo -e "${YELLOW}📧 Demo Account:${NC}"
    echo -e "   Email: <EMAIL>"
    echo -e "   Password: Demo123!@#"
    echo ""
    echo -e "${YELLOW}👑 Admin Account:${NC}"
    echo -e "   Email: <EMAIL>"
    echo -e "   Password: Admin123!@#"
    echo ""
    echo -e "${BLUE}🔧 Services:${NC}"
    echo -e "   PostgreSQL: localhost:5432"
    echo -e "   Redis: localhost:6379"
    echo -e "   MongoDB: localhost:27017"
    echo ""
    echo -e "${PURPLE}💡 Features:${NC}"
    echo -e "   • Beautiful gradient-based UI design"
    echo -e "   • Real-time trading with WebSocket"
    echo -e "   • Advanced security features"
    echo -e "   • Admin dashboard and user management"
    echo -e "   • KYC verification system"
    echo -e "   • Multi-currency wallet"
    echo ""
    echo -e "${RED}⚠️  To stop all services, press Ctrl+C${NC}"
    echo ""
}

# Cleanup function
cleanup() {
    print_header "🧹 Cleaning up..."
    
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null
        print_status "Backend server stopped"
    fi
    
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null
        print_status "Frontend server stopped"
    fi
    
    print_success "CryptoNest stopped successfully"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Main execution
main() {
    check_requirements
    install_dependencies
    setup_environment
    start_databases
    setup_database
    start_backend
    sleep 3
    start_frontend
    sleep 2
    show_startup_info
    
    # Keep script running
    while true; do
        sleep 1
    done
}

# Run main function
main
