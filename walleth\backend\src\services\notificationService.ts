import { PrismaClient, NotificationType } from '@prisma/client';
import nodemailer from 'nodemailer';
import { logger } from '../utils/logger';
import { WebSocketService } from './websocket';

const prisma = new PrismaClient();

interface EmailTemplate {
  subject: string;
  html: string;
  text: string;
}

interface NotificationData {
  userId: string;
  type: NotificationType;
  title: string;
  message: string;
  data?: any;
  sendEmail?: boolean;
  sendSMS?: boolean;
}

export class NotificationService {
  private emailTransporter: nodemailer.Transporter;
  private wsService?: WebSocketService;

  constructor(wsService?: WebSocketService) {
    this.wsService = wsService;
    this.setupEmailTransporter();
  }

  private setupEmailTransporter(): void {
    this.emailTransporter = nodemailer.createTransporter({
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: false, // true for 465, false for other ports
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
      },
    });

    // Verify connection configuration
    this.emailTransporter.verify((error, success) => {
      if (error) {
        logger.error('Email transporter verification failed:', error);
      } else {
        logger.info('Email transporter is ready');
      }
    });
  }

  public async sendNotification(notificationData: NotificationData): Promise<void> {
    try {
      // Create notification in database
      const notification = await prisma.notification.create({
        data: {
          userId: notificationData.userId,
          type: notificationData.type,
          title: notificationData.title,
          message: notificationData.message,
          data: notificationData.data || {}
        }
      });

      // Send real-time notification via WebSocket
      if (this.wsService) {
        this.wsService.notifyUser(notificationData.userId, 'notification', {
          id: notification.id,
          type: notification.type,
          title: notification.title,
          message: notification.message,
          data: notification.data,
          createdAt: notification.createdAt
        });
      }

      // Send email notification if requested
      if (notificationData.sendEmail) {
        await this.sendEmailNotification(notificationData);
      }

      // Send SMS notification if requested
      if (notificationData.sendSMS) {
        await this.sendSMSNotification(notificationData);
      }

      logger.info('Notification sent', {
        userId: notificationData.userId,
        type: notificationData.type,
        title: notificationData.title
      });
    } catch (error) {
      logger.error('Failed to send notification:', error);
      throw error;
    }
  }

  private async sendEmailNotification(notificationData: NotificationData): Promise<void> {
    try {
      // Get user email
      const user = await prisma.user.findUnique({
        where: { id: notificationData.userId },
        select: { email: true, firstName: true, lastName: true }
      });

      if (!user) {
        throw new Error('User not found');
      }

      // Generate email template
      const template = this.generateEmailTemplate(notificationData, user);

      // Send email
      await this.emailTransporter.sendMail({
        from: `${process.env.FROM_NAME} <${process.env.FROM_EMAIL}>`,
        to: user.email,
        subject: template.subject,
        text: template.text,
        html: template.html
      });

      logger.info('Email notification sent', {
        userId: notificationData.userId,
        email: user.email,
        type: notificationData.type
      });
    } catch (error) {
      logger.error('Failed to send email notification:', error);
    }
  }

  private async sendSMSNotification(notificationData: NotificationData): Promise<void> {
    try {
      // Get user phone number
      const user = await prisma.user.findUnique({
        where: { id: notificationData.userId },
        select: { phone: true, isPhoneVerified: true }
      });

      if (!user || !user.phone || !user.isPhoneVerified) {
        logger.warn('Cannot send SMS: user phone not verified', {
          userId: notificationData.userId
        });
        return;
      }

      // TODO: Implement SMS sending using Twilio or similar service
      // For now, just log the SMS
      logger.info('SMS notification would be sent', {
        userId: notificationData.userId,
        phone: user.phone,
        message: notificationData.message
      });
    } catch (error) {
      logger.error('Failed to send SMS notification:', error);
    }
  }

  private generateEmailTemplate(notificationData: NotificationData, user: any): EmailTemplate {
    const userName = user.firstName ? `${user.firstName} ${user.lastName || ''}`.trim() : 'User';
    
    switch (notificationData.type) {
      case 'TRADE_EXECUTED':
        return {
          subject: 'Trade Executed - WALLETH',
          text: `Hi ${userName},\n\nYour trade has been executed successfully.\n\n${notificationData.message}\n\nBest regards,\nWALLETH Team`,
          html: this.generateTradeExecutedHTML(userName, notificationData)
        };

      case 'ORDER_FILLED':
        return {
          subject: 'Order Filled - WALLETH',
          text: `Hi ${userName},\n\nYour order has been filled.\n\n${notificationData.message}\n\nBest regards,\nWALLETH Team`,
          html: this.generateOrderFilledHTML(userName, notificationData)
        };

      case 'DEPOSIT_COMPLETED':
        return {
          subject: 'Deposit Completed - WALLETH',
          text: `Hi ${userName},\n\nYour deposit has been completed successfully.\n\n${notificationData.message}\n\nBest regards,\nWALLETH Team`,
          html: this.generateDepositCompletedHTML(userName, notificationData)
        };

      case 'WITHDRAWAL_COMPLETED':
        return {
          subject: 'Withdrawal Completed - WALLETH',
          text: `Hi ${userName},\n\nYour withdrawal has been processed successfully.\n\n${notificationData.message}\n\nBest regards,\nWALLETH Team`,
          html: this.generateWithdrawalCompletedHTML(userName, notificationData)
        };

      case 'KYC_APPROVED':
        return {
          subject: 'KYC Verification Approved - WALLETH',
          text: `Hi ${userName},\n\nCongratulations! Your KYC verification has been approved.\n\n${notificationData.message}\n\nBest regards,\nWALLETH Team`,
          html: this.generateKYCApprovedHTML(userName, notificationData)
        };

      case 'KYC_REJECTED':
        return {
          subject: 'KYC Verification Rejected - WALLETH',
          text: `Hi ${userName},\n\nUnfortunately, your KYC verification has been rejected.\n\n${notificationData.message}\n\nBest regards,\nWALLETH Team`,
          html: this.generateKYCRejectedHTML(userName, notificationData)
        };

      case 'SECURITY_ALERT':
        return {
          subject: 'Security Alert - WALLETH',
          text: `Hi ${userName},\n\nWe detected a security event on your account.\n\n${notificationData.message}\n\nBest regards,\nWALLETH Team`,
          html: this.generateSecurityAlertHTML(userName, notificationData)
        };

      default:
        return {
          subject: `${notificationData.title} - WALLETH`,
          text: `Hi ${userName},\n\n${notificationData.message}\n\nBest regards,\nWALLETH Team`,
          html: this.generateGenericHTML(userName, notificationData)
        };
    }
  }

  private generateTradeExecutedHTML(userName: string, data: NotificationData): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Trade Executed</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #1976d2; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .footer { padding: 20px; text-align: center; color: #666; }
          .trade-details { background: white; padding: 15px; border-radius: 5px; margin: 15px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🚀 WALLETH</h1>
            <h2>Trade Executed Successfully</h2>
          </div>
          <div class="content">
            <p>Hi ${userName},</p>
            <p>Your trade has been executed successfully!</p>
            <div class="trade-details">
              <h3>Trade Details:</h3>
              <p>${data.message}</p>
            </div>
            <p>You can view more details in your trading history.</p>
          </div>
          <div class="footer">
            <p>Best regards,<br>WALLETH Team</p>
            <p><small>This is an automated message. Please do not reply to this email.</small></p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  private generateOrderFilledHTML(userName: string, data: NotificationData): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Order Filled</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #4caf50; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .footer { padding: 20px; text-align: center; color: #666; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🚀 WALLETH</h1>
            <h2>Order Filled</h2>
          </div>
          <div class="content">
            <p>Hi ${userName},</p>
            <p>Your order has been filled successfully!</p>
            <p>${data.message}</p>
          </div>
          <div class="footer">
            <p>Best regards,<br>WALLETH Team</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  private generateDepositCompletedHTML(userName: string, data: NotificationData): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Deposit Completed</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #2196f3; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .footer { padding: 20px; text-align: center; color: #666; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🚀 WALLETH</h1>
            <h2>Deposit Completed</h2>
          </div>
          <div class="content">
            <p>Hi ${userName},</p>
            <p>Your deposit has been completed successfully!</p>
            <p>${data.message}</p>
            <p>The funds are now available in your wallet.</p>
          </div>
          <div class="footer">
            <p>Best regards,<br>WALLETH Team</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  private generateWithdrawalCompletedHTML(userName: string, data: NotificationData): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Withdrawal Completed</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #ff9800; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .footer { padding: 20px; text-align: center; color: #666; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🚀 WALLETH</h1>
            <h2>Withdrawal Completed</h2>
          </div>
          <div class="content">
            <p>Hi ${userName},</p>
            <p>Your withdrawal has been processed successfully!</p>
            <p>${data.message}</p>
          </div>
          <div class="footer">
            <p>Best regards,<br>WALLETH Team</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  private generateKYCApprovedHTML(userName: string, data: NotificationData): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>KYC Approved</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #4caf50; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .footer { padding: 20px; text-align: center; color: #666; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🚀 WALLETH</h1>
            <h2>KYC Verification Approved</h2>
          </div>
          <div class="content">
            <p>Hi ${userName},</p>
            <p>Congratulations! Your KYC verification has been approved.</p>
            <p>${data.message}</p>
            <p>You now have access to all platform features including higher withdrawal limits.</p>
          </div>
          <div class="footer">
            <p>Best regards,<br>WALLETH Team</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  private generateKYCRejectedHTML(userName: string, data: NotificationData): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>KYC Rejected</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #f44336; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .footer { padding: 20px; text-align: center; color: #666; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🚀 WALLETH</h1>
            <h2>KYC Verification Rejected</h2>
          </div>
          <div class="content">
            <p>Hi ${userName},</p>
            <p>Unfortunately, your KYC verification has been rejected.</p>
            <p>${data.message}</p>
            <p>Please review the requirements and submit new documents.</p>
          </div>
          <div class="footer">
            <p>Best regards,<br>WALLETH Team</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  private generateSecurityAlertHTML(userName: string, data: NotificationData): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Security Alert</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #f44336; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .footer { padding: 20px; text-align: center; color: #666; }
          .alert { background: #ffebee; border-left: 4px solid #f44336; padding: 15px; margin: 15px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🚀 WALLETH</h1>
            <h2>🔒 Security Alert</h2>
          </div>
          <div class="content">
            <p>Hi ${userName},</p>
            <div class="alert">
              <strong>Security Alert:</strong> We detected a security event on your account.
            </div>
            <p>${data.message}</p>
            <p>If this was not you, please contact our support team immediately.</p>
          </div>
          <div class="footer">
            <p>Best regards,<br>WALLETH Security Team</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  private generateGenericHTML(userName: string, data: NotificationData): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>${data.title}</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #1976d2; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; background: #f9f9f9; }
          .footer { padding: 20px; text-align: center; color: #666; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🚀 WALLETH</h1>
            <h2>${data.title}</h2>
          </div>
          <div class="content">
            <p>Hi ${userName},</p>
            <p>${data.message}</p>
          </div>
          <div class="footer">
            <p>Best regards,<br>WALLETH Team</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  public async markAsRead(userId: string, notificationId: string): Promise<void> {
    try {
      await prisma.notification.updateMany({
        where: {
          id: notificationId,
          userId: userId
        },
        data: {
          isRead: true
        }
      });

      logger.info('Notification marked as read', { userId, notificationId });
    } catch (error) {
      logger.error('Failed to mark notification as read:', error);
      throw error;
    }
  }

  public async markAllAsRead(userId: string): Promise<void> {
    try {
      await prisma.notification.updateMany({
        where: {
          userId: userId,
          isRead: false
        },
        data: {
          isRead: true
        }
      });

      logger.info('All notifications marked as read', { userId });
    } catch (error) {
      logger.error('Failed to mark all notifications as read:', error);
      throw error;
    }
  }

  public async getUserNotifications(
    userId: string,
    page: number = 1,
    limit: number = 20
  ): Promise<any> {
    try {
      const skip = (page - 1) * limit;

      const [notifications, total] = await Promise.all([
        prisma.notification.findMany({
          where: { userId },
          orderBy: { createdAt: 'desc' },
          skip,
          take: limit
        }),
        prisma.notification.count({
          where: { userId }
        })
      ]);

      const unreadCount = await prisma.notification.count({
        where: {
          userId,
          isRead: false
        }
      });

      return {
        notifications,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        },
        unreadCount
      };
    } catch (error) {
      logger.error('Failed to get user notifications:', error);
      throw error;
    }
  }
}
