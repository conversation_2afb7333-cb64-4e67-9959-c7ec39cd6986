import { ComponentsProps } from './props';
import { ComponentsOverrides } from './overrides';
import { ComponentsVariants } from './variants';

export interface Components<Theme = unknown> {
  MuiAlert?: {
    defaultProps?: ComponentsProps['MuiAlert'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiAlert'];
    variants?: ComponentsVariants<Theme>['MuiAlert'];
  };
  MuiAlertTitle?: {
    defaultProps?: ComponentsProps['MuiAlertTitle'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiAlertTitle'];
    variants?: ComponentsVariants<Theme>['MuiAlertTitle'];
  };
  MuiAppBar?: {
    defaultProps?: ComponentsProps['MuiAppBar'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiAppBar'];
    variants?: ComponentsVariants<Theme>['MuiAppBar'];
  };
  MuiAutocomplete?: {
    defaultProps?: ComponentsProps['MuiAutocomplete'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiAutocomplete'];
    variants?: ComponentsVariants<Theme>['MuiAutocomplete'];
  };
  MuiAvatar?: {
    defaultProps?: ComponentsProps['MuiAvatar'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiAvatar'];
    variants?: ComponentsVariants<Theme>['MuiAvatar'];
  };
  MuiAvatarGroup?: {
    defaultProps?: ComponentsProps['MuiAvatarGroup'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiAvatarGroup'];
    variants?: ComponentsVariants<Theme>['MuiAvatarGroup'];
  };
  MuiBackdrop?: {
    defaultProps?: ComponentsProps['MuiBackdrop'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiBackdrop'];
    variants?: ComponentsVariants<Theme>['MuiBackdrop'];
  };
  MuiBadge?: {
    defaultProps?: ComponentsProps['MuiBadge'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiBadge'];
    variants?: ComponentsVariants<Theme>['MuiBadge'];
  };
  MuiBottomNavigation?: {
    defaultProps?: ComponentsProps['MuiBottomNavigation'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiBottomNavigation'];
    variants?: ComponentsVariants<Theme>['MuiBottomNavigation'];
  };
  MuiBottomNavigationAction?: {
    defaultProps?: ComponentsProps['MuiBottomNavigationAction'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiBottomNavigationAction'];
    variants?: ComponentsVariants<Theme>['MuiBottomNavigationAction'];
  };
  MuiBreadcrumbs?: {
    defaultProps?: ComponentsProps['MuiBreadcrumbs'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiBreadcrumbs'];
    variants?: ComponentsVariants<Theme>['MuiBreadcrumbs'];
  };
  MuiButton?: {
    defaultProps?: ComponentsProps['MuiButton'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiButton'];
    variants?: ComponentsVariants<Theme>['MuiButton'];
  };
  MuiButtonBase?: {
    defaultProps?: ComponentsProps['MuiButtonBase'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiButtonBase'];
    variants?: ComponentsVariants<Theme>['MuiButtonBase'];
  };
  MuiButtonGroup?: {
    defaultProps?: ComponentsProps['MuiButtonGroup'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiButtonGroup'];
    variants?: ComponentsVariants<Theme>['MuiButtonGroup'];
  };
  MuiCard?: {
    defaultProps?: ComponentsProps['MuiCard'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiCard'];
    variants?: ComponentsVariants<Theme>['MuiCard'];
  };
  MuiCardActionArea?: {
    defaultProps?: ComponentsProps['MuiCardActionArea'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiCardActionArea'];
    variants?: ComponentsVariants<Theme>['MuiCardActionArea'];
  };
  MuiCardActions?: {
    defaultProps?: ComponentsProps['MuiCardActions'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiCardActions'];
    variants?: ComponentsVariants<Theme>['MuiCardActions'];
  };
  MuiCardContent?: {
    defaultProps?: ComponentsProps['MuiCardContent'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiCardContent'];
    variants?: ComponentsVariants<Theme>['MuiCardContent'];
  };
  MuiCardHeader?: {
    defaultProps?: ComponentsProps['MuiCardHeader'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiCardHeader'];
    variants?: ComponentsVariants<Theme>['MuiCardHeader'];
  };
  MuiCardMedia?: {
    defaultProps?: ComponentsProps['MuiCardMedia'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiCardMedia'];
    variants?: ComponentsVariants<Theme>['MuiCardMedia'];
  };
  MuiCheckbox?: {
    defaultProps?: ComponentsProps['MuiCheckbox'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiCheckbox'];
    variants?: ComponentsVariants<Theme>['MuiCheckbox'];
  };
  MuiChip?: {
    defaultProps?: ComponentsProps['MuiChip'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiChip'];
    variants?: ComponentsVariants<Theme>['MuiChip'];
  };
  MuiCircularProgress?: {
    defaultProps?: ComponentsProps['MuiCircularProgress'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiCircularProgress'];
    variants?: ComponentsVariants<Theme>['MuiCircularProgress'];
  };
  MuiCollapse?: {
    defaultProps?: ComponentsProps['MuiCollapse'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiCollapse'];
    variants?: ComponentsVariants<Theme>['MuiCollapse'];
  };
  MuiContainer?: {
    defaultProps?: ComponentsProps['MuiContainer'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiContainer'];
    variants?: ComponentsVariants<Theme>['MuiContainer'];
  };
  MuiCssBaseline?: {
    defaultProps?: ComponentsProps['MuiCssBaseline'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiCssBaseline'];
    variants?: ComponentsVariants<Theme>['MuiCssBaseline'];
  };
  MuiDialog?: {
    defaultProps?: ComponentsProps['MuiDialog'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiDialog'];
    variants?: ComponentsVariants<Theme>['MuiDialog'];
  };
  MuiDialogActions?: {
    defaultProps?: ComponentsProps['MuiDialogActions'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiDialogActions'];
    variants?: ComponentsVariants<Theme>['MuiDialogActions'];
  };
  MuiDialogContent?: {
    defaultProps?: ComponentsProps['MuiDialogContent'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiDialogContent'];
    variants?: ComponentsVariants<Theme>['MuiDialogContent'];
  };
  MuiDialogContentText?: {
    defaultProps?: ComponentsProps['MuiDialogContentText'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiDialogContentText'];
    variants?: ComponentsVariants<Theme>['MuiDialogContentText'];
  };
  MuiDialogTitle?: {
    defaultProps?: ComponentsProps['MuiDialogTitle'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiDialogTitle'];
    variants?: ComponentsVariants<Theme>['MuiDialogTitle'];
  };
  MuiDivider?: {
    defaultProps?: ComponentsProps['MuiDivider'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiDivider'];
    variants?: ComponentsVariants<Theme>['MuiDivider'];
  };
  MuiDrawer?: {
    defaultProps?: ComponentsProps['MuiDrawer'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiDrawer'];
    variants?: ComponentsVariants<Theme>['MuiDrawer'];
  };
  MuiAccordion?: {
    defaultProps?: ComponentsProps['MuiAccordion'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiAccordion'];
    variants?: ComponentsVariants<Theme>['MuiAccordion'];
  };
  MuiAccordionActions?: {
    defaultProps?: ComponentsProps['MuiAccordionActions'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiAccordionActions'];
    variants?: ComponentsVariants<Theme>['MuiAccordionActions'];
  };
  MuiAccordionDetails?: {
    defaultProps?: ComponentsProps['MuiAccordionDetails'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiAccordionDetails'];
    variants?: ComponentsVariants<Theme>['MuiAccordionDetails'];
  };
  MuiAccordionSummary?: {
    defaultProps?: ComponentsProps['MuiAccordionSummary'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiAccordionSummary'];
    variants?: ComponentsVariants<Theme>['MuiAccordionSummary'];
  };
  MuiFab?: {
    defaultProps?: ComponentsProps['MuiFab'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiFab'];
    variants?: ComponentsVariants<Theme>['MuiFab'];
  };
  MuiFilledInput?: {
    defaultProps?: ComponentsProps['MuiFilledInput'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiFilledInput'];
    variants?: ComponentsVariants<Theme>['MuiFilledInput'];
  };
  MuiFormControl?: {
    defaultProps?: ComponentsProps['MuiFormControl'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiFormControl'];
    variants?: ComponentsVariants<Theme>['MuiFormControl'];
  };
  MuiFormControlLabel?: {
    defaultProps?: ComponentsProps['MuiFormControlLabel'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiFormControlLabel'];
    variants?: ComponentsVariants<Theme>['MuiFormControlLabel'];
  };
  MuiFormGroup?: {
    defaultProps?: ComponentsProps['MuiFormGroup'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiFormGroup'];
    variants?: ComponentsVariants<Theme>['MuiFormGroup'];
  };
  MuiFormHelperText?: {
    defaultProps?: ComponentsProps['MuiFormHelperText'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiFormHelperText'];
    variants?: ComponentsVariants<Theme>['MuiFormHelperText'];
  };
  MuiFormLabel?: {
    defaultProps?: ComponentsProps['MuiFormLabel'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiFormLabel'];
    variants?: ComponentsVariants<Theme>['MuiFormLabel'];
  };
  MuiGrid?: {
    defaultProps?: ComponentsProps['MuiGrid'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiGrid'];
    variants?: ComponentsVariants<Theme>['MuiGrid'];
  };
  MuiGrid2?: {
    defaultProps?: ComponentsProps['MuiGrid2'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiGrid2'];
    variants?: ComponentsVariants<Theme>['MuiGrid2'];
  };
  MuiImageList?: {
    defaultProps?: ComponentsProps['MuiImageList'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiImageList'];
    variants?: ComponentsVariants<Theme>['MuiImageList'];
  };
  MuiImageListItem?: {
    defaultProps?: ComponentsProps['MuiImageListItem'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiImageListItem'];
    variants?: ComponentsVariants<Theme>['MuiImageListItem'];
  };
  MuiImageListItemBar?: {
    defaultProps?: ComponentsProps['MuiImageListItemBar'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiImageListItemBar'];
    variants?: ComponentsVariants<Theme>['MuiImageListItemBar'];
  };
  MuiIcon?: {
    defaultProps?: ComponentsProps['MuiIcon'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiIcon'];
    variants?: ComponentsVariants<Theme>['MuiIcon'];
  };
  MuiIconButton?: {
    defaultProps?: ComponentsProps['MuiIconButton'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiIconButton'];
    variants?: ComponentsVariants<Theme>['MuiIconButton'];
  };
  MuiInput?: {
    defaultProps?: ComponentsProps['MuiInput'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiInput'];
    variants?: ComponentsVariants<Theme>['MuiInput'];
  };
  MuiInputAdornment?: {
    defaultProps?: ComponentsProps['MuiInputAdornment'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiInputAdornment'];
    variants?: ComponentsVariants<Theme>['MuiInputAdornment'];
  };
  MuiInputBase?: {
    defaultProps?: ComponentsProps['MuiInputBase'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiInputBase'];
    variants?: ComponentsVariants<Theme>['MuiInputBase'];
  };
  MuiInputLabel?: {
    defaultProps?: ComponentsProps['MuiInputLabel'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiInputLabel'];
    variants?: ComponentsVariants<Theme>['MuiInputLabel'];
  };
  MuiLinearProgress?: {
    defaultProps?: ComponentsProps['MuiLinearProgress'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiLinearProgress'];
    variants?: ComponentsVariants<Theme>['MuiLinearProgress'];
  };
  MuiLink?: {
    defaultProps?: ComponentsProps['MuiLink'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiLink'];
    variants?: ComponentsVariants<Theme>['MuiLink'];
  };
  MuiList?: {
    defaultProps?: ComponentsProps['MuiList'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiList'];
    variants?: ComponentsVariants<Theme>['MuiList'];
  };
  MuiListItem?: {
    defaultProps?: ComponentsProps['MuiListItem'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiListItem'];
    variants?: ComponentsVariants<Theme>['MuiListItem'];
  };
  MuiListItemButton?: {
    defaultProps?: ComponentsProps['MuiListItemButton'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiListItemButton'];
    variants?: ComponentsVariants<Theme>['MuiListItemButton'];
  };
  MuiListItemAvatar?: {
    defaultProps?: ComponentsProps['MuiListItemAvatar'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiListItemAvatar'];
    variants?: ComponentsVariants<Theme>['MuiListItemAvatar'];
  };
  MuiListItemIcon?: {
    defaultProps?: ComponentsProps['MuiListItemIcon'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiListItemIcon'];
    variants?: ComponentsVariants<Theme>['MuiListItemIcon'];
  };
  MuiListItemSecondaryAction?: {
    defaultProps?: ComponentsProps['MuiListItemSecondaryAction'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiListItemSecondaryAction'];
    variants?: ComponentsVariants<Theme>['MuiListItemSecondaryAction'];
  };
  MuiListItemText?: {
    defaultProps?: ComponentsProps['MuiListItemText'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiListItemText'];
    variants?: ComponentsVariants<Theme>['MuiListItemText'];
  };
  MuiListSubheader?: {
    defaultProps?: ComponentsProps['MuiListSubheader'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiListSubheader'];
    variants?: ComponentsVariants<Theme>['MuiListSubheader'];
  };
  MuiMenu?: {
    defaultProps?: ComponentsProps['MuiMenu'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiMenu'];
    variants?: ComponentsVariants<Theme>['MuiMenu'];
  };
  MuiMenuItem?: {
    defaultProps?: ComponentsProps['MuiMenuItem'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiMenuItem'];
    variants?: ComponentsVariants<Theme>['MuiMenuItem'];
  };
  MuiMenuList?: {
    defaultProps?: ComponentsProps['MuiMenuList'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiMenuList'];
    variants?: ComponentsVariants<Theme>['MuiMenuList'];
  };
  MuiMobileStepper?: {
    defaultProps?: ComponentsProps['MuiMobileStepper'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiMobileStepper'];
    variants?: ComponentsVariants<Theme>['MuiMobileStepper'];
  };
  MuiModal?: {
    defaultProps?: ComponentsProps['MuiModal'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiModal'];
    variants?: ComponentsVariants<Theme>['MuiModal'];
  };
  MuiNativeSelect?: {
    defaultProps?: ComponentsProps['MuiNativeSelect'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiNativeSelect'];
    variants?: ComponentsVariants<Theme>['MuiNativeSelect'];
  };
  MuiOutlinedInput?: {
    defaultProps?: ComponentsProps['MuiOutlinedInput'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiOutlinedInput'];
    variants?: ComponentsVariants<Theme>['MuiOutlinedInput'];
  };
  MuiPagination?: {
    defaultProps?: ComponentsProps['MuiPagination'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiPagination'];
    variants?: ComponentsVariants<Theme>['MuiPagination'];
  };
  MuiPaginationItem?: {
    defaultProps?: ComponentsProps['MuiPaginationItem'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiPaginationItem'];
    variants?: ComponentsVariants<Theme>['MuiPaginationItem'];
  };
  MuiPaper?: {
    defaultProps?: ComponentsProps['MuiPaper'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiPaper'];
    variants?: ComponentsVariants<Theme>['MuiPaper'];
  };
  MuiPopper?: {
    defaultProps?: ComponentsProps['MuiPopper'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiPopper'];
  };
  MuiPopover?: {
    defaultProps?: ComponentsProps['MuiPopover'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiPopover'];
    variants?: ComponentsVariants<Theme>['MuiPopover'];
  };
  MuiRadio?: {
    defaultProps?: ComponentsProps['MuiRadio'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiRadio'];
    variants?: ComponentsVariants<Theme>['MuiRadio'];
  };
  MuiRadioGroup?: {
    defaultProps?: ComponentsProps['MuiRadioGroup'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiRadioGroup'];
    variants?: ComponentsVariants<Theme>['MuiRadioGroup'];
  };
  MuiRating?: {
    defaultProps?: ComponentsProps['MuiRating'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiRating'];
    variants?: ComponentsVariants<Theme>['MuiRating'];
  };
  MuiScopedCssBaseline?: {
    defaultProps?: ComponentsProps['MuiScopedCssBaseline'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiScopedCssBaseline'];
    variants?: ComponentsVariants<Theme>['MuiScopedCssBaseline'];
  };
  MuiSelect?: {
    defaultProps?: ComponentsProps['MuiSelect'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiSelect'];
    variants?: ComponentsVariants<Theme>['MuiSelect'];
  };
  MuiSkeleton?: {
    defaultProps?: ComponentsProps['MuiSkeleton'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiSkeleton'];
    variants?: ComponentsVariants<Theme>['MuiSkeleton'];
  };
  MuiSlider?: {
    defaultProps?: ComponentsProps['MuiSlider'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiSlider'];
    variants?: ComponentsVariants<Theme>['MuiSlider'];
  };
  MuiSnackbar?: {
    defaultProps?: ComponentsProps['MuiSnackbar'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiSnackbar'];
    variants?: ComponentsVariants<Theme>['MuiSnackbar'];
  };
  MuiSnackbarContent?: {
    defaultProps?: ComponentsProps['MuiSnackbarContent'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiSnackbarContent'];
    variants?: ComponentsVariants<Theme>['MuiSnackbarContent'];
  };
  MuiSpeedDial?: {
    defaultProps?: ComponentsProps['MuiSpeedDial'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiSpeedDial'];
    variants?: ComponentsVariants<Theme>['MuiSpeedDial'];
  };
  MuiSpeedDialAction?: {
    defaultProps?: ComponentsProps['MuiSpeedDialAction'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiSpeedDialAction'];
    variants?: ComponentsVariants<Theme>['MuiSpeedDialAction'];
  };
  MuiSpeedDialIcon?: {
    defaultProps?: ComponentsProps['MuiSpeedDialIcon'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiSpeedDialIcon'];
    variants?: ComponentsVariants<Theme>['MuiSpeedDialIcon'];
  };
  MuiStack?: {
    defaultProps?: ComponentsProps['MuiStack'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiStack'];
    variants?: ComponentsVariants<Theme>['MuiStack'];
  };
  MuiStep?: {
    defaultProps?: ComponentsProps['MuiStep'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiStep'];
    variants?: ComponentsVariants<Theme>['MuiStep'];
  };
  MuiStepButton?: {
    defaultProps?: ComponentsProps['MuiStepButton'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiStepButton'];
    variants?: ComponentsVariants<Theme>['MuiStepButton'];
  };
  MuiStepConnector?: {
    defaultProps?: ComponentsProps['MuiStepConnector'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiStepConnector'];
    variants?: ComponentsVariants<Theme>['MuiStepConnector'];
  };
  MuiStepContent?: {
    defaultProps?: ComponentsProps['MuiStepContent'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiStepContent'];
    variants?: ComponentsVariants<Theme>['MuiStepContent'];
  };
  MuiStepIcon?: {
    defaultProps?: ComponentsProps['MuiStepIcon'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiStepIcon'];
    variants?: ComponentsVariants<Theme>['MuiStepIcon'];
  };
  MuiStepLabel?: {
    defaultProps?: ComponentsProps['MuiStepLabel'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiStepLabel'];
    variants?: ComponentsVariants<Theme>['MuiStepLabel'];
  };
  MuiStepper?: {
    defaultProps?: ComponentsProps['MuiStepper'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiStepper'];
    variants?: ComponentsVariants<Theme>['MuiStepper'];
  };
  MuiSvgIcon?: {
    defaultProps?: ComponentsProps['MuiSvgIcon'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiSvgIcon'];
    variants?: ComponentsVariants<Theme>['MuiSvgIcon'];
  };
  MuiSwipeableDrawer?: {
    defaultProps?: ComponentsProps['MuiSwipeableDrawer'];
  };
  MuiSwitch?: {
    defaultProps?: ComponentsProps['MuiSwitch'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiSwitch'];
    variants?: ComponentsVariants<Theme>['MuiSwitch'];
  };
  MuiTab?: {
    defaultProps?: ComponentsProps['MuiTab'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiTab'];
    variants?: ComponentsVariants<Theme>['MuiTab'];
  };
  MuiTable?: {
    defaultProps?: ComponentsProps['MuiTable'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiTable'];
    variants?: ComponentsVariants<Theme>['MuiTable'];
  };
  MuiTableBody?: {
    defaultProps?: ComponentsProps['MuiTableBody'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiTableBody'];
    variants?: ComponentsVariants<Theme>['MuiTableBody'];
  };
  MuiTableCell?: {
    defaultProps?: ComponentsProps['MuiTableCell'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiTableCell'];
    variants?: ComponentsVariants<Theme>['MuiTableCell'];
  };
  MuiTableContainer?: {
    defaultProps?: ComponentsProps['MuiTableContainer'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiTableContainer'];
    variants?: ComponentsVariants<Theme>['MuiTableContainer'];
  };
  MuiTableFooter?: {
    defaultProps?: ComponentsProps['MuiTableFooter'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiTableFooter'];
    variants?: ComponentsVariants<Theme>['MuiTableFooter'];
  };
  MuiTableHead?: {
    defaultProps?: ComponentsProps['MuiTableHead'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiTableHead'];
    variants?: ComponentsVariants<Theme>['MuiTableHead'];
  };
  MuiTablePagination?: {
    defaultProps?: ComponentsProps['MuiTablePagination'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiTablePagination'];
    variants?: ComponentsVariants<Theme>['MuiTablePagination'];
  };
  MuiTableRow?: {
    defaultProps?: ComponentsProps['MuiTableRow'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiTableRow'];
    variants?: ComponentsVariants<Theme>['MuiTableRow'];
  };
  MuiTableSortLabel?: {
    defaultProps?: ComponentsProps['MuiTableSortLabel'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiTableSortLabel'];
    variants?: ComponentsVariants<Theme>['MuiTableSortLabel'];
  };
  MuiTabs?: {
    defaultProps?: ComponentsProps['MuiTabs'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiTabs'];
    variants?: ComponentsVariants<Theme>['MuiTabs'];
  };
  MuiTextField?: {
    defaultProps?: ComponentsProps['MuiTextField'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiTextField'];
    variants?: ComponentsVariants<Theme>['MuiTextField'];
  };
  MuiToggleButton?: {
    defaultProps?: ComponentsProps['MuiToggleButton'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiToggleButton'];
    variants?: ComponentsVariants<Theme>['MuiToggleButton'];
  };
  MuiToggleButtonGroup?: {
    defaultProps?: ComponentsProps['MuiToggleButtonGroup'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiToggleButtonGroup'];
    variants?: ComponentsVariants<Theme>['MuiToggleButtonGroup'];
  };
  MuiToolbar?: {
    defaultProps?: ComponentsProps['MuiToolbar'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiToolbar'];
    variants?: ComponentsVariants<Theme>['MuiToolbar'];
  };
  MuiTooltip?: {
    defaultProps?: ComponentsProps['MuiTooltip'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiTooltip'];
    variants?: ComponentsVariants<Theme>['MuiTooltip'];
  };
  MuiTouchRipple?: {
    defaultProps?: ComponentsProps['MuiTouchRipple'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiTouchRipple'];
    variants?: ComponentsVariants<Theme>['MuiTouchRipple'];
  };
  MuiTypography?: {
    defaultProps?: ComponentsProps['MuiTypography'];
    styleOverrides?: ComponentsOverrides<Theme>['MuiTypography'];
    variants?: ComponentsVariants<Theme>['MuiTypography'];
  };
  MuiUseMediaQuery?: {
    defaultProps?: ComponentsProps['MuiUseMediaQuery'];
  };
}
