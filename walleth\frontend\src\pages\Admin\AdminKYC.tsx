import React, { useEffect, useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Grid,
  Card,
  CardContent,
  Avatar,
  Divider,
  Alert,
} from '@mui/material';
import {
  CheckCircle as ApproveIcon,
  Cancel as RejectIcon,
  Visibility as ViewIcon,
  Person as PersonIcon,
  Description as DocumentIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';

// Services
import { adminAPI } from '@/services/api';

interface KYCSubmission {
  id: string;
  userId: string;
  user: {
    email: string;
    firstName: string;
    lastName: string;
    phone: string;
  };
  documentType: string;
  documentNumber: string;
  documentImages: string[];
  selfieImage: string;
  status: string;
  submittedAt: string;
  reviewedAt?: string;
  reviewedBy?: string;
  rejectionReason?: string;
}

const AdminKYC: React.FC = () => {
  const [submissions, setSubmissions] = useState<KYCSubmission[]>([]);
  const [totalSubmissions, setTotalSubmissions] = useState(0);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);
  const [selectedSubmission, setSelectedSubmission] = useState<KYCSubmission | null>(null);
  const [reviewDialog, setReviewDialog] = useState(false);
  const [rejectionReason, setRejectionReason] = useState('');

  useEffect(() => {
    fetchKYCSubmissions();
  }, [page, rowsPerPage]);

  const fetchKYCSubmissions = async () => {
    try {
      const response = await adminAPI.getPendingKYC({
        page: page + 1,
        limit: rowsPerPage,
      });
      setSubmissions(response.data.submissions);
      setTotalSubmissions(response.data.total);
    } catch (error) {
      console.error('Failed to fetch KYC submissions:', error);
    }
  };

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleReviewSubmission = (submission: KYCSubmission) => {
    setSelectedSubmission(submission);
    setReviewDialog(true);
    setRejectionReason('');
  };

  const handleApproveKYC = async () => {
    if (!selectedSubmission) return;

    try {
      await adminAPI.approveKYC(selectedSubmission.userId);
      setReviewDialog(false);
      fetchKYCSubmissions();
    } catch (error) {
      console.error('Failed to approve KYC:', error);
    }
  };

  const handleRejectKYC = async () => {
    if (!selectedSubmission || !rejectionReason.trim()) return;

    try {
      await adminAPI.rejectKYC(selectedSubmission.userId, rejectionReason);
      setReviewDialog(false);
      fetchKYCSubmissions();
    } catch (error) {
      console.error('Failed to reject KYC:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'SUBMITTED':
        return 'info';
      case 'APPROVED':
        return 'success';
      case 'REJECTED':
        return 'error';
      default:
        return 'warning';
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom>
          KYC Review
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Review and approve user identity verification submissions
        </Typography>
      </Box>

      {/* Stats */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={4}>
          <Card>
            <CardContent>
              <Typography variant="h4" color="warning.main">
                {submissions.filter(s => s.status === 'SUBMITTED').length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Pending Review
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={4}>
          <Card>
            <CardContent>
              <Typography variant="h4" color="success.main">
                {submissions.filter(s => s.status === 'APPROVED').length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Approved Today
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={4}>
          <Card>
            <CardContent>
              <Typography variant="h4" color="error.main">
                {submissions.filter(s => s.status === 'REJECTED').length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Rejected Today
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* KYC Submissions Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>User</TableCell>
              <TableCell>Document Type</TableCell>
              <TableCell>Document Number</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Submitted</TableCell>
              <TableCell align="center">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {submissions.map((submission) => (
              <TableRow key={submission.id} hover>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Avatar sx={{ mr: 2, bgcolor: 'primary.main' }}>
                      {submission.user.firstName ? submission.user.firstName[0] : submission.user.email[0]}
                    </Avatar>
                    <Box>
                      <Typography variant="body2" fontWeight={600}>
                        {submission.user.firstName && submission.user.lastName 
                          ? `${submission.user.firstName} ${submission.user.lastName}`
                          : 'No name'
                        }
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {submission.user.email}
                      </Typography>
                    </Box>
                  </Box>
                </TableCell>
                <TableCell>
                  <Chip
                    label={submission.documentType}
                    size="small"
                    variant="outlined"
                  />
                </TableCell>
                <TableCell>
                  <Typography variant="body2" fontFamily="monospace">
                    {submission.documentNumber}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Chip
                    label={submission.status}
                    color={getStatusColor(submission.status)}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {new Date(submission.submittedAt).toLocaleDateString()}
                  </Typography>
                </TableCell>
                <TableCell align="center">
                  <Button
                    size="small"
                    variant="outlined"
                    startIcon={<ViewIcon />}
                    onClick={() => handleReviewSubmission(submission)}
                    disabled={submission.status !== 'SUBMITTED'}
                  >
                    Review
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
        
        <TablePagination
          rowsPerPageOptions={[10, 25, 50]}
          component="div"
          count={totalSubmissions}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </TableContainer>

      {/* Review Dialog */}
      <Dialog open={reviewDialog} onClose={() => setReviewDialog(false)} maxWidth="lg" fullWidth>
        <DialogTitle>KYC Review</DialogTitle>
        <DialogContent>
          {selectedSubmission && (
            <Grid container spacing={3}>
              {/* User Information */}
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      User Information
                    </Typography>
                    
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Avatar sx={{ mr: 2, bgcolor: 'primary.main', width: 56, height: 56 }}>
                        {selectedSubmission.user.firstName ? selectedSubmission.user.firstName[0] : selectedSubmission.user.email[0]}
                      </Avatar>
                      <Box>
                        <Typography variant="h6">
                          {selectedSubmission.user.firstName && selectedSubmission.user.lastName 
                            ? `${selectedSubmission.user.firstName} ${selectedSubmission.user.lastName}`
                            : 'No name provided'
                          }
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {selectedSubmission.user.email}
                        </Typography>
                      </Box>
                    </Box>

                    <Divider sx={{ my: 2 }} />

                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" color="text.secondary">Document Type:</Typography>
                      <Typography variant="body1">{selectedSubmission.documentType}</Typography>
                    </Box>

                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" color="text.secondary">Document Number:</Typography>
                      <Typography variant="body1" fontFamily="monospace">
                        {selectedSubmission.documentNumber}
                      </Typography>
                    </Box>

                    <Box>
                      <Typography variant="body2" color="text.secondary">Submitted:</Typography>
                      <Typography variant="body1">
                        {new Date(selectedSubmission.submittedAt).toLocaleString()}
                      </Typography>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>

              {/* Document Images */}
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Submitted Documents
                    </Typography>
                    
                    <Box sx={{ mb: 3 }}>
                      <Typography variant="subtitle2" gutterBottom>
                        Document Images:
                      </Typography>
                      <Grid container spacing={2}>
                        {selectedSubmission.documentImages.map((image, index) => (
                          <Grid item xs={6} key={index}>
                            <Box
                              sx={{
                                border: '1px solid',
                                borderColor: 'divider',
                                borderRadius: 1,
                                p: 1,
                                textAlign: 'center',
                                cursor: 'pointer',
                                '&:hover': { backgroundColor: 'action.hover' },
                              }}
                            >
                              <DocumentIcon sx={{ fontSize: 48, color: 'text.secondary' }} />
                              <Typography variant="caption" display="block">
                                Document {index + 1}
                              </Typography>
                            </Box>
                          </Grid>
                        ))}
                      </Grid>
                    </Box>

                    <Box>
                      <Typography variant="subtitle2" gutterBottom>
                        Selfie Image:
                      </Typography>
                      <Box
                        sx={{
                          border: '1px solid',
                          borderColor: 'divider',
                          borderRadius: 1,
                          p: 2,
                          textAlign: 'center',
                          cursor: 'pointer',
                          '&:hover': { backgroundColor: 'action.hover' },
                        }}
                      >
                        <PersonIcon sx={{ fontSize: 48, color: 'text.secondary' }} />
                        <Typography variant="caption" display="block">
                          Selfie Photo
                        </Typography>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>

              {/* Review Actions */}
              <Grid item xs={12}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Review Decision
                    </Typography>
                    
                    <Alert severity="info" sx={{ mb: 3 }}>
                      Please carefully review all submitted documents before making a decision. 
                      Ensure the information matches and the documents are clear and valid.
                    </Alert>

                    <TextField
                      fullWidth
                      multiline
                      rows={3}
                      label="Rejection Reason (if rejecting)"
                      value={rejectionReason}
                      onChange={(e) => setRejectionReason(e.target.value)}
                      placeholder="Provide a clear reason for rejection..."
                      sx={{ mb: 2 }}
                    />
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setReviewDialog(false)}>
            Cancel
          </Button>
          <Button
            onClick={handleRejectKYC}
            color="error"
            variant="outlined"
            startIcon={<RejectIcon />}
            disabled={!rejectionReason.trim()}
          >
            Reject
          </Button>
          <Button
            onClick={handleApproveKYC}
            color="success"
            variant="contained"
            startIcon={<ApproveIcon />}
          >
            Approve
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AdminKYC;
