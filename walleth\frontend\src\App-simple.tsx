import React, { useState } from 'react';

const Walleth: React.FC = () => {
  const [balance] = useState({
    BTC: 0.00234567,
    ETH: 1.45678901,
    USDT: 1250.00,
    BNB: 5.67890123
  });

  const [transactions] = useState([
    { id: 1, type: 'Recibido', amount: '+0.001 BTC', from: '1A1zP1...', time: '2 min ago', status: 'Confirmado' },
    { id: 2, type: 'Enviado', amount: '-0.5 ETH', to: '0x742d...', time: '1 hora ago', status: 'Confirmado' },
    { id: 3, type: 'Recibido', amount: '+100 USDT', from: '0x8ba1...', time: '3 horas ago', status: 'Confirmado' },
  ]);

  const [activeTab, setActiveTab] = useState('wallet');

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)',
      fontFamily: 'Inter, sans-serif',
      color: 'white'
    }}>
      {/* Header */}
      <div style={{
        padding: '1rem 2rem',
        background: 'rgba(255, 255, 255, 0.1)',
        backdropFilter: 'blur(20px)',
        borderBottom: '1px solid rgba(255, 255, 255, 0.2)'
      }}>
        <h1 style={{ margin: 0, fontSize: '1.8rem', fontWeight: 700 }}>
          💳 Walleth - Tu Wallet Crypto
        </h1>
      </div>

      {/* Navigation */}
      <div style={{
        display: 'flex',
        padding: '1rem 2rem',
        gap: '1rem'
      }}>
        {['wallet', 'enviar', 'recibir', 'historial'].map(tab => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab)}
            style={{
              padding: '0.75rem 1.5rem',
              background: activeTab === tab ? 'rgba(255, 255, 255, 0.2)' : 'rgba(255, 255, 255, 0.1)',
              border: '1px solid rgba(255, 255, 255, 0.2)',
              borderRadius: '12px',
              color: 'white',
              cursor: 'pointer',
              textTransform: 'capitalize',
              fontWeight: activeTab === tab ? 600 : 400,
              backdropFilter: 'blur(10px)'
            }}
          >
            {tab}
          </button>
        ))}
      </div>

      {/* Content */}
      <div style={{ padding: '0 2rem 2rem' }}>

        {/* Wallet Tab */}
        {activeTab === 'wallet' && (
          <div>
            <h2 style={{ marginBottom: '1.5rem' }}>💰 Mis Balances</h2>
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1rem' }}>
              {Object.entries(balance).map(([coin, amount]) => (
                <div key={coin} style={{
                  background: 'rgba(255, 255, 255, 0.1)',
                  padding: '1.5rem',
                  borderRadius: '16px',
                  backdropFilter: 'blur(20px)',
                  border: '1px solid rgba(255, 255, 255, 0.2)'
                }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <h3 style={{ margin: 0, fontSize: '1.2rem' }}>{coin}</h3>
                    <span style={{ fontSize: '0.9rem', opacity: 0.7 }}>
                      {coin === 'BTC' ? '₿' : coin === 'ETH' ? 'Ξ' : coin === 'USDT' ? '$' : '🔸'}
                    </span>
                  </div>
                  <p style={{ margin: '0.5rem 0 0', fontSize: '1.5rem', fontWeight: 600 }}>
                    {amount.toLocaleString()} {coin}
                  </p>
                  <p style={{ margin: '0.25rem 0 0', fontSize: '0.9rem', opacity: 0.7 }}>
                    ≈ ${(amount * (coin === 'BTC' ? 45000 : coin === 'ETH' ? 2800 : coin === 'USDT' ? 1 : 300)).toLocaleString()}
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Enviar Tab */}
        {activeTab === 'enviar' && (
          <div style={{
            background: 'rgba(255, 255, 255, 0.1)',
            padding: '2rem',
            borderRadius: '16px',
            backdropFilter: 'blur(20px)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            maxWidth: '500px'
          }}>
            <h2 style={{ marginBottom: '1.5rem' }}>📤 Enviar Criptomonedas</h2>
            <div style={{ marginBottom: '1rem' }}>
              <label style={{ display: 'block', marginBottom: '0.5rem' }}>Moneda:</label>
              <select style={{
                width: '100%',
                padding: '0.75rem',
                borderRadius: '8px',
                border: '1px solid rgba(255, 255, 255, 0.3)',
                background: 'rgba(255, 255, 255, 0.1)',
                color: 'white'
              }}>
                <option>Bitcoin (BTC)</option>
                <option>Ethereum (ETH)</option>
                <option>Tether (USDT)</option>
                <option>Binance Coin (BNB)</option>
              </select>
            </div>
            <div style={{ marginBottom: '1rem' }}>
              <label style={{ display: 'block', marginBottom: '0.5rem' }}>Dirección destino:</label>
              <input
                type="text"
                placeholder="0x742d35Cc6634C0532925a3b8D..."
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  borderRadius: '8px',
                  border: '1px solid rgba(255, 255, 255, 0.3)',
                  background: 'rgba(255, 255, 255, 0.1)',
                  color: 'white'
                }}
              />
            </div>
            <div style={{ marginBottom: '1.5rem' }}>
              <label style={{ display: 'block', marginBottom: '0.5rem' }}>Cantidad:</label>
              <input
                type="number"
                placeholder="0.001"
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  borderRadius: '8px',
                  border: '1px solid rgba(255, 255, 255, 0.3)',
                  background: 'rgba(255, 255, 255, 0.1)',
                  color: 'white'
                }}
              />
            </div>
            <button style={{
              width: '100%',
              padding: '1rem',
              background: 'linear-gradient(45deg, #667eea, #764ba2)',
              border: 'none',
              borderRadius: '12px',
              color: 'white',
              fontSize: '1.1rem',
              fontWeight: 600,
              cursor: 'pointer'
            }}>
              🚀 Enviar
            </button>
          </div>
        )}

        {/* Recibir Tab */}
        {activeTab === 'recibir' && (
          <div style={{
            background: 'rgba(255, 255, 255, 0.1)',
            padding: '2rem',
            borderRadius: '16px',
            backdropFilter: 'blur(20px)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            maxWidth: '500px',
            textAlign: 'center'
          }}>
            <h2 style={{ marginBottom: '1.5rem' }}>📥 Recibir Criptomonedas</h2>
            <div style={{
              background: 'white',
              padding: '2rem',
              borderRadius: '12px',
              marginBottom: '1rem'
            }}>
              <div style={{
                width: '150px',
                height: '150px',
                background: '#000',
                margin: '0 auto',
                borderRadius: '8px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
                fontSize: '0.8rem'
              }}>
                QR CODE
              </div>
            </div>
            <p style={{ fontSize: '0.9rem', marginBottom: '1rem' }}>Tu dirección Bitcoin:</p>
            <div style={{
              background: 'rgba(255, 255, 255, 0.1)',
              padding: '1rem',
              borderRadius: '8px',
              fontSize: '0.9rem',
              wordBreak: 'break-all',
              marginBottom: '1rem'
            }}>
              **********************************
            </div>
            <button style={{
              padding: '0.75rem 1.5rem',
              background: 'linear-gradient(45deg, #667eea, #764ba2)',
              border: 'none',
              borderRadius: '8px',
              color: 'white',
              cursor: 'pointer'
            }}>
              📋 Copiar Dirección
            </button>
          </div>
        )}

        {/* Historial Tab */}
        {activeTab === 'historial' && (
          <div>
            <h2 style={{ marginBottom: '1.5rem' }}>📜 Historial de Transacciones</h2>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
              {transactions.map(tx => (
                <div key={tx.id} style={{
                  background: 'rgba(255, 255, 255, 0.1)',
                  padding: '1.5rem',
                  borderRadius: '12px',
                  backdropFilter: 'blur(20px)',
                  border: '1px solid rgba(255, 255, 255, 0.2)',
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center'
                }}>
                  <div>
                    <h4 style={{ margin: 0, marginBottom: '0.25rem' }}>{tx.type}</h4>
                    <p style={{ margin: 0, fontSize: '0.9rem', opacity: 0.7 }}>
                      {tx.from ? `De: ${tx.from}` : `Para: ${tx.to}`}
                    </p>
                    <p style={{ margin: 0, fontSize: '0.8rem', opacity: 0.6 }}>{tx.time}</p>
                  </div>
                  <div style={{ textAlign: 'right' }}>
                    <p style={{
                      margin: 0,
                      fontSize: '1.1rem',
                      fontWeight: 600,
                      color: tx.amount.startsWith('+') ? '#4ade80' : '#f87171'
                    }}>
                      {tx.amount}
                    </p>
                    <p style={{ margin: 0, fontSize: '0.8rem', opacity: 0.7 }}>{tx.status}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Walleth;
