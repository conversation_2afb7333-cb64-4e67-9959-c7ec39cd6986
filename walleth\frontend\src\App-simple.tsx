import React from 'react';

const App: React.FC = () => {
  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)',
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center',
      alignItems: 'center',
      color: 'white',
      fontFamily: 'Inter, sans-serif'
    }}>
      <h1 style={{
        fontSize: '4rem',
        fontWeight: 700,
        marginBottom: '1rem',
        textAlign: 'center'
      }}>
        🏠 CryptoNest
      </h1>
      <h2 style={{
        fontSize: '1.5rem',
        fontWeight: 400,
        marginBottom: '2rem',
        textAlign: 'center',
        opacity: 0.9
      }}>
        Your Secure Cryptocurrency Trading Haven
      </h2>
      <div style={{
        background: 'rgba(255, 255, 255, 0.1)',
        padding: '2rem',
        borderRadius: '16px',
        backdropFilter: 'blur(20px)',
        border: '1px solid rgba(255, 255, 255, 0.2)',
        textAlign: 'center',
        maxWidth: '400px'
      }}>
        <h3 style={{ marginBottom: '1rem' }}>🎉 ¡Funciona!</h3>
        <p style={{ marginBottom: '1rem', opacity: 0.8 }}>
          CryptoNest se está ejecutando correctamente con la hermosa paleta de colores.
        </p>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-around',
          marginTop: '1rem'
        }}>
          <div style={{ textAlign: 'center' }}>
            <div style={{ 
              width: '40px', 
              height: '40px', 
              background: '#667eea', 
              borderRadius: '50%',
              margin: '0 auto 0.5rem'
            }}></div>
            <small>Azul Púrpura</small>
          </div>
          <div style={{ textAlign: 'center' }}>
            <div style={{ 
              width: '40px', 
              height: '40px', 
              background: '#764ba2', 
              borderRadius: '50%',
              margin: '0 auto 0.5rem'
            }}></div>
            <small>Púrpura Profundo</small>
          </div>
          <div style={{ textAlign: 'center' }}>
            <div style={{ 
              width: '40px', 
              height: '40px', 
              background: '#f093fb', 
              borderRadius: '50%',
              margin: '0 auto 0.5rem'
            }}></div>
            <small>Rosa Suave</small>
          </div>
        </div>
      </div>
    </div>
  );
};

export default App;
