import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { WalletState, Wallet, Transaction } from '@/types';
import { walletAPI } from '@/services/api';
import toast from 'react-hot-toast';

// Initial state
const initialState: WalletState = {
  wallets: [],
  transactions: [],
  depositAddresses: {},
  isLoading: false,
  error: null,
};

// Async thunks
export const fetchWallets = createAsyncThunk(
  'wallet/fetchWallets',
  async (_, { rejectWithValue }) => {
    try {
      const response = await walletAPI.getWallets();
      return response.data.wallets;
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to fetch wallets';
      return rejectWithValue(message);
    }
  }
);

export const fetchWalletBalance = createAsyncThunk(
  'wallet/fetchWalletBalance',
  async (currencySymbol: string, { rejectWithValue }) => {
    try {
      const response = await walletAPI.getWalletBalance(currencySymbol);
      return response.data.wallet;
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to fetch wallet balance';
      return rejectWithValue(message);
    }
  }
);

export const generateDepositAddress = createAsyncThunk(
  'wallet/generateDepositAddress',
  async (currencySymbol: string, { rejectWithValue }) => {
    try {
      const response = await walletAPI.generateDepositAddress(currencySymbol);
      toast.success('Deposit address generated successfully!');
      return { currencySymbol, address: response.data.address };
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to generate deposit address';
      toast.error(message);
      return rejectWithValue(message);
    }
  }
);

export const requestWithdrawal = createAsyncThunk(
  'wallet/requestWithdrawal',
  async (withdrawalData: {
    currencySymbol: string;
    amount: string;
    address: string;
    twoFactorCode?: string;
  }, { rejectWithValue }) => {
    try {
      const response = await walletAPI.requestWithdrawal(withdrawalData);
      toast.success('Withdrawal request submitted successfully!');
      return response.data.withdrawal;
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to request withdrawal';
      toast.error(message);
      return rejectWithValue(message);
    }
  }
);

export const fetchTransactions = createAsyncThunk(
  'wallet/fetchTransactions',
  async (params?: {
    type?: 'DEPOSIT' | 'WITHDRAWAL';
    currency?: string;
    status?: string;
    page?: number;
    limit?: number;
  }, { rejectWithValue }) => {
    try {
      const response = await walletAPI.getTransactions(params);
      return response.data;
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to fetch transactions';
      return rejectWithValue(message);
    }
  }
);

export const fetchTransactionDetails = createAsyncThunk(
  'wallet/fetchTransactionDetails',
  async (transactionId: string, { rejectWithValue }) => {
    try {
      const response = await walletAPI.getTransactionDetails(transactionId);
      return response.data.transaction;
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to fetch transaction details';
      return rejectWithValue(message);
    }
  }
);

// Wallet slice
const walletSlice = createSlice({
  name: 'wallet',
  initialState,
  reducers: {
    updateWalletBalance: (state, action: PayloadAction<{ currencySymbol: string; balance: string; lockedBalance: string }>) => {
      const { currencySymbol, balance, lockedBalance } = action.payload;
      const walletIndex = state.wallets.findIndex(
        wallet => wallet.currency.symbol === currencySymbol
      );
      
      if (walletIndex !== -1) {
        state.wallets[walletIndex].balance = balance;
        state.wallets[walletIndex].lockedBalance = lockedBalance;
        state.wallets[walletIndex].availableBalance = (
          parseFloat(balance) - parseFloat(lockedBalance)
        ).toString();
      }
    },
    
    addTransaction: (state, action: PayloadAction<Transaction>) => {
      const newTransaction = action.payload;
      state.transactions.unshift(newTransaction);
      
      // Keep only last 100 transactions
      if (state.transactions.length > 100) {
        state.transactions = state.transactions.slice(0, 100);
      }
    },
    
    updateTransaction: (state, action: PayloadAction<Transaction>) => {
      const updatedTransaction = action.payload;
      const index = state.transactions.findIndex(tx => tx.id === updatedTransaction.id);
      
      if (index !== -1) {
        state.transactions[index] = updatedTransaction;
      }
    },
    
    setDepositAddress: (state, action: PayloadAction<{ currencySymbol: string; address: string }>) => {
      const { currencySymbol, address } = action.payload;
      state.depositAddresses[currencySymbol] = address;
    },
    
    clearError: (state) => {
      state.error = null;
    },
    
    resetWalletData: (state) => {
      state.wallets = [];
      state.transactions = [];
      state.depositAddresses = {};
    },
    
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
  },
  extraReducers: (builder) => {
    // Fetch Wallets
    builder
      .addCase(fetchWallets.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchWallets.fulfilled, (state, action) => {
        state.isLoading = false;
        state.wallets = action.payload;
        state.error = null;
      })
      .addCase(fetchWallets.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch Wallet Balance
    builder
      .addCase(fetchWalletBalance.fulfilled, (state, action) => {
        const wallet = action.payload;
        const existingIndex = state.wallets.findIndex(
          w => w.currency.symbol === wallet.currency.symbol
        );
        
        if (existingIndex !== -1) {
          state.wallets[existingIndex] = wallet;
        } else {
          state.wallets.push(wallet);
        }
      });

    // Generate Deposit Address
    builder
      .addCase(generateDepositAddress.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(generateDepositAddress.fulfilled, (state, action) => {
        state.isLoading = false;
        const { currencySymbol, address } = action.payload;
        state.depositAddresses[currencySymbol] = address;
        
        // Update wallet address if wallet exists
        const walletIndex = state.wallets.findIndex(
          wallet => wallet.currency.symbol === currencySymbol
        );
        if (walletIndex !== -1) {
          state.wallets[walletIndex].address = address;
        }
        
        state.error = null;
      })
      .addCase(generateDepositAddress.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Request Withdrawal
    builder
      .addCase(requestWithdrawal.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(requestWithdrawal.fulfilled, (state, action) => {
        state.isLoading = false;
        const withdrawal = action.payload;
        
        // Add withdrawal to transactions
        const transaction: Transaction = {
          id: withdrawal.id,
          type: 'WITHDRAWAL',
          currency: withdrawal.currency,
          amount: withdrawal.amount,
          fee: withdrawal.fee,
          address: withdrawal.address,
          status: withdrawal.status,
          createdAt: withdrawal.createdAt,
          updatedAt: withdrawal.createdAt,
        };
        
        state.transactions.unshift(transaction);
        state.error = null;
      })
      .addCase(requestWithdrawal.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch Transactions
    builder
      .addCase(fetchTransactions.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchTransactions.fulfilled, (state, action) => {
        state.isLoading = false;
        state.transactions = action.payload.transactions;
        state.error = null;
      })
      .addCase(fetchTransactions.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch Transaction Details
    builder
      .addCase(fetchTransactionDetails.fulfilled, (state, action) => {
        const transaction = action.payload;
        const index = state.transactions.findIndex(tx => tx.id === transaction.id);
        
        if (index !== -1) {
          state.transactions[index] = transaction;
        }
      });
  },
});

export const {
  updateWalletBalance,
  addTransaction,
  updateTransaction,
  setDepositAddress,
  clearError,
  resetWalletData,
  setLoading,
} = walletSlice.actions;

export default walletSlice.reducer;
