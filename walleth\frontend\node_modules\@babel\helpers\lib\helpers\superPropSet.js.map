{"version": 3, "names": ["_set", "require", "_getPrototypeOf", "_superPropSet", "classArg", "property", "value", "receiver", "isStrict", "prototype", "set", "getPrototypeOf"], "sources": ["../../src/helpers/superPropSet.ts"], "sourcesContent": ["/* @minVersion 7.25.0 */\n\nimport set from \"./set.ts\";\nimport getPrototypeOf from \"./getPrototypeOf.ts\";\n\nexport default function _superPropSet(\n  classArg: any,\n  property: string,\n  value: any,\n  receiver: any,\n  isStrict: boolean,\n  prototype?: 1,\n) {\n  return set(\n    getPrototypeOf(prototype ? classArg.prototype : classArg),\n    property,\n    value,\n    receiver,\n    isStrict,\n  );\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,IAAA,GAAAC,OAAA;AACA,IAAAC,eAAA,GAAAD,OAAA;AAEe,SAASE,aAAaA,CACnCC,QAAa,EACbC,QAAgB,EAChBC,KAAU,EACVC,QAAa,EACbC,QAAiB,EACjBC,SAAa,EACb;EACA,OAAO,IAAAC,YAAG,EACR,IAAAC,uBAAc,EAACF,SAAS,GAAGL,QAAQ,CAACK,SAAS,GAAGL,QAAQ,CAAC,EACzDC,QAAQ,EACRC,KAAK,EACLC,QAAQ,EACRC,QACF,CAAC;AACH", "ignoreList": []}