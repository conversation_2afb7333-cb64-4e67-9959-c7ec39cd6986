import express from 'express';
import { body, query, validationResult } from 'express-validator';
import { PrismaClient, OrderType, OrderSide, TimeInForce } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';
import { asyncHandler } from '../middleware/errorHandler';
import { ValidationError, NotFoundError, AuthorizationError } from '../middleware/errorHandler';
import { kycRequiredMiddleware, emailVerifiedMiddleware } from '../middleware/auth';
import { TradingEngine } from '../services/tradingEngine';
import { logger } from '../utils/logger';

const router = express.Router();
const prisma = new PrismaClient();
const tradingEngine = new TradingEngine();

// Validation rules
const placeOrderValidation = [
  body('tradingPair').notEmpty().withMessage('Trading pair is required'),
  body('type').isIn(['MARKET', 'LIMIT', 'STOP_LOSS', 'TAKE_PROFIT', 'STOP_LIMIT']).withMessage('Invalid order type'),
  body('side').isIn(['BUY', 'SELL']).withMessage('Invalid order side'),
  body('amount').isDecimal({ decimal_digits: '0,8' }).withMessage('Invalid amount'),
  body('price').optional().isDecimal({ decimal_digits: '0,8' }).withMessage('Invalid price'),
  body('stopPrice').optional().isDecimal({ decimal_digits: '0,8' }).withMessage('Invalid stop price'),
  body('timeInForce').optional().isIn(['GTC', 'IOC', 'FOK']).withMessage('Invalid time in force')
];

const getOrdersValidation = [
  query('tradingPair').optional().isString(),
  query('status').optional().isIn(['PENDING', 'PARTIAL_FILLED', 'FILLED', 'CANCELLED', 'REJECTED', 'EXPIRED']),
  query('side').optional().isIn(['BUY', 'SELL']),
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100')
];

// Place order endpoint
router.post('/orders', 
  emailVerifiedMiddleware,
  kycRequiredMiddleware,
  placeOrderValidation,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError(errors.array().map(err => err.msg).join(', '));
    }

    const { tradingPair, type, side, amount, price, stopPrice, timeInForce } = req.body;
    const userId = req.user!.id;

    // Get trading pair
    const tradingPairData = await prisma.tradingPair.findUnique({
      where: { symbol: tradingPair },
      include: {
        baseCurrency: true,
        quoteCurrency: true
      }
    });

    if (!tradingPairData || !tradingPairData.isActive) {
      throw new NotFoundError('Trading pair not found or inactive');
    }

    // Validate order requirements
    if ((type === 'LIMIT' || type === 'STOP_LIMIT') && !price) {
      throw new ValidationError('Price is required for limit orders');
    }

    if ((type === 'STOP_LOSS' || type === 'TAKE_PROFIT' || type === 'STOP_LIMIT') && !stopPrice) {
      throw new ValidationError('Stop price is required for stop orders');
    }

    // Place order through trading engine
    const order = await tradingEngine.placeOrder({
      userId,
      tradingPairId: tradingPairData.id,
      tradingPairSymbol: tradingPairData.symbol,
      type: type as OrderType,
      side: side as OrderSide,
      amount: new Decimal(amount),
      price: price ? new Decimal(price) : undefined,
      stopPrice: stopPrice ? new Decimal(stopPrice) : undefined,
      timeInForce: (timeInForce as TimeInForce) || 'GTC'
    });

    logger.info('Order placed via API', {
      orderId: order.id,
      userId,
      tradingPair,
      type,
      side,
      amount
    });

    res.status(201).json({
      success: true,
      message: 'Order placed successfully',
      data: {
        order: {
          id: order.id,
          tradingPair,
          type: order.type,
          side: order.side,
          amount: order.amount.toString(),
          price: order.price?.toString(),
          stopPrice: order.stopPrice?.toString(),
          filledAmount: order.filledAmount.toString(),
          status: order.status,
          timeInForce: order.timeInForce,
          createdAt: order.createdAt
        }
      }
    });
  })
);

// Get user orders
router.get('/orders',
  getOrdersValidation,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError(errors.array().map(err => err.msg).join(', '));
    }

    const userId = req.user!.id;
    const {
      tradingPair,
      status,
      side,
      page = 1,
      limit = 20
    } = req.query;

    const skip = (Number(page) - 1) * Number(limit);

    // Build where clause
    const where: any = { userId };
    
    if (tradingPair) {
      const tradingPairData = await prisma.tradingPair.findUnique({
        where: { symbol: tradingPair as string }
      });
      if (tradingPairData) {
        where.tradingPairId = tradingPairData.id;
      }
    }
    
    if (status) where.status = status;
    if (side) where.side = side;

    const [orders, total] = await Promise.all([
      prisma.order.findMany({
        where,
        include: {
          tradingPair: {
            include: {
              baseCurrency: true,
              quoteCurrency: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: Number(limit)
      }),
      prisma.order.count({ where })
    ]);

    const formattedOrders = orders.map(order => ({
      id: order.id,
      tradingPair: order.tradingPair.symbol,
      baseCurrency: order.tradingPair.baseCurrency.symbol,
      quoteCurrency: order.tradingPair.quoteCurrency.symbol,
      type: order.type,
      side: order.side,
      amount: order.amount.toString(),
      price: order.price?.toString(),
      stopPrice: order.stopPrice?.toString(),
      filledAmount: order.filledAmount.toString(),
      status: order.status,
      timeInForce: order.timeInForce,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt
    }));

    res.json({
      success: true,
      data: {
        orders: formattedOrders,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit))
        }
      }
    });
  })
);

// Get specific order
router.get('/orders/:orderId',
  asyncHandler(async (req, res) => {
    const { orderId } = req.params;
    const userId = req.user!.id;

    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: {
        tradingPair: {
          include: {
            baseCurrency: true,
            quoteCurrency: true
          }
        },
        trades: {
          orderBy: { createdAt: 'desc' }
        }
      }
    });

    if (!order) {
      throw new NotFoundError('Order not found');
    }

    if (order.userId !== userId) {
      throw new AuthorizationError('Access denied');
    }

    const formattedOrder = {
      id: order.id,
      tradingPair: order.tradingPair.symbol,
      baseCurrency: order.tradingPair.baseCurrency.symbol,
      quoteCurrency: order.tradingPair.quoteCurrency.symbol,
      type: order.type,
      side: order.side,
      amount: order.amount.toString(),
      price: order.price?.toString(),
      stopPrice: order.stopPrice?.toString(),
      filledAmount: order.filledAmount.toString(),
      status: order.status,
      timeInForce: order.timeInForce,
      createdAt: order.createdAt,
      updatedAt: order.updatedAt,
      trades: order.trades.map(trade => ({
        id: trade.id,
        amount: trade.amount.toString(),
        price: trade.price.toString(),
        fee: order.side === 'BUY' ? trade.buyerFee.toString() : trade.sellerFee.toString(),
        createdAt: trade.createdAt
      }))
    };

    res.json({
      success: true,
      data: { order: formattedOrder }
    });
  })
);

// Cancel order
router.delete('/orders/:orderId',
  asyncHandler(async (req, res) => {
    const { orderId } = req.params;
    const userId = req.user!.id;

    await tradingEngine.cancelOrder(orderId, userId);

    logger.info('Order cancelled via API', { orderId, userId });

    res.json({
      success: true,
      message: 'Order cancelled successfully'
    });
  })
);

// Get user trades
router.get('/trades',
  getOrdersValidation,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError(errors.array().map(err => err.msg).join(', '));
    }

    const userId = req.user!.id;
    const {
      tradingPair,
      page = 1,
      limit = 20
    } = req.query;

    const skip = (Number(page) - 1) * Number(limit);

    // Build where clause
    const where: any = {
      OR: [
        { buyUserId: userId },
        { sellUserId: userId }
      ]
    };
    
    if (tradingPair) {
      const tradingPairData = await prisma.tradingPair.findUnique({
        where: { symbol: tradingPair as string }
      });
      if (tradingPairData) {
        where.tradingPairId = tradingPairData.id;
      }
    }

    const [trades, total] = await Promise.all([
      prisma.trade.findMany({
        where,
        include: {
          tradingPair: {
            include: {
              baseCurrency: true,
              quoteCurrency: true
            }
          },
          buyOrder: true,
          sellOrder: true
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: Number(limit)
      }),
      prisma.trade.count({ where })
    ]);

    const formattedTrades = trades.map(trade => {
      const isBuyer = trade.buyUserId === userId;
      const userOrder = isBuyer ? trade.buyOrder : trade.sellOrder;
      
      return {
        id: trade.id,
        tradingPair: trade.tradingPair.symbol,
        baseCurrency: trade.tradingPair.baseCurrency.symbol,
        quoteCurrency: trade.tradingPair.quoteCurrency.symbol,
        side: isBuyer ? 'BUY' : 'SELL',
        amount: trade.amount.toString(),
        price: trade.price.toString(),
        fee: isBuyer ? trade.buyerFee.toString() : trade.sellerFee.toString(),
        orderId: userOrder.id,
        orderType: userOrder.type,
        createdAt: trade.createdAt
      };
    });

    res.json({
      success: true,
      data: {
        trades: formattedTrades,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit))
        }
      }
    });
  })
);

// Get trading statistics
router.get('/stats',
  asyncHandler(async (req, res) => {
    const userId = req.user!.id;

    // Get user trading statistics
    const [
      totalTrades,
      totalVolume,
      totalFees,
      activeOrders
    ] = await Promise.all([
      prisma.trade.count({
        where: {
          OR: [
            { buyUserId: userId },
            { sellUserId: userId }
          ]
        }
      }),
      prisma.trade.aggregate({
        where: {
          OR: [
            { buyUserId: userId },
            { sellUserId: userId }
          ]
        },
        _sum: {
          amount: true
        }
      }),
      prisma.trade.aggregate({
        where: {
          OR: [
            { buyUserId: userId },
            { sellUserId: userId }
          ]
        },
        _sum: {
          buyerFee: true,
          sellerFee: true
        }
      }),
      prisma.order.count({
        where: {
          userId,
          status: {
            in: ['PENDING', 'PARTIAL_FILLED']
          }
        }
      })
    ]);

    const stats = {
      totalTrades,
      totalVolume: totalVolume._sum.amount?.toString() || '0',
      totalFees: {
        buyerFees: totalFees._sum.buyerFee?.toString() || '0',
        sellerFees: totalFees._sum.sellerFee?.toString() || '0'
      },
      activeOrders
    };

    res.json({
      success: true,
      data: { stats }
    });
  })
);

export default router;
