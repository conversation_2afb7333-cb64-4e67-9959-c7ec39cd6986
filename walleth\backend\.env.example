# Environment
NODE_ENV=development
PORT=3001

# Database
DATABASE_URL="postgresql://walleth_user:walleth_password@localhost:5432/walleth_db?schema=public"

# Redis
REDIS_URL="redis://localhost:6379"
REDIS_PASSWORD=""

# MongoDB
MONGODB_URL="mongodb://localhost:27017/walleth_logs"

# JWT
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_EXPIRES_IN="7d"
REFRESH_TOKEN_SECRET="your-refresh-token-secret"
REFRESH_TOKEN_EXPIRES_IN="30d"

# Encryption
ENCRYPTION_KEY="your-32-character-encryption-key-here"
ENCRYPTION_ALGORITHM="aes-256-gcm"

# Email Configuration
SMTP_HOST="smtp.gmail.com"
SMTP_PORT=587
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"
FROM_EMAIL="<EMAIL>"
FROM_NAME="WALLETH Exchange"

# SMS Configuration (Twilio)
TWILIO_ACCOUNT_SID="your-twilio-account-sid"
TWILIO_AUTH_TOKEN="your-twilio-auth-token"
TWILIO_PHONE_NUMBER="+**********"

# Blockchain Configuration
# Bitcoin
BITCOIN_RPC_URL="http://localhost:8332"
BITCOIN_RPC_USER="bitcoin_user"
BITCOIN_RPC_PASS="bitcoin_password"
BITCOIN_NETWORK="testnet"

# Ethereum
ETHEREUM_RPC_URL="https://mainnet.infura.io/v3/your-project-id"
ETHEREUM_PRIVATE_KEY="your-ethereum-private-key"
ETHEREUM_NETWORK="mainnet"

# Binance Smart Chain
BSC_RPC_URL="https://bsc-dataseed.binance.org/"
BSC_PRIVATE_KEY="your-bsc-private-key"

# External APIs
COINMARKETCAP_API_KEY="your-coinmarketcap-api-key"
COINGECKO_API_KEY="your-coingecko-api-key"
BINANCE_API_KEY="your-binance-api-key"
BINANCE_SECRET_KEY="your-binance-secret-key"

# File Upload
MAX_FILE_SIZE="5242880"  # 5MB
UPLOAD_PATH="./uploads"
ALLOWED_FILE_TYPES="image/jpeg,image/png,image/gif,application/pdf"

# Rate Limiting
RATE_LIMIT_WINDOW_MS="900000"  # 15 minutes
RATE_LIMIT_MAX_REQUESTS="100"

# Security
BCRYPT_ROUNDS="12"
SESSION_SECRET="your-session-secret"
CORS_ORIGIN="http://localhost:3000"

# Trading Configuration
MIN_TRADE_AMOUNT="0.00001"
MAX_TRADE_AMOUNT="1000000"
DEFAULT_MAKER_FEE="0.001"  # 0.1%
DEFAULT_TAKER_FEE="0.001"  # 0.1%
WITHDRAWAL_FEE_BTC="0.0005"
WITHDRAWAL_FEE_ETH="0.005"
WITHDRAWAL_FEE_USDT="1"

# KYC Configuration
KYC_REQUIRED_FOR_WITHDRAWAL="true"
MAX_UNVERIFIED_WITHDRAWAL="1000"  # USD equivalent
MAX_VERIFIED_WITHDRAWAL="50000"   # USD equivalent

# Monitoring
SENTRY_DSN="your-sentry-dsn"
LOG_LEVEL="info"

# Admin Configuration
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="change-this-secure-password"

# WebSocket
WS_PORT="3002"
WS_HEARTBEAT_INTERVAL="30000"  # 30 seconds

# Queue Configuration
QUEUE_REDIS_URL="redis://localhost:6379"
QUEUE_CONCURRENCY="5"

# Backup Configuration
BACKUP_ENABLED="true"
BACKUP_INTERVAL="86400000"  # 24 hours
BACKUP_RETENTION_DAYS="30"

# Development
DEBUG="walleth:*"
ENABLE_SWAGGER="true"
ENABLE_PLAYGROUND="true"
