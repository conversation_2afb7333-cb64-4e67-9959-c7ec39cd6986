{"version": 3, "file": "ParseError.js", "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "code", "Object", "setPrototypeOf", "prototype", "name", "constructor", "Error"], "sources": ["../source/ParseError.js"], "sourcesContent": ["// https://stackoverflow.com/a/46971044/970769\r\n// \"Breaking changes in Typescript 2.1\"\r\n// \"Extending built-ins like <PERSON>rror, Array, and Map may no longer work.\"\r\n// \"As a recommendation, you can manually adjust the prototype immediately after any super(...) calls.\"\r\n// https://github.com/Microsoft/TypeScript-wiki/blob/main/Breaking-Changes.md#extending-built-ins-like-error-array-and-map-may-no-longer-work\r\nexport default class ParseError extends Error {\r\n  constructor(code) {\r\n    super(code)\r\n    // Set the prototype explicitly.\r\n    // Any subclass of FooError will have to manually set the prototype as well.\r\n    Object.setPrototypeOf(this, ParseError.prototype)\r\n    this.name = this.constructor.name\r\n  }\r\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;IACqBA,U;;;;;EACnB,oBAAYC,IAAZ,EAAkB;IAAA;;IAAA;;IAChB,0BAAMA,IAAN,EADgB,CAEhB;IACA;;IACAC,MAAM,CAACC,cAAP,gCAA4BH,UAAU,CAACI,SAAvC;IACA,MAAKC,IAAL,GAAY,MAAKC,WAAL,CAAiBD,IAA7B;IALgB;EAMjB;;;iCAPqCE,K"}