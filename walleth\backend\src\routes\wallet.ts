import express from 'express';
import { body, query, validationResult } from 'express-validator';
import { PrismaClient } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';
import { asyncHandler } from '../middleware/errorHandler';
import { ValidationError, NotFoundError, AuthorizationError } from '../middleware/errorHandler';
import { kycRequiredMiddleware, emailVerifiedMiddleware } from '../middleware/auth';
import { logger } from '../utils/logger';

const router = express.Router();
const prisma = new PrismaClient();

// Validation rules
const withdrawValidation = [
  body('currencySymbol').notEmpty().withMessage('Currency symbol is required'),
  body('amount').isDecimal({ decimal_digits: '0,8' }).withMessage('Invalid amount'),
  body('address').notEmpty().withMessage('Withdrawal address is required'),
  body('twoFactorCode').optional().isLength({ min: 6, max: 6 }).withMessage('2FA code must be 6 digits')
];

const getTransactionsValidation = [
  query('type').optional().isIn(['DEPOSIT', 'WITHDRAWAL']).withMessage('Invalid transaction type'),
  query('currency').optional().isString(),
  query('status').optional().isString(),
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100')
];

// Get user wallets
router.get('/balances',
  asyncHandler(async (req, res) => {
    const userId = req.user!.id;

    const wallets = await prisma.wallet.findMany({
      where: { 
        userId,
        isActive: true
      },
      include: {
        currency: {
          select: {
            symbol: true,
            name: true,
            decimals: true,
            iconUrl: true,
            isFiat: true,
            network: true
          }
        }
      },
      orderBy: {
        currency: {
          symbol: 'asc'
        }
      }
    });

    const formattedWallets = wallets.map(wallet => ({
      currency: wallet.currency,
      balance: wallet.balance.toString(),
      lockedBalance: wallet.lockedBalance.toString(),
      availableBalance: new Decimal(wallet.balance).minus(wallet.lockedBalance).toString(),
      address: wallet.address
    }));

    res.json({
      success: true,
      data: { wallets: formattedWallets }
    });
  })
);

// Get specific currency wallet
router.get('/balance/:currencySymbol',
  asyncHandler(async (req, res) => {
    const userId = req.user!.id;
    const { currencySymbol } = req.params;

    const currency = await prisma.currency.findUnique({
      where: { symbol: currencySymbol.toUpperCase() }
    });

    if (!currency) {
      throw new NotFoundError('Currency not found');
    }

    const wallet = await prisma.wallet.findUnique({
      where: {
        userId_currencyId: {
          userId,
          currencyId: currency.id
        }
      },
      include: {
        currency: {
          select: {
            symbol: true,
            name: true,
            decimals: true,
            iconUrl: true,
            isFiat: true,
            network: true,
            minDeposit: true,
            minWithdrawal: true,
            withdrawalFee: true
          }
        }
      }
    });

    if (!wallet) {
      // Create wallet if it doesn't exist
      const newWallet = await prisma.wallet.create({
        data: {
          userId,
          currencyId: currency.id,
          balance: new Decimal(0),
          lockedBalance: new Decimal(0)
        },
        include: {
          currency: {
            select: {
              symbol: true,
              name: true,
              decimals: true,
              iconUrl: true,
              isFiat: true,
              network: true,
              minDeposit: true,
              minWithdrawal: true,
              withdrawalFee: true
            }
          }
        }
      });

      const formattedWallet = {
        currency: newWallet.currency,
        balance: newWallet.balance.toString(),
        lockedBalance: newWallet.lockedBalance.toString(),
        availableBalance: new Decimal(newWallet.balance).minus(newWallet.lockedBalance).toString(),
        address: newWallet.address
      };

      res.json({
        success: true,
        data: { wallet: formattedWallet }
      });
      return;
    }

    const formattedWallet = {
      currency: wallet.currency,
      balance: wallet.balance.toString(),
      lockedBalance: wallet.lockedBalance.toString(),
      availableBalance: new Decimal(wallet.balance).minus(wallet.lockedBalance).toString(),
      address: wallet.address
    };

    res.json({
      success: true,
      data: { wallet: formattedWallet }
    });
  })
);

// Generate deposit address
router.post('/deposit/address/:currencySymbol',
  emailVerifiedMiddleware,
  asyncHandler(async (req, res) => {
    const userId = req.user!.id;
    const { currencySymbol } = req.params;

    const currency = await prisma.currency.findUnique({
      where: { symbol: currencySymbol.toUpperCase() }
    });

    if (!currency || !currency.isActive) {
      throw new NotFoundError('Currency not found or inactive');
    }

    // Get or create wallet
    let wallet = await prisma.wallet.findUnique({
      where: {
        userId_currencyId: {
          userId,
          currencyId: currency.id
        }
      }
    });

    if (!wallet) {
      wallet = await prisma.wallet.create({
        data: {
          userId,
          currencyId: currency.id,
          balance: new Decimal(0),
          lockedBalance: new Decimal(0)
        }
      });
    }

    // Generate address if not exists
    if (!wallet.address) {
      // TODO: Implement actual address generation for different cryptocurrencies
      // For now, generate a mock address
      const mockAddress = this.generateMockAddress(currency.symbol, currency.network);
      
      wallet = await prisma.wallet.update({
        where: { id: wallet.id },
        data: { address: mockAddress }
      });

      logger.info('Deposit address generated', {
        userId,
        currency: currency.symbol,
        address: mockAddress
      });
    }

    res.json({
      success: true,
      data: {
        currency: currency.symbol,
        address: wallet.address,
        network: currency.network,
        minDeposit: currency.minDeposit.toString(),
        confirmations: currency.confirmations
      }
    });
  })
);

// Mock address generation function
function generateMockAddress(symbol: string, network: string | null): string {
  const prefixes: { [key: string]: string } = {
    'BTC': '1',
    'ETH': '0x',
    'USDT': '0x',
    'BNB': 'bnb',
    'ADA': 'addr',
    'SOL': '',
    'DOT': '1',
    'LINK': '0x',
    'LTC': 'L',
    'BCH': 'q',
    'XRP': 'r'
  };

  const prefix = prefixes[symbol] || '0x';
  const randomPart = Math.random().toString(36).substring(2, 15) + 
                    Math.random().toString(36).substring(2, 15);
  
  return prefix + randomPart;
}

// Request withdrawal
router.post('/withdraw',
  emailVerifiedMiddleware,
  kycRequiredMiddleware,
  withdrawValidation,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError(errors.array().map(err => err.msg).join(', '));
    }

    const userId = req.user!.id;
    const { currencySymbol, amount, address, twoFactorCode } = req.body;

    // Get currency
    const currency = await prisma.currency.findUnique({
      where: { symbol: currencySymbol.toUpperCase() }
    });

    if (!currency || !currency.isActive) {
      throw new NotFoundError('Currency not found or inactive');
    }

    // Validate amount
    const withdrawAmount = new Decimal(amount);
    if (withdrawAmount.lessThan(currency.minWithdrawal)) {
      throw new ValidationError(`Minimum withdrawal amount is ${currency.minWithdrawal} ${currency.symbol}`);
    }

    // Get user wallet
    const wallet = await prisma.wallet.findUnique({
      where: {
        userId_currencyId: {
          userId,
          currencyId: currency.id
        }
      }
    });

    if (!wallet) {
      throw new NotFoundError('Wallet not found');
    }

    // Check balance
    const totalAmount = withdrawAmount.plus(currency.withdrawalFee);
    const availableBalance = new Decimal(wallet.balance).minus(wallet.lockedBalance);
    
    if (availableBalance.lessThan(totalAmount)) {
      throw new ValidationError('Insufficient balance');
    }

    // TODO: Verify 2FA if enabled
    if (req.user!.is2FAEnabled && !twoFactorCode) {
      throw new ValidationError('2FA code is required');
    }

    // Create withdrawal request
    const withdrawal = await prisma.withdrawal.create({
      data: {
        userId,
        walletId: wallet.id,
        currencyId: currency.id,
        amount: withdrawAmount,
        fee: currency.withdrawalFee,
        address,
        status: 'PENDING'
      }
    });

    // Lock the funds
    await prisma.wallet.update({
      where: { id: wallet.id },
      data: {
        lockedBalance: new Decimal(wallet.lockedBalance).plus(totalAmount)
      }
    });

    logger.info('Withdrawal requested', {
      withdrawalId: withdrawal.id,
      userId,
      currency: currency.symbol,
      amount: withdrawAmount.toString(),
      address
    });

    res.status(201).json({
      success: true,
      message: 'Withdrawal request submitted successfully',
      data: {
        withdrawal: {
          id: withdrawal.id,
          currency: currency.symbol,
          amount: withdrawal.amount.toString(),
          fee: withdrawal.fee.toString(),
          address: withdrawal.address,
          status: withdrawal.status,
          createdAt: withdrawal.createdAt
        }
      }
    });
  })
);

// Get transaction history
router.get('/transactions',
  getTransactionsValidation,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError(errors.array().map(err => err.msg).join(', '));
    }

    const userId = req.user!.id;
    const {
      type,
      currency,
      status,
      page = 1,
      limit = 20
    } = req.query;

    const skip = (Number(page) - 1) * Number(limit);

    // Build where clauses for deposits and withdrawals
    const baseWhere = { userId };
    const currencyWhere = currency ? {
      currency: { symbol: currency as string }
    } : {};
    const statusWhere = status ? { status: status as string } : {};

    let transactions: any[] = [];
    let total = 0;

    if (!type || type === 'DEPOSIT') {
      const [deposits, depositCount] = await Promise.all([
        prisma.deposit.findMany({
          where: {
            ...baseWhere,
            ...currencyWhere,
            ...statusWhere
          },
          include: {
            currency: {
              select: {
                symbol: true,
                name: true,
                decimals: true,
                iconUrl: true
              }
            }
          },
          orderBy: { createdAt: 'desc' },
          skip: !type ? skip : 0,
          take: !type ? Number(limit) : undefined
        }),
        prisma.deposit.count({
          where: {
            ...baseWhere,
            ...currencyWhere,
            ...statusWhere
          }
        })
      ]);

      const formattedDeposits = deposits.map(deposit => ({
        id: deposit.id,
        type: 'DEPOSIT',
        currency: deposit.currency,
        amount: deposit.amount.toString(),
        fee: '0',
        address: deposit.address,
        txHash: deposit.txHash,
        confirmations: deposit.confirmations,
        requiredConfirmations: deposit.requiredConfirmations,
        status: deposit.status,
        createdAt: deposit.createdAt,
        updatedAt: deposit.updatedAt
      }));

      transactions = [...transactions, ...formattedDeposits];
      total += depositCount;
    }

    if (!type || type === 'WITHDRAWAL') {
      const [withdrawals, withdrawalCount] = await Promise.all([
        prisma.withdrawal.findMany({
          where: {
            ...baseWhere,
            ...currencyWhere,
            ...statusWhere
          },
          include: {
            currency: {
              select: {
                symbol: true,
                name: true,
                decimals: true,
                iconUrl: true
              }
            }
          },
          orderBy: { createdAt: 'desc' },
          skip: !type ? 0 : skip,
          take: !type ? undefined : Number(limit)
        }),
        prisma.withdrawal.count({
          where: {
            ...baseWhere,
            ...currencyWhere,
            ...statusWhere
          }
        })
      ]);

      const formattedWithdrawals = withdrawals.map(withdrawal => ({
        id: withdrawal.id,
        type: 'WITHDRAWAL',
        currency: withdrawal.currency,
        amount: withdrawal.amount.toString(),
        fee: withdrawal.fee.toString(),
        address: withdrawal.address,
        txHash: withdrawal.txHash,
        confirmations: null,
        requiredConfirmations: null,
        status: withdrawal.status,
        rejectionReason: withdrawal.rejectionReason,
        createdAt: withdrawal.createdAt,
        updatedAt: withdrawal.updatedAt,
        processedAt: withdrawal.processedAt
      }));

      transactions = [...transactions, ...formattedWithdrawals];
      total += withdrawalCount;
    }

    // Sort by creation date if both types are included
    if (!type) {
      transactions.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
      transactions = transactions.slice(skip, skip + Number(limit));
    }

    res.json({
      success: true,
      data: {
        transactions,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit))
        }
      }
    });
  })
);

// Get specific transaction
router.get('/transactions/:transactionId',
  asyncHandler(async (req, res) => {
    const userId = req.user!.id;
    const { transactionId } = req.params;

    // Try to find in deposits first
    let transaction = await prisma.deposit.findUnique({
      where: { id: transactionId },
      include: {
        currency: {
          select: {
            symbol: true,
            name: true,
            decimals: true,
            iconUrl: true
          }
        }
      }
    });

    if (transaction && transaction.userId === userId) {
      const formattedTransaction = {
        id: transaction.id,
        type: 'DEPOSIT',
        currency: transaction.currency,
        amount: transaction.amount.toString(),
        fee: '0',
        address: transaction.address,
        txHash: transaction.txHash,
        confirmations: transaction.confirmations,
        requiredConfirmations: transaction.requiredConfirmations,
        status: transaction.status,
        createdAt: transaction.createdAt,
        updatedAt: transaction.updatedAt
      };

      res.json({
        success: true,
        data: { transaction: formattedTransaction }
      });
      return;
    }

    // Try to find in withdrawals
    const withdrawal = await prisma.withdrawal.findUnique({
      where: { id: transactionId },
      include: {
        currency: {
          select: {
            symbol: true,
            name: true,
            decimals: true,
            iconUrl: true
          }
        }
      }
    });

    if (withdrawal && withdrawal.userId === userId) {
      const formattedTransaction = {
        id: withdrawal.id,
        type: 'WITHDRAWAL',
        currency: withdrawal.currency,
        amount: withdrawal.amount.toString(),
        fee: withdrawal.fee.toString(),
        address: withdrawal.address,
        txHash: withdrawal.txHash,
        status: withdrawal.status,
        rejectionReason: withdrawal.rejectionReason,
        createdAt: withdrawal.createdAt,
        updatedAt: withdrawal.updatedAt,
        processedAt: withdrawal.processedAt
      };

      res.json({
        success: true,
        data: { transaction: formattedTransaction }
      });
      return;
    }

    throw new NotFoundError('Transaction not found');
  })
);

export default router;
