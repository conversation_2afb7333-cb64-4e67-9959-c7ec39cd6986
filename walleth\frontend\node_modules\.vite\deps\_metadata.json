{"hash": "d372ab09", "configHash": "c1f56024", "lockfileHash": "9be34958", "browserHash": "99a5196e", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "06a03b47", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "a6d9cab4", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "d800946e", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "b542b94a", "needsInterop": true}, "@mui/icons-material": {"src": "../../@mui/icons-material/esm/index.js", "file": "@mui_icons-material.js", "fileHash": "e331567c", "needsInterop": false}, "@mui/material": {"src": "../../@mui/material/index.js", "file": "@mui_material.js", "fileHash": "9d11886f", "needsInterop": false}, "@mui/material/styles": {"src": "../../@mui/material/styles/index.js", "file": "@mui_material_styles.js", "fileHash": "6e7ca5df", "needsInterop": false}, "@reduxjs/toolkit": {"src": "../../@reduxjs/toolkit/dist/redux-toolkit.modern.mjs", "file": "@reduxjs_toolkit.js", "fileHash": "6ba8a995", "needsInterop": false}, "axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "dd03ecd8", "needsInterop": false}, "date-fns": {"src": "../../date-fns/index.mjs", "file": "date-fns.js", "fileHash": "1f562b9d", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "b21c2dc4", "needsInterop": true}, "react-hook-form": {"src": "../../react-hook-form/dist/index.esm.mjs", "file": "react-hook-form.js", "fileHash": "6c32cc48", "needsInterop": false}, "react-hot-toast": {"src": "../../react-hot-toast/dist/index.mjs", "file": "react-hot-toast.js", "fileHash": "e076136c", "needsInterop": false}, "react-redux": {"src": "../../react-redux/dist/react-redux.mjs", "file": "react-redux.js", "fileHash": "4d50131d", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "287b265d", "needsInterop": false}, "redux-persist": {"src": "../../redux-persist/es/index.js", "file": "redux-persist.js", "fileHash": "e2e5d3ac", "needsInterop": false}, "redux-persist/lib/storage": {"src": "../../redux-persist/lib/storage/index.js", "file": "redux-persist_lib_storage.js", "fileHash": "8b4a30e6", "needsInterop": true}, "socket.io-client": {"src": "../../socket.io-client/build/esm/index.js", "file": "socket__io-client.js", "fileHash": "176e660e", "needsInterop": false}}, "chunks": {"chunk-YAQ2H3LO": {"file": "chunk-YAQ2H3LO.js"}, "chunk-WRD5HZVH": {"file": "chunk-WRD5HZVH.js"}, "chunk-4JFGIWRW": {"file": "chunk-4JFGIWRW.js"}, "chunk-R2INDF4K": {"file": "chunk-R2INDF4K.js"}, "chunk-OT5EQO2H": {"file": "chunk-OT5EQO2H.js"}, "chunk-OU5AQDZK": {"file": "chunk-OU5AQDZK.js"}, "chunk-RJJ6DPM5": {"file": "chunk-RJJ6DPM5.js"}, "chunk-EWTE5DHJ": {"file": "chunk-EWTE5DHJ.js"}}}