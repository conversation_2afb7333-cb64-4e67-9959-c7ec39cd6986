import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { Box, Typography, Button } from '@mui/material';
import { Lock as LockIcon } from '@mui/icons-material';

// Store
import { RootState } from '@/types';

// Components
import LoadingScreen from '../UI/LoadingScreen';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: 'USER' | 'ADMIN' | 'SUPER_ADMIN';
  requireKYC?: boolean;
  requireEmailVerification?: boolean;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRole,
  requireKYC = false,
  requireEmailVerification = false,
}) => {
  const location = useLocation();
  const { user, isAuthenticated, isLoading } = useSelector((state: RootState) => state.auth);

  // Show loading screen while checking authentication
  if (isLoading) {
    return <LoadingScreen message="Verifying authentication..." />;
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated || !user) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Check role-based access
  if (requiredRole) {
    const hasRequiredRole = () => {
      switch (requiredRole) {
        case 'SUPER_ADMIN':
          return user.role === 'SUPER_ADMIN';
        case 'ADMIN':
          return user.role === 'ADMIN' || user.role === 'SUPER_ADMIN';
        case 'USER':
          return true; // All authenticated users have USER access
        default:
          return false;
      }
    };

    if (!hasRequiredRole()) {
      return (
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100vh',
            textAlign: 'center',
            p: 3,
          }}
        >
          <LockIcon sx={{ fontSize: 64, color: 'error.main', mb: 2 }} />
          <Typography variant="h4" gutterBottom>
            Access Denied
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 3, maxWidth: 400 }}>
            You don't have permission to access this page. 
            {requiredRole === 'ADMIN' && ' Administrator privileges are required.'}
            {requiredRole === 'SUPER_ADMIN' && ' Super administrator privileges are required.'}
          </Typography>
          <Button
            variant="contained"
            onClick={() => window.history.back()}
          >
            Go Back
          </Button>
        </Box>
      );
    }
  }

  // Check email verification requirement
  if (requireEmailVerification && !user.isEmailVerified) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100vh',
          textAlign: 'center',
          p: 3,
        }}
      >
        <Typography variant="h4" gutterBottom>
          Email Verification Required
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 3, maxWidth: 400 }}>
          Please verify your email address to access this feature. 
          Check your inbox for a verification link.
        </Typography>
        <Button
          variant="contained"
          onClick={() => window.location.href = '/profile'}
        >
          Go to Profile
        </Button>
      </Box>
    );
  }

  // Check KYC requirement
  if (requireKYC && user.kycStatus !== 'APPROVED') {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100vh',
          textAlign: 'center',
          p: 3,
        }}
      >
        <Typography variant="h4" gutterBottom>
          KYC Verification Required
        </Typography>
        <Typography variant="body1" color="text.secondary" sx={{ mb: 3, maxWidth: 400 }}>
          {user.kycStatus === 'PENDING' && 'Please complete your KYC verification to access this feature.'}
          {user.kycStatus === 'SUBMITTED' && 'Your KYC verification is under review. Please wait for approval.'}
          {user.kycStatus === 'REJECTED' && 'Your KYC verification was rejected. Please resubmit your documents.'}
          {user.kycStatus === 'EXPIRED' && 'Your KYC verification has expired. Please submit new documents.'}
        </Typography>
        <Button
          variant="contained"
          onClick={() => window.location.href = '/profile'}
        >
          Complete KYC
        </Button>
      </Box>
    );
  }

  // All checks passed, render the protected content
  return <>{children}</>;
};

export default ProtectedRoute;
