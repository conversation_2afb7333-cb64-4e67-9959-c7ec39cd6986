export interface InputClasses {
    /** Styles applied to the root element. */
    root: string;
    /** Styles applied to the root element if the component is a descendant of `FormControl`. */
    formControl: string;
    /** Styles applied to the root element if the component is focused. */
    focused: string;
    /** Styles applied to the root element if `disabled={true}`. */
    disabled: string;
    /** Styles applied to the root element if color secondary. */
    colorSecondary: string;
    /** Styles applied to the root element unless `disableUnderline={true}`. */
    underline: string;
    /** State class applied to the root element if `error={true}`. */
    error: string;
    /** Styles applied to the input element if `size="small"`. */
    sizeSmall: string;
    /** Styles applied to the root element if `multiline={true}`. */
    multiline: string;
    /** Styles applied to the root element if `fullWidth={true}`. */
    fullWidth: string;
    /** Styles applied to the input element. */
    input: string;
    /** Styles applied to the input element if `size="small"`. */
    inputSizeSmall: string;
    /** Styles applied to the input element if `multiline={true}`. */
    inputMultiline: string;
    /** Styles applied to the input element if `startAdornment` is provided. */
    inputAdornedStart: string;
    /** Styles applied to the input element if `endAdornment` is provided. */
    inputAdornedEnd: string;
    /** Styles applied to the input element if `type="search"`. */
    inputTypeSearch: string;
}
export type InputClassKey = keyof InputClasses;
export declare function getInputUtilityClass(slot: string): string;
declare const inputClasses: InputClasses;
export default inputClasses;
