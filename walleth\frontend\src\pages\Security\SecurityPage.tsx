import React, { useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useForm } from 'react-hook-form';
import {
  Box,
  Grid,
  Paper,
  Typography,
  TextField,
  Button,
  Card,
  CardContent,
  Switch,
  FormControlLabel,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  Divider,
  IconButton,
} from '@mui/material';
import {
  Security as SecurityIcon,
  Lock as LockIcon,
  Smartphone as PhoneIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Shield as ShieldIcon,
  Key as KeyIcon,
  History as HistoryIcon,
  Warning as WarningIcon,
  CheckCircle as CheckIcon,
} from '@mui/icons-material';

// Store
import { RootState } from '@/types';

// Services
import { userAPI } from '@/services/api';

interface PasswordFormData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

const SecurityPage: React.FC = () => {
  const { user } = useSelector((state: RootState) => state.auth);
  
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false,
  });
  const [twoFADialog, setTwoFADialog] = useState(false);
  const [qrCode, setQrCode] = useState('');
  const [backupCodes, setBackupCodes] = useState<string[]>([]);

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    reset,
  } = useForm<PasswordFormData>();

  const newPassword = watch('newPassword');

  const onSubmitPassword = async (data: PasswordFormData) => {
    try {
      await userAPI.changePassword({
        currentPassword: data.currentPassword,
        newPassword: data.newPassword,
      });
      reset();
      // Show success message
    } catch (error) {
      console.error('Failed to change password:', error);
    }
  };

  const handleSetup2FA = async () => {
    try {
      const response = await userAPI.setup2FA();
      setQrCode(response.data.qrCode);
      setBackupCodes(response.data.backupCodes);
      setTwoFADialog(true);
    } catch (error) {
      console.error('Failed to setup 2FA:', error);
    }
  };

  const handleDisable2FA = async () => {
    // Implementation for disabling 2FA
  };

  const togglePasswordVisibility = (field: keyof typeof showPasswords) => {
    setShowPasswords(prev => ({
      ...prev,
      [field]: !prev[field],
    }));
  };

  const getPasswordStrength = (password: string) => {
    if (!password) return { strength: 0, label: 'No password', color: 'error' };
    
    let strength = 0;
    if (password.length >= 8) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/\d/.test(password)) strength++;
    if (/[^a-zA-Z\d]/.test(password)) strength++;

    const labels = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong'];
    const colors = ['error', 'error', 'warning', 'info', 'success'];
    
    return {
      strength: (strength / 5) * 100,
      label: labels[strength - 1] || 'Very Weak',
      color: colors[strength - 1] || 'error',
    };
  };

  const passwordStrength = getPasswordStrength(newPassword || '');

  if (!user) {
    return null;
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom>
          Security Settings
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Protect your CryptoNest account with advanced security features
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* Security Overview */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <ShieldIcon sx={{ color: 'primary.main', mr: 1 }} />
                <Typography variant="h6">Security Score</Typography>
              </Box>
              
              <Box sx={{ textAlign: 'center', mb: 3 }}>
                <Typography variant="h2" color="primary.main">
                  {user.is2FAEnabled ? '85' : '65'}%
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {user.is2FAEnabled ? 'Good Security' : 'Needs Improvement'}
                </Typography>
              </Box>

              <List dense>
                <ListItem>
                  <ListItemIcon>
                    <CheckIcon color="success" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Email Verified"
                    secondary={user.isEmailVerified ? 'Verified' : 'Not verified'}
                  />
                </ListItem>

                <ListItem>
                  <ListItemIcon>
                    {user.is2FAEnabled ? (
                      <CheckIcon color="success" />
                    ) : (
                      <WarningIcon color="warning" />
                    )}
                  </ListItemIcon>
                  <ListItemText 
                    primary="Two-Factor Authentication"
                    secondary={user.is2FAEnabled ? 'Enabled' : 'Disabled'}
                  />
                </ListItem>

                <ListItem>
                  <ListItemIcon>
                    <CheckIcon color="success" />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Strong Password"
                    secondary="Last changed recently"
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Security Settings */}
        <Grid item xs={12} md={8}>
          <Grid container spacing={3}>
            {/* Change Password */}
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                    <LockIcon sx={{ color: 'primary.main', mr: 1 }} />
                    <Typography variant="h6">Change Password</Typography>
                  </Box>

                  <Box component="form" onSubmit={handleSubmit(onSubmitPassword)}>
                    <Grid container spacing={2}>
                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          label="Current Password"
                          type={showPasswords.current ? 'text' : 'password'}
                          {...register('currentPassword', {
                            required: 'Current password is required',
                          })}
                          error={!!errors.currentPassword}
                          helperText={errors.currentPassword?.message}
                          InputProps={{
                            endAdornment: (
                              <IconButton
                                onClick={() => togglePasswordVisibility('current')}
                                edge="end"
                              >
                                {showPasswords.current ? <VisibilityOffIcon /> : <VisibilityIcon />}
                              </IconButton>
                            ),
                          }}
                        />
                      </Grid>

                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          label="New Password"
                          type={showPasswords.new ? 'text' : 'password'}
                          {...register('newPassword', {
                            required: 'New password is required',
                            minLength: {
                              value: 8,
                              message: 'Password must be at least 8 characters',
                            },
                            pattern: {
                              value: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
                              message: 'Password must contain uppercase, lowercase, number, and special character',
                            },
                          })}
                          error={!!errors.newPassword}
                          helperText={errors.newPassword?.message || `Strength: ${passwordStrength.label}`}
                          InputProps={{
                            endAdornment: (
                              <IconButton
                                onClick={() => togglePasswordVisibility('new')}
                                edge="end"
                              >
                                {showPasswords.new ? <VisibilityOffIcon /> : <VisibilityIcon />}
                              </IconButton>
                            ),
                          }}
                        />
                      </Grid>

                      <Grid item xs={12}>
                        <TextField
                          fullWidth
                          label="Confirm New Password"
                          type={showPasswords.confirm ? 'text' : 'password'}
                          {...register('confirmPassword', {
                            required: 'Please confirm your password',
                            validate: (value) =>
                              value === newPassword || 'Passwords do not match',
                          })}
                          error={!!errors.confirmPassword}
                          helperText={errors.confirmPassword?.message}
                          InputProps={{
                            endAdornment: (
                              <IconButton
                                onClick={() => togglePasswordVisibility('confirm')}
                                edge="end"
                              >
                                {showPasswords.confirm ? <VisibilityOffIcon /> : <VisibilityIcon />}
                              </IconButton>
                            ),
                          }}
                        />
                      </Grid>

                      <Grid item xs={12}>
                        <Button
                          type="submit"
                          variant="contained"
                          sx={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' }}
                        >
                          Update Password
                        </Button>
                      </Grid>
                    </Grid>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            {/* Two-Factor Authentication */}
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <PhoneIcon sx={{ color: 'primary.main', mr: 1 }} />
                      <Typography variant="h6">Two-Factor Authentication</Typography>
                    </Box>
                    <Chip
                      label={user.is2FAEnabled ? 'Enabled' : 'Disabled'}
                      color={user.is2FAEnabled ? 'success' : 'error'}
                      size="small"
                    />
                  </Box>

                  <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                    Add an extra layer of security to your account with 2FA using an authenticator app.
                  </Typography>

                  {user.is2FAEnabled ? (
                    <Box>
                      <Alert severity="success" sx={{ mb: 2 }}>
                        Two-factor authentication is enabled and protecting your account.
                      </Alert>
                      <Button
                        variant="outlined"
                        color="error"
                        onClick={handleDisable2FA}
                      >
                        Disable 2FA
                      </Button>
                    </Box>
                  ) : (
                    <Box>
                      <Alert severity="warning" sx={{ mb: 2 }}>
                        Your account is not protected by two-factor authentication.
                      </Alert>
                      <Button
                        variant="contained"
                        onClick={handleSetup2FA}
                        sx={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' }}
                      >
                        Enable 2FA
                      </Button>
                    </Box>
                  )}
                </CardContent>
              </Card>
            </Grid>

            {/* Login History */}
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
                    <HistoryIcon sx={{ color: 'primary.main', mr: 1 }} />
                    <Typography variant="h6">Recent Login Activity</Typography>
                  </Box>

                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    Monitor your account access and detect any suspicious activity.
                  </Typography>

                  <Button variant="outlined">
                    View Full Login History
                  </Button>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Grid>
      </Grid>

      {/* 2FA Setup Dialog */}
      <Dialog open={twoFADialog} onClose={() => setTwoFADialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Setup Two-Factor Authentication</DialogTitle>
        <DialogContent>
          <Typography variant="body2" gutterBottom>
            1. Install an authenticator app like Google Authenticator or Authy
          </Typography>
          <Typography variant="body2" gutterBottom>
            2. Scan the QR code below with your authenticator app
          </Typography>
          <Typography variant="body2" sx={{ mb: 3 }}>
            3. Enter the 6-digit code from your app to verify
          </Typography>

          {qrCode && (
            <Box sx={{ textAlign: 'center', mb: 3 }}>
              <img src={qrCode} alt="2FA QR Code" style={{ maxWidth: '200px' }} />
            </Box>
          )}

          <TextField
            fullWidth
            label="Verification Code"
            placeholder="Enter 6-digit code"
            sx={{ mb: 3 }}
          />

          {backupCodes.length > 0 && (
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                Backup Codes (Save these securely):
              </Typography>
              <Box sx={{ backgroundColor: 'action.hover', p: 2, borderRadius: 1 }}>
                {backupCodes.map((code, index) => (
                  <Typography key={index} variant="body2" fontFamily="monospace">
                    {code}
                  </Typography>
                ))}
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setTwoFADialog(false)}>Cancel</Button>
          <Button variant="contained">Verify & Enable</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default SecurityPage;
