{"version": 3, "file": "parsePhoneNumber_.js", "names": ["parsePhoneNumberWithError", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isSupportedCountry", "parsePhoneNumber", "text", "options", "metadata", "defaultCountry", "undefined", "error"], "sources": ["../source/parsePhoneNumber_.js"], "sourcesContent": ["import parsePhoneNumberWithError from './parsePhoneNumberWithError_.js'\r\nimport ParseError from './ParseError.js'\r\nimport { isSupportedCountry } from './metadata.js'\r\n\r\nexport default function parsePhoneNumber(text, options, metadata) {\r\n\t// Validate `defaultCountry`.\r\n\tif (options && options.defaultCountry && !isSupportedCountry(options.defaultCountry, metadata)) {\r\n\t\toptions = {\r\n\t\t\t...options,\r\n\t\t\tdefaultCountry: undefined\r\n\t\t}\r\n\t}\r\n\t// Parse phone number.\r\n\ttry {\r\n\t\treturn parsePhoneNumberWithError(text, options, metadata)\r\n\t} catch (error) {\r\n\t\t/* istanbul ignore else */\r\n\t\tif (error instanceof ParseError) {\r\n\t\t\t//\r\n\t\t} else {\r\n\t\t\tthrow error\r\n\t\t}\r\n\t}\r\n}\r\n"], "mappings": ";;;;;;AAAA,OAAOA,yBAAP,MAAsC,iCAAtC;AACA,OAAOC,UAAP,MAAuB,iBAAvB;AACA,SAASC,kBAAT,QAAmC,eAAnC;AAEA,eAAe,SAASC,gBAAT,CAA0BC,IAA1B,EAAgCC,OAAhC,EAAyCC,QAAzC,EAAmD;EACjE;EACA,IAAID,OAAO,IAAIA,OAAO,CAACE,cAAnB,IAAqC,CAACL,kBAAkB,CAACG,OAAO,CAACE,cAAT,EAAyBD,QAAzB,CAA5D,EAAgG;IAC/FD,OAAO,mCACHA,OADG;MAENE,cAAc,EAAEC;IAFV,EAAP;EAIA,CAPgE,CAQjE;;;EACA,IAAI;IACH,OAAOR,yBAAyB,CAACI,IAAD,EAAOC,OAAP,EAAgBC,QAAhB,CAAhC;EACA,CAFD,CAEE,OAAOG,KAAP,EAAc;IACf;IACA,IAAIA,KAAK,YAAYR,UAArB,EAAiC,CAChC;IACA,CAFD,MAEO;MACN,MAAMQ,KAAN;IACA;EACD;AACD"}