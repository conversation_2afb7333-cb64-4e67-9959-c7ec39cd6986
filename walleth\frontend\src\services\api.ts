import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { 
  LoginCredentials, 
  RegisterData, 
  PlaceOrderData,
  ApiResponse,
  ChartInterval 
} from '@/types';

// Create axios instance
const api: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:3001/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('accessToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle token refresh
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;
    
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      
      try {
        const refreshToken = localStorage.getItem('refreshToken');
        if (refreshToken) {
          const response = await axios.post(
            `${import.meta.env.VITE_API_URL}/auth/refresh`,
            { refreshToken }
          );
          
          const { accessToken, refreshToken: newRefreshToken } = response.data.data;
          localStorage.setItem('accessToken', accessToken);
          localStorage.setItem('refreshToken', newRefreshToken);
          
          // Retry original request with new token
          originalRequest.headers.Authorization = `Bearer ${accessToken}`;
          return api(originalRequest);
        }
      } catch (refreshError) {
        // Refresh failed, redirect to login
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }
    
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: (credentials: LoginCredentials): Promise<AxiosResponse<ApiResponse>> =>
    api.post('/auth/login', credentials),
    
  register: (userData: RegisterData): Promise<AxiosResponse<ApiResponse>> =>
    api.post('/auth/register', userData),
    
  logout: (): Promise<AxiosResponse<ApiResponse>> =>
    api.post('/auth/logout'),
    
  refreshToken: (refreshToken: string): Promise<AxiosResponse<ApiResponse>> =>
    api.post('/auth/refresh', { refreshToken }),
    
  getCurrentUser: (): Promise<AxiosResponse<ApiResponse>> =>
    api.get('/auth/me'),
};

// User API
export const userAPI = {
  getProfile: (): Promise<AxiosResponse<ApiResponse>> =>
    api.get('/user/profile'),
    
  updateProfile: (data: any): Promise<AxiosResponse<ApiResponse>> =>
    api.put('/user/profile', data),
    
  changePassword: (data: { currentPassword: string; newPassword: string }): Promise<AxiosResponse<ApiResponse>> =>
    api.put('/user/password', data),
    
  setup2FA: (): Promise<AxiosResponse<ApiResponse>> =>
    api.post('/user/2fa/setup'),
    
  verify2FA: (token: string): Promise<AxiosResponse<ApiResponse>> =>
    api.post('/user/2fa/verify', { token }),
    
  disable2FA: (token: string, password: string): Promise<AxiosResponse<ApiResponse>> =>
    api.post('/user/2fa/disable', { token, password }),
    
  getNotifications: (page?: number, limit?: number): Promise<AxiosResponse<ApiResponse>> =>
    api.get('/user/notifications', { params: { page, limit } }),
    
  markNotificationAsRead: (notificationId: string): Promise<AxiosResponse<ApiResponse>> =>
    api.put(`/user/notifications/${notificationId}/read`),
    
  markAllNotificationsAsRead: (): Promise<AxiosResponse<ApiResponse>> =>
    api.put('/user/notifications/read-all'),
    
  getReferralInfo: (): Promise<AxiosResponse<ApiResponse>> =>
    api.get('/user/referral'),
    
  getLoginHistory: (page?: number, limit?: number): Promise<AxiosResponse<ApiResponse>> =>
    api.get('/user/login-history', { params: { page, limit } }),
};

// Market API
export const marketAPI = {
  getTradingPairs: (): Promise<AxiosResponse<ApiResponse>> =>
    api.get('/market/pairs'),
    
  getTicker: (symbol?: string): Promise<AxiosResponse<ApiResponse>> =>
    symbol ? api.get(`/market/ticker/${symbol}`) : api.get('/market/ticker'),
    
  getOrderBook: (symbol: string, depth?: number): Promise<AxiosResponse<ApiResponse>> =>
    api.get(`/market/orderbook/${symbol}`, { params: { depth } }),
    
  getRecentTrades: (symbol: string, limit?: number): Promise<AxiosResponse<ApiResponse>> =>
    api.get(`/market/trades/${symbol}`, { params: { limit } }),
    
  getCandlestickData: (symbol: string, interval: ChartInterval, limit?: number): Promise<AxiosResponse<ApiResponse>> =>
    api.get(`/market/klines/${symbol}`, { params: { interval, limit } }),
    
  getMarketStats: (): Promise<AxiosResponse<ApiResponse>> =>
    api.get('/market/stats/24hr'),
    
  getMarketSummary: (): Promise<AxiosResponse<ApiResponse>> =>
    api.get('/market/summary'),
    
  getCurrencies: (): Promise<AxiosResponse<ApiResponse>> =>
    api.get('/market/currencies'),
};

// Trading API
export const tradingAPI = {
  placeOrder: (orderData: PlaceOrderData): Promise<AxiosResponse<ApiResponse>> =>
    api.post('/trading/orders', orderData),
    
  cancelOrder: (orderId: string): Promise<AxiosResponse<ApiResponse>> =>
    api.delete(`/trading/orders/${orderId}`),
    
  getOrders: (params?: any): Promise<AxiosResponse<ApiResponse>> =>
    api.get('/trading/orders', { params }),
    
  getOrderDetails: (orderId: string): Promise<AxiosResponse<ApiResponse>> =>
    api.get(`/trading/orders/${orderId}`),
    
  getTrades: (params?: any): Promise<AxiosResponse<ApiResponse>> =>
    api.get('/trading/trades', { params }),
    
  getTradingStats: (): Promise<AxiosResponse<ApiResponse>> =>
    api.get('/trading/stats'),
};

// Wallet API
export const walletAPI = {
  getWallets: (): Promise<AxiosResponse<ApiResponse>> =>
    api.get('/wallet/balances'),
    
  getWalletBalance: (currencySymbol: string): Promise<AxiosResponse<ApiResponse>> =>
    api.get(`/wallet/balance/${currencySymbol}`),
    
  generateDepositAddress: (currencySymbol: string): Promise<AxiosResponse<ApiResponse>> =>
    api.post(`/wallet/deposit/address/${currencySymbol}`),
    
  requestWithdrawal: (data: {
    currencySymbol: string;
    amount: string;
    address: string;
    twoFactorCode?: string;
  }): Promise<AxiosResponse<ApiResponse>> =>
    api.post('/wallet/withdraw', data),
    
  getTransactions: (params?: any): Promise<AxiosResponse<ApiResponse>> =>
    api.get('/wallet/transactions', { params }),
    
  getTransactionDetails: (transactionId: string): Promise<AxiosResponse<ApiResponse>> =>
    api.get(`/wallet/transactions/${transactionId}`),
};

// Admin API
export const adminAPI = {
  getDashboardStats: (): Promise<AxiosResponse<ApiResponse>> =>
    api.get('/admin/dashboard'),
    
  getUsers: (params?: any): Promise<AxiosResponse<ApiResponse>> =>
    api.get('/admin/users', { params }),
    
  getUserDetails: (userId: string): Promise<AxiosResponse<ApiResponse>> =>
    api.get(`/admin/users/${userId}`),
    
  updateUserStatus: (userId: string, status: string, reason?: string): Promise<AxiosResponse<ApiResponse>> =>
    api.put(`/admin/users/${userId}/status`, { status, reason }),
    
  getPendingKYC: (params?: any): Promise<AxiosResponse<ApiResponse>> =>
    api.get('/admin/kyc/pending', { params }),
    
  approveKYC: (userId: string, reason?: string): Promise<AxiosResponse<ApiResponse>> =>
    api.put(`/admin/kyc/${userId}/approve`, { reason }),
    
  rejectKYC: (userId: string, reason: string): Promise<AxiosResponse<ApiResponse>> =>
    api.put(`/admin/kyc/${userId}/reject`, { reason }),
    
  getPendingWithdrawals: (params?: any): Promise<AxiosResponse<ApiResponse>> =>
    api.get('/admin/withdrawals/pending', { params }),
    
  approveWithdrawal: (withdrawalId: string, txHash?: string): Promise<AxiosResponse<ApiResponse>> =>
    api.put(`/admin/withdrawals/${withdrawalId}/approve`, { txHash }),
    
  rejectWithdrawal: (withdrawalId: string, reason: string): Promise<AxiosResponse<ApiResponse>> =>
    api.put(`/admin/withdrawals/${withdrawalId}/reject`, { reason }),
    
  createTradingPair: (data: any): Promise<AxiosResponse<ApiResponse>> =>
    api.post('/admin/trading-pairs', data),
    
  getSettings: (): Promise<AxiosResponse<ApiResponse>> =>
    api.get('/admin/settings'),
};

export default api;
