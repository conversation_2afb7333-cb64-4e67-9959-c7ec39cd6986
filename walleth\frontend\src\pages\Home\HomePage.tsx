import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import {
  Box,
  Container,
  Typography,
  Button,
  Grid,
  Card,
  CardContent,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Avatar,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Security as SecurityIcon,
  Speed as SpeedIcon,
  AccountBalance as AccountBalanceIcon,
  Groups as GroupsIcon,
} from '@mui/icons-material';

// Store
import { RootState } from '@/types';
import { fetchTickers, fetchTradingPairs } from '@/store/slices/marketSlice';

const HomePage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  
  const { isAuthenticated, user } = useSelector((state: RootState) => state.auth);
  const { tickers, tradingPairs } = useSelector((state: RootState) => state.market);

  useEffect(() => {
    dispatch(fetchTickers());
    dispatch(fetchTradingPairs());
  }, [dispatch]);

  const topGainers = Object.values(tickers)
    .sort((a, b) => b.change24h - a.change24h)
    .slice(0, 5);

  const topLosers = Object.values(tickers)
    .sort((a, b) => a.change24h - b.change24h)
    .slice(0, 5);

  const formatPrice = (price: number) => {
    return price.toLocaleString('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 8,
    });
  };

  const formatChange = (change: number) => {
    const isPositive = change >= 0;
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
        {isPositive ? (
          <TrendingUpIcon sx={{ color: 'success.main', fontSize: 16 }} />
        ) : (
          <TrendingDownIcon sx={{ color: 'error.main', fontSize: 16 }} />
        )}
        <Typography
          variant="body2"
          sx={{ color: isPositive ? 'success.main' : 'error.main' }}
        >
          {isPositive ? '+' : ''}{change.toFixed(2)}%
        </Typography>
      </Box>
    );
  };

  return (
    <Box sx={{ minHeight: '100vh', backgroundColor: 'background.default' }}>
      {/* Hero Section */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #1976d2 0%, #42a5f5 100%)',
          color: 'white',
          py: 8,
        }}
      >
        <Container maxWidth="lg">
          <Grid container spacing={4} alignItems="center">
            <Grid item xs={12} md={6}>
              <Typography variant="h2" fontWeight={700} gutterBottom>
                Welcome to WALLETH
              </Typography>
              <Typography variant="h5" sx={{ mb: 3, opacity: 0.9 }}>
                The most advanced cryptocurrency exchange platform
              </Typography>
              <Typography variant="body1" sx={{ mb: 4, opacity: 0.8 }}>
                Trade Bitcoin, Ethereum, and hundreds of other cryptocurrencies with 
                professional-grade tools, advanced security, and lightning-fast execution.
              </Typography>
              
              {isAuthenticated ? (
                <Box sx={{ display: 'flex', gap: 2 }}>
                  <Button
                    variant="contained"
                    size="large"
                    onClick={() => navigate('/trading')}
                    sx={{
                      backgroundColor: 'white',
                      color: 'primary.main',
                      '&:hover': { backgroundColor: 'grey.100' },
                    }}
                  >
                    Start Trading
                  </Button>
                  <Button
                    variant="outlined"
                    size="large"
                    onClick={() => navigate('/wallet')}
                    sx={{
                      borderColor: 'white',
                      color: 'white',
                      '&:hover': { borderColor: 'grey.300', backgroundColor: 'rgba(255,255,255,0.1)' },
                    }}
                  >
                    View Wallet
                  </Button>
                </Box>
              ) : (
                <Box sx={{ display: 'flex', gap: 2 }}>
                  <Button
                    variant="contained"
                    size="large"
                    onClick={() => navigate('/register')}
                    sx={{
                      backgroundColor: 'white',
                      color: 'primary.main',
                      '&:hover': { backgroundColor: 'grey.100' },
                    }}
                  >
                    Get Started
                  </Button>
                  <Button
                    variant="outlined"
                    size="large"
                    onClick={() => navigate('/login')}
                    sx={{
                      borderColor: 'white',
                      color: 'white',
                      '&:hover': { borderColor: 'grey.300', backgroundColor: 'rgba(255,255,255,0.1)' },
                    }}
                  >
                    Sign In
                  </Button>
                </Box>
              )}
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Box
                sx={{
                  textAlign: 'center',
                  '& .hero-emoji': {
                    fontSize: '8rem',
                    animation: 'float 3s ease-in-out infinite',
                  },
                  '@keyframes float': {
                    '0%, 100%': { transform: 'translateY(0px)' },
                    '50%': { transform: 'translateY(-20px)' },
                  },
                }}
              >
                <span className="hero-emoji">🚀</span>
              </Box>
            </Grid>
          </Grid>
        </Container>
      </Box>

      {/* Features Section */}
      <Container maxWidth="lg" sx={{ py: 8 }}>
        <Typography variant="h3" textAlign="center" gutterBottom>
          Why Choose WALLETH?
        </Typography>
        <Typography variant="h6" textAlign="center" color="text.secondary" sx={{ mb: 6 }}>
          Built for traders, by traders
        </Typography>

        <Grid container spacing={4}>
          <Grid item xs={12} md={3}>
            <Card sx={{ textAlign: 'center', p: 3, height: '100%' }}>
              <Avatar sx={{ bgcolor: 'primary.main', mx: 'auto', mb: 2, width: 64, height: 64 }}>
                <SecurityIcon sx={{ fontSize: 32 }} />
              </Avatar>
              <Typography variant="h6" gutterBottom>
                Bank-Grade Security
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Multi-layer security with cold storage, 2FA, and advanced encryption
              </Typography>
            </Card>
          </Grid>

          <Grid item xs={12} md={3}>
            <Card sx={{ textAlign: 'center', p: 3, height: '100%' }}>
              <Avatar sx={{ bgcolor: 'success.main', mx: 'auto', mb: 2, width: 64, height: 64 }}>
                <SpeedIcon sx={{ fontSize: 32 }} />
              </Avatar>
              <Typography variant="h6" gutterBottom>
                Lightning Fast
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Ultra-low latency trading engine with real-time market data
              </Typography>
            </Card>
          </Grid>

          <Grid item xs={12} md={3}>
            <Card sx={{ textAlign: 'center', p: 3, height: '100%' }}>
              <Avatar sx={{ bgcolor: 'warning.main', mx: 'auto', mb: 2, width: 64, height: 64 }}>
                <AccountBalanceIcon sx={{ fontSize: 32 }} />
              </Avatar>
              <Typography variant="h6" gutterBottom>
                Low Fees
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Competitive trading fees starting from 0.1% with volume discounts
              </Typography>
            </Card>
          </Grid>

          <Grid item xs={12} md={3}>
            <Card sx={{ textAlign: 'center', p: 3, height: '100%' }}>
              <Avatar sx={{ bgcolor: 'info.main', mx: 'auto', mb: 2, width: 64, height: 64 }}>
                <GroupsIcon sx={{ fontSize: 32 }} />
              </Avatar>
              <Typography variant="h6" gutterBottom>
                24/7 Support
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Round-the-clock customer support from our expert team
              </Typography>
            </Card>
          </Grid>
        </Grid>
      </Container>

      {/* Market Overview */}
      <Box sx={{ backgroundColor: 'background.paper', py: 8 }}>
        <Container maxWidth="lg">
          <Typography variant="h3" textAlign="center" gutterBottom>
            Market Overview
          </Typography>
          <Typography variant="h6" textAlign="center" color="text.secondary" sx={{ mb: 6 }}>
            Real-time cryptocurrency prices
          </Typography>

          <Grid container spacing={4}>
            {/* Top Gainers */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ color: 'success.main' }}>
                    🔥 Top Gainers (24h)
                  </Typography>
                  <TableContainer>
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell>Symbol</TableCell>
                          <TableCell align="right">Price</TableCell>
                          <TableCell align="right">Change</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {topGainers.map((ticker) => (
                          <TableRow key={ticker.symbol}>
                            <TableCell>
                              <Typography variant="body2" fontWeight={600}>
                                {ticker.symbol}
                              </Typography>
                            </TableCell>
                            <TableCell align="right">
                              {formatPrice(ticker.price)}
                            </TableCell>
                            <TableCell align="right">
                              {formatChange(ticker.change24h)}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </CardContent>
              </Card>
            </Grid>

            {/* Top Losers */}
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ color: 'error.main' }}>
                    📉 Top Losers (24h)
                  </Typography>
                  <TableContainer>
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell>Symbol</TableCell>
                          <TableCell align="right">Price</TableCell>
                          <TableCell align="right">Change</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {topLosers.map((ticker) => (
                          <TableRow key={ticker.symbol}>
                            <TableCell>
                              <Typography variant="body2" fontWeight={600}>
                                {ticker.symbol}
                              </Typography>
                            </TableCell>
                            <TableCell align="right">
                              {formatPrice(ticker.price)}
                            </TableCell>
                            <TableCell align="right">
                              {formatChange(ticker.change24h)}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* CTA */}
          <Box sx={{ textAlign: 'center', mt: 6 }}>
            {!isAuthenticated && (
              <Button
                variant="contained"
                size="large"
                onClick={() => navigate('/register')}
                sx={{ px: 4, py: 1.5 }}
              >
                Start Trading Now
              </Button>
            )}
          </Box>
        </Container>
      </Box>

      {/* User Dashboard Preview */}
      {isAuthenticated && user && (
        <Container maxWidth="lg" sx={{ py: 8 }}>
          <Typography variant="h3" textAlign="center" gutterBottom>
            Welcome back, {user.firstName || 'Trader'}!
          </Typography>
          
          <Grid container spacing={4} sx={{ mt: 4 }}>
            <Grid item xs={12} md={4}>
              <Card sx={{ textAlign: 'center', p: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Account Status
                </Typography>
                <Chip
                  label={user.kycStatus}
                  color={user.kycStatus === 'APPROVED' ? 'success' : 'warning'}
                  sx={{ mb: 2 }}
                />
                <Typography variant="body2" color="text.secondary">
                  {user.kycStatus === 'APPROVED' 
                    ? 'Your account is fully verified'
                    : 'Complete KYC to unlock all features'
                  }
                </Typography>
              </Card>
            </Grid>

            <Grid item xs={12} md={4}>
              <Card sx={{ textAlign: 'center', p: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Security
                </Typography>
                <Chip
                  label={user.is2FAEnabled ? '2FA Enabled' : '2FA Disabled'}
                  color={user.is2FAEnabled ? 'success' : 'error'}
                  sx={{ mb: 2 }}
                />
                <Typography variant="body2" color="text.secondary">
                  {user.is2FAEnabled 
                    ? 'Your account is secured with 2FA'
                    : 'Enable 2FA for better security'
                  }
                </Typography>
              </Card>
            </Grid>

            <Grid item xs={12} md={4}>
              <Card sx={{ textAlign: 'center', p: 3 }}>
                <Typography variant="h6" gutterBottom>
                  Quick Actions
                </Typography>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                  <Button
                    variant="outlined"
                    size="small"
                    onClick={() => navigate('/trading')}
                  >
                    Start Trading
                  </Button>
                  <Button
                    variant="outlined"
                    size="small"
                    onClick={() => navigate('/wallet')}
                  >
                    View Wallet
                  </Button>
                </Box>
              </Card>
            </Grid>
          </Grid>
        </Container>
      )}
    </Box>
  );
};

export default HomePage;
