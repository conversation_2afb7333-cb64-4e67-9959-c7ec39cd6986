/**
 *  About Cloudflare
 *
 *  @_subsection: api/providers/thirdparty:Cloudflare  [providers-cloudflare]
 */
import { JsonRpcProvider } from "./provider-jsonrpc.js";
import type { Networkish } from "./network.js";
/**
 *  About Cloudflare...
 */
export declare class CloudflareProvider extends JsonRpcProvider {
    constructor(_network?: Networkish);
}
//# sourceMappingURL=provider-cloudflare.d.ts.map