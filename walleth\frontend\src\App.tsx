import React, { useEffect } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { Box } from '@mui/material';

// Components
import Layout from '@/components/Layout/Layout';
import ProtectedRoute from '@/components/Auth/ProtectedRoute';
import LoadingScreen from '@/components/UI/LoadingScreen';

// Pages
import HomePage from '@/pages/Home/HomePage';
import LoginPage from '@/pages/Auth/LoginPage';
import RegisterPage from '@/pages/Auth/RegisterPage';
import TradingPage from '@/pages/Trading/TradingPage';
import WalletPage from '@/pages/Wallet/WalletPage';
import ProfilePage from '@/pages/Profile/ProfilePage';
import MarketsPage from '@/pages/Markets/MarketsPage';
import HistoryPage from '@/pages/History/HistoryPage';
import SecurityPage from '@/pages/Security/SecurityPage';
import NotFoundPage from '@/pages/NotFound/NotFoundPage';

// Admin Pages
import AdminDashboard from '@/pages/Admin/AdminDashboard';
import AdminUsers from '@/pages/Admin/AdminUsers';
import AdminKYC from '@/pages/Admin/AdminKYC';
import AdminWithdrawals from '@/pages/Admin/AdminWithdrawals';

// Store
import { RootState } from '@/types';
import { initializeAuth } from '@/store/slices/authSlice';
import { connectWebSocket } from '@/store/slices/marketSlice';

// Services
import { WebSocketService } from '@/services/websocket';

function App() {
  const dispatch = useDispatch();
  const { isAuthenticated, isLoading } = useSelector((state: RootState) => state.auth);
  const { isConnected } = useSelector((state: RootState) => state.ui);

  useEffect(() => {
    // Initialize authentication on app start
    dispatch(initializeAuth());
  }, [dispatch]);

  useEffect(() => {
    // Initialize WebSocket connection
    if (!isConnected) {
      dispatch(connectWebSocket());
    }

    // Cleanup on unmount
    return () => {
      WebSocketService.disconnect();
    };
  }, [dispatch, isConnected]);

  // Show loading screen while initializing
  if (isLoading) {
    return <LoadingScreen />;
  }

  return (
    <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      <Routes>
        {/* Public Routes */}
        <Route path="/login" element={
          isAuthenticated ? <Navigate to="/trading" replace /> : <LoginPage />
        } />
        <Route path="/register" element={
          isAuthenticated ? <Navigate to="/trading" replace /> : <RegisterPage />
        } />
        
        {/* Protected Routes with Layout */}
        <Route path="/" element={
          <ProtectedRoute>
            <Layout />
          </ProtectedRoute>
        }>
          {/* Main App Routes */}
          <Route index element={<Navigate to="/trading" replace />} />
          <Route path="home" element={<HomePage />} />
          <Route path="trading" element={<TradingPage />} />
          <Route path="trading/:pair" element={<TradingPage />} />
          <Route path="markets" element={<MarketsPage />} />
          <Route path="wallet" element={<WalletPage />} />
          <Route path="history" element={<HistoryPage />} />
          <Route path="profile" element={<ProfilePage />} />
          <Route path="security" element={<SecurityPage />} />
          
          {/* Admin Routes */}
          <Route path="admin" element={
            <ProtectedRoute requiredRole="ADMIN">
              <AdminDashboard />
            </ProtectedRoute>
          } />
          <Route path="admin/users" element={
            <ProtectedRoute requiredRole="ADMIN">
              <AdminUsers />
            </ProtectedRoute>
          } />
          <Route path="admin/kyc" element={
            <ProtectedRoute requiredRole="ADMIN">
              <AdminKYC />
            </ProtectedRoute>
          } />
          <Route path="admin/withdrawals" element={
            <ProtectedRoute requiredRole="ADMIN">
              <AdminWithdrawals />
            </ProtectedRoute>
          } />
        </Route>

        {/* Public Home Route (for non-authenticated users) */}
        <Route path="/home" element={<HomePage />} />
        
        {/* 404 Route */}
        <Route path="*" element={<NotFoundPage />} />
      </Routes>
    </Box>
  );
}

export default App;
