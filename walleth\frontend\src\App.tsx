import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

// Components
import LoadingScreen from './components/UI/LoadingScreen';
import AppRoutes from './routes';

// Store
import { RootState } from './types';
import { checkAuthStatus } from './store/slices/authSlice';

const App: React.FC = () => {
  const dispatch = useDispatch();
  const { isLoading } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    dispatch(checkAuthStatus());
  }, [dispatch]);

  if (isLoading) {
    return <LoadingScreen message="Initializing CryptoNest..." />;
  }

  return <AppRoutes />;
};

export default App;
