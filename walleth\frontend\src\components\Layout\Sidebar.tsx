import React from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useSelector } from 'react-redux';
import {
  Box,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Divider,
  Typography,
  Chip,
  Collapse,
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  TrendingUp as TradingIcon,
  AccountBalanceWallet as WalletIcon,
  ShowChart as MarketsIcon,
  History as HistoryIcon,
  Person as ProfileIcon,
  Security as SecurityIcon,
  AdminPanelSettings as AdminIcon,
  People as UsersIcon,
  VerifiedUser as KYCIcon,
  MonetizationOn as WithdrawalsIcon,
  ExpandLess,
  ExpandMore,
  Home as HomeIcon,
} from '@mui/icons-material';

// Store
import { RootState } from '@/types';

interface MenuItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  path: string;
  badge?: string | number;
  requiredRole?: 'USER' | 'ADMIN' | 'SUPER_ADMIN';
  children?: MenuItem[];
}

const Sidebar: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const { user } = useSelector((state: RootState) => state.auth);
  const { unreadCount } = useSelector((state: RootState) => state.ui);
  
  const [adminExpanded, setAdminExpanded] = React.useState(false);

  // Navigation menu items
  const menuItems: MenuItem[] = [
    {
      id: 'home',
      label: 'Home',
      icon: <HomeIcon />,
      path: '/home',
    },
    {
      id: 'trading',
      label: 'Trading',
      icon: <TradingIcon />,
      path: '/trading',
    },
    {
      id: 'markets',
      label: 'Markets',
      icon: <MarketsIcon />,
      path: '/markets',
    },
    {
      id: 'wallet',
      label: 'Wallet',
      icon: <WalletIcon />,
      path: '/wallet',
    },
    {
      id: 'history',
      label: 'History',
      icon: <HistoryIcon />,
      path: '/history',
    },
    {
      id: 'profile',
      label: 'Profile',
      icon: <ProfileIcon />,
      path: '/profile',
      badge: unreadCount > 0 ? unreadCount : undefined,
    },
    {
      id: 'security',
      label: 'Security',
      icon: <SecurityIcon />,
      path: '/security',
    },
  ];

  // Admin menu items
  const adminMenuItems: MenuItem[] = [
    {
      id: 'admin-dashboard',
      label: 'Dashboard',
      icon: <DashboardIcon />,
      path: '/admin',
    },
    {
      id: 'admin-users',
      label: 'Users',
      icon: <UsersIcon />,
      path: '/admin/users',
    },
    {
      id: 'admin-kyc',
      label: 'KYC Review',
      icon: <KYCIcon />,
      path: '/admin/kyc',
    },
    {
      id: 'admin-withdrawals',
      label: 'Withdrawals',
      icon: <WithdrawalsIcon />,
      path: '/admin/withdrawals',
    },
  ];

  const handleNavigation = (path: string) => {
    navigate(path);
  };

  const handleAdminToggle = () => {
    setAdminExpanded(!adminExpanded);
  };

  const isActive = (path: string) => {
    return location.pathname === path || 
           (path !== '/' && location.pathname.startsWith(path));
  };

  const canAccessAdmin = user?.role === 'ADMIN' || user?.role === 'SUPER_ADMIN';

  return (
    <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* User Info */}
      <Box sx={{ p: 2, borderBottom: '1px solid', borderColor: 'divider' }}>
        <Typography variant="subtitle2" color="text.secondary" gutterBottom>
          Welcome back
        </Typography>
        <Typography variant="h6" noWrap>
          {user?.firstName || user?.email?.split('@')[0] || 'User'}
        </Typography>
        <Box sx={{ mt: 1, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          <Chip
            size="small"
            label={user?.kycStatus || 'PENDING'}
            color={user?.kycStatus === 'APPROVED' ? 'success' : 'warning'}
            variant="outlined"
          />
          {user?.is2FAEnabled && (
            <Chip
              size="small"
              label="2FA"
              color="primary"
              variant="outlined"
            />
          )}
        </Box>
      </Box>

      {/* Navigation Menu */}
      <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
        <List sx={{ pt: 1 }}>
          {menuItems.map((item) => (
            <ListItem key={item.id} disablePadding>
              <ListItemButton
                selected={isActive(item.path)}
                onClick={() => handleNavigation(item.path)}
                sx={{
                  mx: 1,
                  mb: 0.5,
                  borderRadius: 1,
                  '&.Mui-selected': {
                    backgroundColor: 'primary.main',
                    color: 'primary.contrastText',
                    '&:hover': {
                      backgroundColor: 'primary.dark',
                    },
                    '& .MuiListItemIcon-root': {
                      color: 'primary.contrastText',
                    },
                  },
                }}
              >
                <ListItemIcon
                  sx={{
                    minWidth: 40,
                    color: isActive(item.path) ? 'inherit' : 'text.secondary',
                  }}
                >
                  {item.icon}
                </ListItemIcon>
                <ListItemText 
                  primary={item.label}
                  primaryTypographyProps={{
                    fontSize: '0.875rem',
                    fontWeight: isActive(item.path) ? 600 : 400,
                  }}
                />
                {item.badge && (
                  <Chip
                    size="small"
                    label={item.badge}
                    color="error"
                    sx={{ height: 20, fontSize: '0.75rem' }}
                  />
                )}
              </ListItemButton>
            </ListItem>
          ))}
        </List>

        {/* Admin Section */}
        {canAccessAdmin && (
          <>
            <Divider sx={{ mx: 2, my: 1 }} />
            <List>
              <ListItem disablePadding>
                <ListItemButton
                  onClick={handleAdminToggle}
                  sx={{
                    mx: 1,
                    mb: 0.5,
                    borderRadius: 1,
                  }}
                >
                  <ListItemIcon sx={{ minWidth: 40, color: 'text.secondary' }}>
                    <AdminIcon />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Administration"
                    primaryTypographyProps={{
                      fontSize: '0.875rem',
                      fontWeight: 500,
                    }}
                  />
                  {adminExpanded ? <ExpandLess /> : <ExpandMore />}
                </ListItemButton>
              </ListItem>
              
              <Collapse in={adminExpanded} timeout="auto" unmountOnExit>
                <List component="div" disablePadding>
                  {adminMenuItems.map((item) => (
                    <ListItem key={item.id} disablePadding>
                      <ListItemButton
                        selected={isActive(item.path)}
                        onClick={() => handleNavigation(item.path)}
                        sx={{
                          mx: 1,
                          mb: 0.5,
                          borderRadius: 1,
                          pl: 4,
                          '&.Mui-selected': {
                            backgroundColor: 'primary.main',
                            color: 'primary.contrastText',
                            '&:hover': {
                              backgroundColor: 'primary.dark',
                            },
                            '& .MuiListItemIcon-root': {
                              color: 'primary.contrastText',
                            },
                          },
                        }}
                      >
                        <ListItemIcon
                          sx={{
                            minWidth: 40,
                            color: isActive(item.path) ? 'inherit' : 'text.secondary',
                          }}
                        >
                          {item.icon}
                        </ListItemIcon>
                        <ListItemText 
                          primary={item.label}
                          primaryTypographyProps={{
                            fontSize: '0.875rem',
                            fontWeight: isActive(item.path) ? 600 : 400,
                          }}
                        />
                      </ListItemButton>
                    </ListItem>
                  ))}
                </List>
              </Collapse>
            </List>
          </>
        )}
      </Box>

      {/* Footer */}
      <Box sx={{ p: 2, borderTop: '1px solid', borderColor: 'divider' }}>
        <Typography variant="caption" color="text.secondary" align="center" display="block">
          CryptoNest v1.0.0
        </Typography>
        <Typography variant="caption" color="text.secondary" align="center" display="block">
          © 2024 CryptoNest Team
        </Typography>
      </Box>
    </Box>
  );
};

export default Sidebar;
