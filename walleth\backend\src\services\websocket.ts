import { Server as SocketIOServer, Socket } from 'socket.io';
import jwt from 'jsonwebtoken';
import { PrismaClient } from '@prisma/client';
import { logger } from '../utils/logger';
// import { redis } from '../index';

const prisma = new PrismaClient();

interface AuthenticatedSocket extends Socket {
  userId?: string;
  userEmail?: string;
  userRole?: string;
}

export class WebSocketService {
  private io: SocketIOServer;
  private connectedUsers: Map<string, Set<string>> = new Map(); // userId -> Set of socketIds
  private subscribedChannels: Map<string, Set<string>> = new Map(); // channel -> Set of socketIds

  constructor(io: SocketIOServer) {
    this.io = io;
    this.setupMiddleware();
    this.setupEventHandlers();
    this.startHeartbeat();
  }

  private setupMiddleware(): void {
    // Authentication middleware
    this.io.use(async (socket: AuthenticatedSocket, next) => {
      try {
        const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.replace('Bearer ', '');
        
        if (!token) {
          // Allow anonymous connections for public data
          logger.info('Anonymous WebSocket connection', { socketId: socket.id });
          return next();
        }

        const decoded = jwt.verify(token, process.env.JWT_SECRET || 'fallback-secret') as any;
        
        const user = await prisma.user.findUnique({
          where: { id: decoded.userId },
          select: {
            id: true,
            email: true,
            role: true,
            status: true
          }
        });

        if (!user || user.status !== 'ACTIVE') {
          return next(new Error('Authentication failed'));
        }

        socket.userId = user.id;
        socket.userEmail = user.email;
        socket.userRole = user.role;

        logger.info('Authenticated WebSocket connection', {
          socketId: socket.id,
          userId: user.id,
          email: user.email
        });

        next();
      } catch (error) {
        logger.error('WebSocket authentication error:', error);
        next(new Error('Authentication failed'));
      }
    });
  }

  private setupEventHandlers(): void {
    this.io.on('connection', (socket: AuthenticatedSocket) => {
      logger.info('WebSocket client connected', {
        socketId: socket.id,
        userId: socket.userId,
        totalConnections: this.io.engine.clientsCount
      });

      // Track authenticated users
      if (socket.userId) {
        if (!this.connectedUsers.has(socket.userId)) {
          this.connectedUsers.set(socket.userId, new Set());
        }
        this.connectedUsers.get(socket.userId)!.add(socket.id);
      }

      // Handle channel subscriptions
      socket.on('subscribe', (data: { channels: string[] }) => {
        this.handleSubscribe(socket, data.channels);
      });

      socket.on('unsubscribe', (data: { channels: string[] }) => {
        this.handleUnsubscribe(socket, data.channels);
      });

      // Handle trading-related events
      socket.on('subscribe_orderbook', (data: { tradingPair: string }) => {
        this.subscribeToOrderbook(socket, data.tradingPair);
      });

      socket.on('subscribe_trades', (data: { tradingPair: string }) => {
        this.subscribeToTrades(socket, data.tradingPair);
      });

      socket.on('subscribe_ticker', (data: { tradingPair?: string }) => {
        this.subscribeToTicker(socket, data.tradingPair);
      });

      // Handle user-specific events (requires authentication)
      socket.on('subscribe_user_orders', () => {
        if (socket.userId) {
          this.subscribeToUserOrders(socket);
        } else {
          socket.emit('error', { message: 'Authentication required' });
        }
      });

      socket.on('subscribe_user_trades', () => {
        if (socket.userId) {
          this.subscribeToUserTrades(socket);
        } else {
          socket.emit('error', { message: 'Authentication required' });
        }
      });

      socket.on('subscribe_user_wallet', () => {
        if (socket.userId) {
          this.subscribeToUserWallet(socket);
        } else {
          socket.emit('error', { message: 'Authentication required' });
        }
      });

      // Handle ping/pong for connection health
      socket.on('ping', () => {
        socket.emit('pong', { timestamp: Date.now() });
      });

      // Handle disconnection
      socket.on('disconnect', (reason) => {
        logger.info('WebSocket client disconnected', {
          socketId: socket.id,
          userId: socket.userId,
          reason,
          totalConnections: this.io.engine.clientsCount
        });

        this.handleDisconnect(socket);
      });

      // Send welcome message
      socket.emit('connected', {
        message: 'Connected to WALLETH WebSocket',
        timestamp: Date.now(),
        authenticated: !!socket.userId
      });
    });
  }

  private handleSubscribe(socket: AuthenticatedSocket, channels: string[]): void {
    channels.forEach(channel => {
      socket.join(channel);
      
      if (!this.subscribedChannels.has(channel)) {
        this.subscribedChannels.set(channel, new Set());
      }
      this.subscribedChannels.get(channel)!.add(socket.id);
      
      logger.debug('Socket subscribed to channel', {
        socketId: socket.id,
        userId: socket.userId,
        channel
      });
    });

    socket.emit('subscribed', { channels });
  }

  private handleUnsubscribe(socket: AuthenticatedSocket, channels: string[]): void {
    channels.forEach(channel => {
      socket.leave(channel);
      
      if (this.subscribedChannels.has(channel)) {
        this.subscribedChannels.get(channel)!.delete(socket.id);
        if (this.subscribedChannels.get(channel)!.size === 0) {
          this.subscribedChannels.delete(channel);
        }
      }
      
      logger.debug('Socket unsubscribed from channel', {
        socketId: socket.id,
        userId: socket.userId,
        channel
      });
    });

    socket.emit('unsubscribed', { channels });
  }

  private subscribeToOrderbook(socket: AuthenticatedSocket, tradingPair: string): void {
    const channel = `orderbook:${tradingPair}`;
    socket.join(channel);
    logger.debug('Subscribed to orderbook', { socketId: socket.id, tradingPair });
  }

  private subscribeToTrades(socket: AuthenticatedSocket, tradingPair: string): void {
    const channel = `trades:${tradingPair}`;
    socket.join(channel);
    logger.debug('Subscribed to trades', { socketId: socket.id, tradingPair });
  }

  private subscribeToTicker(socket: AuthenticatedSocket, tradingPair?: string): void {
    const channel = tradingPair ? `ticker:${tradingPair}` : 'ticker:all';
    socket.join(channel);
    logger.debug('Subscribed to ticker', { socketId: socket.id, tradingPair });
  }

  private subscribeToUserOrders(socket: AuthenticatedSocket): void {
    const channel = `user:${socket.userId}:orders`;
    socket.join(channel);
    logger.debug('Subscribed to user orders', { socketId: socket.id, userId: socket.userId });
  }

  private subscribeToUserTrades(socket: AuthenticatedSocket): void {
    const channel = `user:${socket.userId}:trades`;
    socket.join(channel);
    logger.debug('Subscribed to user trades', { socketId: socket.id, userId: socket.userId });
  }

  private subscribeToUserWallet(socket: AuthenticatedSocket): void {
    const channel = `user:${socket.userId}:wallet`;
    socket.join(channel);
    logger.debug('Subscribed to user wallet', { socketId: socket.id, userId: socket.userId });
  }

  private handleDisconnect(socket: AuthenticatedSocket): void {
    // Remove from connected users
    if (socket.userId && this.connectedUsers.has(socket.userId)) {
      this.connectedUsers.get(socket.userId)!.delete(socket.id);
      if (this.connectedUsers.get(socket.userId)!.size === 0) {
        this.connectedUsers.delete(socket.userId);
      }
    }

    // Remove from subscribed channels
    this.subscribedChannels.forEach((socketIds, channel) => {
      socketIds.delete(socket.id);
      if (socketIds.size === 0) {
        this.subscribedChannels.delete(channel);
      }
    });
  }

  private startHeartbeat(): void {
    const interval = parseInt(process.env.WS_HEARTBEAT_INTERVAL || '30000');
    
    setInterval(() => {
      this.io.emit('heartbeat', { timestamp: Date.now() });
    }, interval);
  }

  // Public methods for broadcasting data
  public broadcastOrderbookUpdate(tradingPair: string, orderbook: any): void {
    this.io.to(`orderbook:${tradingPair}`).emit('orderbook_update', {
      tradingPair,
      orderbook,
      timestamp: Date.now()
    });
  }

  public broadcastTradeUpdate(tradingPair: string, trade: any): void {
    this.io.to(`trades:${tradingPair}`).emit('trade_update', {
      tradingPair,
      trade,
      timestamp: Date.now()
    });
  }

  public broadcastTickerUpdate(tradingPair: string, ticker: any): void {
    this.io.to(`ticker:${tradingPair}`).emit('ticker_update', {
      tradingPair,
      ticker,
      timestamp: Date.now()
    });
    
    // Also broadcast to all tickers channel
    this.io.to('ticker:all').emit('ticker_update', {
      tradingPair,
      ticker,
      timestamp: Date.now()
    });
  }

  public notifyUser(userId: string, event: string, data: any): void {
    const userSockets = this.connectedUsers.get(userId);
    if (userSockets) {
      userSockets.forEach(socketId => {
        this.io.to(socketId).emit(event, {
          ...data,
          timestamp: Date.now()
        });
      });
    }
  }

  public notifyUserOrder(userId: string, order: any): void {
    this.io.to(`user:${userId}:orders`).emit('order_update', {
      order,
      timestamp: Date.now()
    });
  }

  public notifyUserTrade(userId: string, trade: any): void {
    this.io.to(`user:${userId}:trades`).emit('trade_update', {
      trade,
      timestamp: Date.now()
    });
  }

  public notifyUserWallet(userId: string, wallet: any): void {
    this.io.to(`user:${userId}:wallet`).emit('wallet_update', {
      wallet,
      timestamp: Date.now()
    });
  }

  // Get statistics
  public getStats(): any {
    return {
      totalConnections: this.io.engine.clientsCount,
      authenticatedUsers: this.connectedUsers.size,
      subscribedChannels: this.subscribedChannels.size,
      channels: Array.from(this.subscribedChannels.keys())
    };
  }
}
