import React, { Suspense } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';

// Layout
import Layout from '@/components/Layout/Layout';
import ProtectedRoute from '@/components/Auth/ProtectedRoute';
import LoadingScreen from '@/components/UI/LoadingScreen';

// Lazy load pages for better performance
const HomePage = React.lazy(() => import('@/pages/Home/HomePage'));
const LoginPage = React.lazy(() => import('@/pages/Auth/LoginPage'));
const RegisterPage = React.lazy(() => import('@/pages/Auth/RegisterPage'));
const TradingPage = React.lazy(() => import('@/pages/Trading/TradingPage'));
const MarketsPage = React.lazy(() => import('@/pages/Markets/MarketsPage'));
const WalletPage = React.lazy(() => import('@/pages/Wallet/WalletPage'));
const ProfilePage = React.lazy(() => import('@/pages/Profile/ProfilePage'));
const SecurityPage = React.lazy(() => import('@/pages/Security/SecurityPage'));
const HistoryPage = React.lazy(() => import('@/pages/History/HistoryPage'));
const NotFoundPage = React.lazy(() => import('@/pages/NotFound/NotFoundPage'));

// Admin pages
const AdminDashboard = React.lazy(() => import('@/pages/Admin/AdminDashboard'));
const AdminUsers = React.lazy(() => import('@/pages/Admin/AdminUsers'));
const AdminKYC = React.lazy(() => import('@/pages/Admin/AdminKYC'));

const AppRoutes: React.FC = () => {
  return (
    <Suspense fallback={<LoadingScreen message="Loading CryptoNest..." />}>
      <Routes>
        {/* Public Routes */}
        <Route path="/login" element={<LoginPage />} />
        <Route path="/register" element={<RegisterPage />} />
        
        {/* Protected Routes with Layout */}
        <Route path="/" element={<Layout />}>
          {/* Home */}
          <Route index element={<HomePage />} />
          <Route path="home" element={<HomePage />} />
          
          {/* Trading */}
          <Route 
            path="trading" 
            element={
              <ProtectedRoute>
                <TradingPage />
              </ProtectedRoute>
            } 
          />
          <Route 
            path="trading/:pair" 
            element={
              <ProtectedRoute>
                <TradingPage />
              </ProtectedRoute>
            } 
          />
          
          {/* Markets */}
          <Route path="markets" element={<MarketsPage />} />
          
          {/* Wallet */}
          <Route 
            path="wallet" 
            element={
              <ProtectedRoute requireEmailVerification>
                <WalletPage />
              </ProtectedRoute>
            } 
          />
          
          {/* Profile */}
          <Route 
            path="profile" 
            element={
              <ProtectedRoute>
                <ProfilePage />
              </ProtectedRoute>
            } 
          />
          
          {/* Security */}
          <Route 
            path="security" 
            element={
              <ProtectedRoute>
                <SecurityPage />
              </ProtectedRoute>
            } 
          />
          
          {/* History */}
          <Route 
            path="history" 
            element={
              <ProtectedRoute>
                <HistoryPage />
              </ProtectedRoute>
            } 
          />
          
          {/* Admin Routes */}
          <Route 
            path="admin" 
            element={
              <ProtectedRoute requiredRole="ADMIN">
                <AdminDashboard />
              </ProtectedRoute>
            } 
          />
          <Route 
            path="admin/users" 
            element={
              <ProtectedRoute requiredRole="ADMIN">
                <AdminUsers />
              </ProtectedRoute>
            } 
          />
          <Route 
            path="admin/kyc" 
            element={
              <ProtectedRoute requiredRole="ADMIN">
                <AdminKYC />
              </ProtectedRoute>
            } 
          />
          
          {/* Redirects */}
          <Route path="dashboard" element={<Navigate to="/home" replace />} />
          <Route path="exchange" element={<Navigate to="/trading" replace />} />
          <Route path="trade" element={<Navigate to="/trading" replace />} />
        </Route>
        
        {/* 404 Page */}
        <Route path="*" element={<NotFoundPage />} />
      </Routes>
    </Suspense>
  );
};

export default AppRoutes;
