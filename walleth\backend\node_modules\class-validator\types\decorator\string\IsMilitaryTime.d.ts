import { ValidationOptions } from '../ValidationOptions';
export declare const IS_MILITARY_TIME = "isMilitaryTime";
/**
 * Checks if the string represents a time without a given timezone in the format HH:MM (military)
 * If the given value does not match the pattern HH:MM, then it returns false.
 */
export declare function isMilitaryTime(value: unknown): boolean;
/**
 * Checks if the string represents a time without a given timezone in the format HH:MM (military)
 * If the given value does not match the pattern HH:MM, then it returns false.
 */
export declare function IsMilitaryTime(validationOptions?: ValidationOptions): PropertyDecorator;
