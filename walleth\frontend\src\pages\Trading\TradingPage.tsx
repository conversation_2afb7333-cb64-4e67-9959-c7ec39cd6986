import React, { useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import {
  Box,
  Grid,
  Paper,
  Typography,
  Card,
  CardContent,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
} from '@mui/icons-material';

// Store
import { RootState } from '@/types';
import { 
  setCurrentPair, 
  fetchTradingPairs, 
  fetchTickers,
  fetchOrderBook,
  fetchRecentTrades 
} from '@/store/slices/marketSlice';

const TradingPage: React.FC = () => {
  const dispatch = useDispatch();
  const { pair } = useParams<{ pair: string }>();
  
  const { 
    currentPair, 
    tradingPairs, 
    tickers, 
    orderBooks, 
    recentTrades,
    isLoading 
  } = useSelector((state: RootState) => state.market);

  useEffect(() => {
    // Set current pair from URL or default
    const targetPair = pair?.toUpperCase() || 'BTCUSDT';
    if (targetPair !== currentPair) {
      dispatch(setCurrentPair(targetPair));
    }
  }, [pair, currentPair, dispatch]);

  useEffect(() => {
    // Fetch initial data
    dispatch(fetchTradingPairs());
    dispatch(fetchTickers());
  }, [dispatch]);

  useEffect(() => {
    // Fetch pair-specific data when current pair changes
    if (currentPair) {
      dispatch(fetchOrderBook(currentPair));
      dispatch(fetchRecentTrades(currentPair));
    }
  }, [currentPair, dispatch]);

  const currentTicker = tickers[currentPair];
  const currentOrderBook = orderBooks[currentPair];
  const currentTrades = recentTrades[currentPair] || [];

  const formatPrice = (price: number | string) => {
    const num = typeof price === 'string' ? parseFloat(price) : price;
    return num.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 8,
    });
  };

  const formatChange = (change: number) => {
    const isPositive = change >= 0;
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
        {isPositive ? (
          <TrendingUpIcon sx={{ color: 'success.main', fontSize: 16 }} />
        ) : (
          <TrendingDownIcon sx={{ color: 'error.main', fontSize: 16 }} />
        )}
        <Typography
          variant="body2"
          sx={{ color: isPositive ? 'success.main' : 'error.main' }}
        >
          {isPositive ? '+' : ''}{change.toFixed(2)}%
        </Typography>
      </Box>
    );
  };

  return (
    <Box sx={{ p: 2, height: '100%', overflow: 'auto' }}>
      <Grid container spacing={2} sx={{ height: '100%' }}>
        {/* Header */}
        <Grid item xs={12}>
          <Paper sx={{ p: 2 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
              <Typography variant="h4" fontWeight={700}>
                {currentPair}
              </Typography>
              {currentTicker && (
                <>
                  <Typography variant="h5" color="text.secondary">
                    ${formatPrice(currentTicker.price)}
                  </Typography>
                  {formatChange(currentTicker.change24h)}
                </>
              )}
            </Box>
            
            {currentTicker && (
              <Grid container spacing={2}>
                <Grid item xs={6} sm={3}>
                  <Typography variant="caption" color="text.secondary">
                    24h High
                  </Typography>
                  <Typography variant="body2" fontWeight={600}>
                    ${formatPrice(currentTicker.high24h)}
                  </Typography>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Typography variant="caption" color="text.secondary">
                    24h Low
                  </Typography>
                  <Typography variant="body2" fontWeight={600}>
                    ${formatPrice(currentTicker.low24h)}
                  </Typography>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Typography variant="caption" color="text.secondary">
                    24h Volume
                  </Typography>
                  <Typography variant="body2" fontWeight={600}>
                    {formatPrice(currentTicker.volume24h)}
                  </Typography>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Typography variant="caption" color="text.secondary">
                    Last Update
                  </Typography>
                  <Typography variant="body2" fontWeight={600}>
                    {new Date(currentTicker.timestamp).toLocaleTimeString()}
                  </Typography>
                </Grid>
              </Grid>
            )}
          </Paper>
        </Grid>

        {/* Chart Area */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 2, height: 400 }}>
            <Typography variant="h6" gutterBottom>
              Price Chart
            </Typography>
            <Box
              sx={{
                height: '100%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: 'action.hover',
                borderRadius: 1,
              }}
            >
              <Typography color="text.secondary">
                Chart component will be implemented here
              </Typography>
            </Box>
          </Paper>
        </Grid>

        {/* Order Book */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2, height: 400 }}>
            <Typography variant="h6" gutterBottom>
              Order Book
            </Typography>
            {currentOrderBook ? (
              <Box sx={{ height: '100%', overflow: 'auto' }}>
                {/* Asks */}
                <Typography variant="subtitle2" color="error.main" gutterBottom>
                  Asks
                </Typography>
                <TableContainer sx={{ maxHeight: 150, mb: 2 }}>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Price</TableCell>
                        <TableCell align="right">Amount</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {currentOrderBook.asks.slice(0, 5).map((ask, index) => (
                        <TableRow key={index}>
                          <TableCell sx={{ color: 'error.main' }}>
                            {formatPrice(ask.price)}
                          </TableCell>
                          <TableCell align="right">
                            {formatPrice(ask.amount)}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>

                {/* Bids */}
                <Typography variant="subtitle2" color="success.main" gutterBottom>
                  Bids
                </Typography>
                <TableContainer sx={{ maxHeight: 150 }}>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>Price</TableCell>
                        <TableCell align="right">Amount</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {currentOrderBook.bids.slice(0, 5).map((bid, index) => (
                        <TableRow key={index}>
                          <TableCell sx={{ color: 'success.main' }}>
                            {formatPrice(bid.price)}
                          </TableCell>
                          <TableCell align="right">
                            {formatPrice(bid.amount)}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Box>
            ) : (
              <Box
                sx={{
                  height: '100%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <Typography color="text.secondary">
                  Loading order book...
                </Typography>
              </Box>
            )}
          </Paper>
        </Grid>

        {/* Trading Panel */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2, height: 300 }}>
            <Typography variant="h6" gutterBottom>
              Place Order
            </Typography>
            <Box
              sx={{
                height: '100%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                backgroundColor: 'action.hover',
                borderRadius: 1,
              }}
            >
              <Typography color="text.secondary">
                Trading form will be implemented here
              </Typography>
            </Box>
          </Paper>
        </Grid>

        {/* Recent Trades */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 2, height: 300 }}>
            <Typography variant="h6" gutterBottom>
              Recent Trades
            </Typography>
            {currentTrades.length > 0 ? (
              <TableContainer sx={{ height: '100%', overflow: 'auto' }}>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Time</TableCell>
                      <TableCell>Price</TableCell>
                      <TableCell>Amount</TableCell>
                      <TableCell>Side</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {currentTrades.slice(0, 20).map((trade) => (
                      <TableRow key={trade.id}>
                        <TableCell>
                          {new Date(trade.timestamp).toLocaleTimeString()}
                        </TableCell>
                        <TableCell
                          sx={{
                            color: trade.side === 'buy' ? 'success.main' : 'error.main',
                          }}
                        >
                          {formatPrice(trade.price)}
                        </TableCell>
                        <TableCell>{formatPrice(trade.amount)}</TableCell>
                        <TableCell>
                          <Chip
                            label={trade.side.toUpperCase()}
                            size="small"
                            color={trade.side === 'buy' ? 'success' : 'error'}
                            variant="outlined"
                          />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            ) : (
              <Box
                sx={{
                  height: '100%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <Typography color="text.secondary">
                  No recent trades
                </Typography>
              </Box>
            )}
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default TradingPage;
