import React, { useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useForm } from 'react-hook-form';
import {
  Box,
  Grid,
  Paper,
  Typography,
  TextField,
  Button,
  Avatar,
  Chip,
  Card,
  CardContent,
  Divider,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
} from '@mui/material';
import {
  Person as PersonIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  LocationOn as LocationIcon,
  CalendarToday as CalendarIcon,
  Verified as VerifiedIcon,
  Warning as WarningIcon,
  Security as SecurityIcon,
  Notifications as NotificationsIcon,
} from '@mui/icons-material';

// Store
import { RootState } from '@/types';
import { updateUser } from '@/store/slices/authSlice';

// Services
import { userAPI } from '@/services/api';

interface ProfileFormData {
  firstName: string;
  lastName: string;
  phone: string;
  dateOfBirth: string;
  country: string;
}

const ProfilePage: React.FC = () => {
  const dispatch = useDispatch();
  const { user } = useSelector((state: RootState) => state.auth);
  
  const [isEditing, setIsEditing] = useState(false);
  const [kycDialog, setKycDialog] = useState(false);
  const [referralDialog, setReferralDialog] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<ProfileFormData>({
    defaultValues: {
      firstName: user?.firstName || '',
      lastName: user?.lastName || '',
      phone: user?.phone || '',
      dateOfBirth: user?.dateOfBirth ? user.dateOfBirth.split('T')[0] : '',
      country: user?.country || '',
    },
  });

  const onSubmit = async (data: ProfileFormData) => {
    try {
      const response = await userAPI.updateProfile(data);
      dispatch(updateUser(response.data.user));
      setIsEditing(false);
    } catch (error) {
      console.error('Failed to update profile:', error);
    }
  };

  const handleCancel = () => {
    reset();
    setIsEditing(false);
  };

  const getKycStatusColor = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return 'success';
      case 'SUBMITTED':
        return 'info';
      case 'REJECTED':
        return 'error';
      default:
        return 'warning';
    }
  };

  const getKycStatusText = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return 'Verified';
      case 'SUBMITTED':
        return 'Under Review';
      case 'REJECTED':
        return 'Rejected';
      case 'EXPIRED':
        return 'Expired';
      default:
        return 'Pending';
    }
  };

  if (!user) {
    return null;
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom>
          Profile
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Manage your account information and settings
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* Profile Overview */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar
                sx={{
                  width: 80,
                  height: 80,
                  mx: 'auto',
                  mb: 2,
                  bgcolor: 'primary.main',
                  fontSize: '2rem',
                }}
              >
                {user.firstName ? user.firstName[0] : user.email[0]}
              </Avatar>
              
              <Typography variant="h6" gutterBottom>
                {user.firstName && user.lastName 
                  ? `${user.firstName} ${user.lastName}`
                  : user.email
                }
              </Typography>
              
              <Typography variant="body2" color="text.secondary" gutterBottom>
                {user.email}
              </Typography>

              <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1, mb: 2 }}>
                <Chip
                  label={getKycStatusText(user.kycStatus)}
                  color={getKycStatusColor(user.kycStatus)}
                  size="small"
                />
                {user.is2FAEnabled && (
                  <Chip
                    label="2FA"
                    color="primary"
                    size="small"
                  />
                )}
              </Box>

              <Typography variant="caption" color="text.secondary">
                Member since {new Date(user.createdAt).toLocaleDateString()}
              </Typography>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card sx={{ mt: 2 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Quick Actions
              </Typography>
              
              <List dense>
                <ListItem button onClick={() => setKycDialog(true)}>
                  <ListItemIcon>
                    <VerifiedIcon color={user.kycStatus === 'APPROVED' ? 'success' : 'warning'} />
                  </ListItemIcon>
                  <ListItemText 
                    primary="KYC Verification"
                    secondary={getKycStatusText(user.kycStatus)}
                  />
                </ListItem>

                <ListItem button onClick={() => window.location.href = '/security'}>
                  <ListItemIcon>
                    <SecurityIcon />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Security Settings"
                    secondary="2FA, Password"
                  />
                </ListItem>

                <ListItem button onClick={() => setReferralDialog(true)}>
                  <ListItemIcon>
                    <PersonIcon />
                  </ListItemIcon>
                  <ListItemText 
                    primary="Referral Program"
                    secondary="Invite friends"
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Profile Details */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Typography variant="h6">
                  Personal Information
                </Typography>
                {!isEditing ? (
                  <Button variant="outlined" onClick={() => setIsEditing(true)}>
                    Edit Profile
                  </Button>
                ) : (
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Button variant="outlined" onClick={handleCancel}>
                      Cancel
                    </Button>
                    <Button variant="contained" onClick={handleSubmit(onSubmit)}>
                      Save Changes
                    </Button>
                  </Box>
                )}
              </Box>

              {!user.isEmailVerified && (
                <Alert severity="warning" sx={{ mb: 3 }}>
                  Please verify your email address to unlock all features.
                </Alert>
              )}

              <Box component="form" onSubmit={handleSubmit(onSubmit)}>
                <Grid container spacing={3}>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="First Name"
                      disabled={!isEditing}
                      {...register('firstName', {
                        required: 'First name is required',
                      })}
                      error={!!errors.firstName}
                      helperText={errors.firstName?.message}
                      InputProps={{
                        startAdornment: <PersonIcon sx={{ mr: 1, color: 'action.active' }} />,
                      }}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Last Name"
                      disabled={!isEditing}
                      {...register('lastName', {
                        required: 'Last name is required',
                      })}
                      error={!!errors.lastName}
                      helperText={errors.lastName?.message}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Email Address"
                      value={user.email}
                      disabled
                      InputProps={{
                        startAdornment: <EmailIcon sx={{ mr: 1, color: 'action.active' }} />,
                        endAdornment: user.isEmailVerified ? (
                          <VerifiedIcon color="success" />
                        ) : (
                          <WarningIcon color="warning" />
                        ),
                      }}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Phone Number"
                      disabled={!isEditing}
                      {...register('phone')}
                      InputProps={{
                        startAdornment: <PhoneIcon sx={{ mr: 1, color: 'action.active' }} />,
                      }}
                    />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Date of Birth"
                      type="date"
                      disabled={!isEditing}
                      {...register('dateOfBirth')}
                      InputLabelProps={{
                        shrink: true,
                      }}
                      InputProps={{
                        startAdornment: <CalendarIcon sx={{ mr: 1, color: 'action.active' }} />,
                      }}
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Country"
                      disabled={!isEditing}
                      {...register('country')}
                      InputProps={{
                        startAdornment: <LocationIcon sx={{ mr: 1, color: 'action.active' }} />,
                      }}
                    />
                  </Grid>
                </Grid>
              </Box>
            </CardContent>
          </Card>

          {/* Account Status */}
          <Card sx={{ mt: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Account Status
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <EmailIcon color={user.isEmailVerified ? 'success' : 'warning'} />
                    <Typography variant="body2">
                      Email: {user.isEmailVerified ? 'Verified' : 'Not Verified'}
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <PhoneIcon color={user.isPhoneVerified ? 'success' : 'disabled'} />
                    <Typography variant="body2">
                      Phone: {user.isPhoneVerified ? 'Verified' : 'Not Verified'}
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <VerifiedIcon color={user.kycStatus === 'APPROVED' ? 'success' : 'warning'} />
                    <Typography variant="body2">
                      KYC: {getKycStatusText(user.kycStatus)}
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <SecurityIcon color={user.is2FAEnabled ? 'success' : 'warning'} />
                    <Typography variant="body2">
                      2FA: {user.is2FAEnabled ? 'Enabled' : 'Disabled'}
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* KYC Dialog */}
      <Dialog open={kycDialog} onClose={() => setKycDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>KYC Verification</DialogTitle>
        <DialogContent>
          <Typography variant="body2" gutterBottom>
            Current Status: <Chip label={getKycStatusText(user.kycStatus)} color={getKycStatusColor(user.kycStatus)} size="small" />
          </Typography>
          
          {user.kycStatus === 'PENDING' && (
            <Typography variant="body2" color="text.secondary">
              Complete your KYC verification to unlock higher withdrawal limits and access to all features.
            </Typography>
          )}
          
          {user.kycStatus === 'SUBMITTED' && (
            <Typography variant="body2" color="text.secondary">
              Your KYC documents are under review. We'll notify you once the verification is complete.
            </Typography>
          )}
          
          {user.kycStatus === 'APPROVED' && (
            <Typography variant="body2" color="success.main">
              Your account is fully verified! You have access to all platform features.
            </Typography>
          )}
          
          {user.kycStatus === 'REJECTED' && (
            <Typography variant="body2" color="error.main">
              Your KYC verification was rejected. Please resubmit your documents with the correct information.
            </Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setKycDialog(false)}>Close</Button>
          {user.kycStatus !== 'APPROVED' && (
            <Button variant="contained">
              {user.kycStatus === 'PENDING' ? 'Start KYC' : 'Resubmit Documents'}
            </Button>
          )}
        </DialogActions>
      </Dialog>

      {/* Referral Dialog */}
      <Dialog open={referralDialog} onClose={() => setReferralDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Referral Program</DialogTitle>
        <DialogContent>
          <Typography variant="body2" gutterBottom>
            Your Referral Code: <strong>{user.referralCode}</strong>
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Share your referral code with friends and earn rewards when they sign up and start trading!
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setReferralDialog(false)}>Close</Button>
          <Button variant="contained">Copy Code</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ProfilePage;
