{"name": "walleth-backend", "version": "1.0.0", "description": "WALLETH Cryptocurrency Exchange Backend API", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "migrate": "npx prisma migrate dev", "migrate:prod": "npx prisma migrate deploy", "seed": "npx prisma db seed", "generate": "npx prisma generate", "studio": "npx prisma studio", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts"}, "keywords": ["cryptocurrency", "exchange", "trading", "blockchain", "bitcoin", "ethereum"], "author": "WALLETH Team", "license": "MIT", "dependencies": {"@prisma/client": "^5.7.1", "@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20.10.5", "@types/passport": "^1.0.16", "@types/passport-jwt": "^3.0.13", "@types/passport-local": "^1.0.38", "@types/uuid": "^9.0.7", "bcryptjs": "^2.4.3", "bull": "^4.12.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "ioredis": "^5.3.2", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "nodemailer": "^6.9.7", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "prisma": "^5.7.1", "socket.io": "^4.7.4", "speakeasy": "^2.0.0", "uuid": "^9.0.1", "winston": "^3.11.0", "ws": "^8.14.2"}, "devDependencies": {"@types/jest": "^29.5.8", "@types/nodemailer": "^6.4.14", "@types/speakeasy": "^2.0.10", "@types/ws": "^8.5.10", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "prettier": "^3.1.0", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.3.3"}, "prisma": {"seed": "ts-node prisma/seed.ts"}, "engines": {"node": ">=18.0.0"}}