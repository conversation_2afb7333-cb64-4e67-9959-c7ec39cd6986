import { ValidationOptions } from '../ValidationOptions';
export declare const NOT_EQUALS = "notEquals";
/**
 * Checks if value does not match ("!==") the comparison.
 */
export declare function notEquals(value: unknown, comparison: unknown): boolean;
/**
 * Checks if value does not match ("!==") the comparison.
 */
export declare function NotEquals(comparison: any, validationOptions?: ValidationOptions): PropertyDecorator;
