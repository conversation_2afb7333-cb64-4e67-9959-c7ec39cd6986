import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors({
  origin: "http://localhost:3000",
  credentials: true
}));
app.use(express.json());

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'CryptoNest Exchange API is running',
    timestamp: new Date().toISOString()
  });
});

// Mock crypto prices with real-time simulation
const mockPrices = {
  BTC: { price: 45000, change: 2.5 },
  ETH: { price: 2800, change: -1.2 },
  USDT: { price: 1.00, change: 0.0 },
  BNB: { price: 300, change: 3.1 }
};

// Simulate price changes
setInterval(() => {
  Object.keys(mockPrices).forEach(symbol => {
    if (symbol !== 'USDT') {
      const change = (Math.random() - 0.5) * 0.02; // ±1% change
      mockPrices[symbol].price *= (1 + change);
      mockPrices[symbol].change = change * 100;
    }
  });
}, 5000); // Update every 5 seconds

app.get('/api/prices', (req, res) => {
  res.json(mockPrices);
});

// Mock user balance
app.get('/api/balance', (req, res) => {
  res.json({
    BTC: 0.00234567,
    ETH: 1.45678901,
    USDT: 1250.00,
    BNB: 5.67890123
  });
});

// Mock trading pairs
app.get('/api/pairs', (req, res) => {
  res.json([
    { symbol: 'BTCUSDT', price: mockPrices.BTC.price, volume: 1234.56, change: mockPrices.BTC.change },
    { symbol: 'ETHUSDT', price: mockPrices.ETH.price, volume: 5678.90, change: mockPrices.ETH.change },
    { symbol: 'BNBUSDT', price: mockPrices.BNB.price, volume: 2345.67, change: mockPrices.BNB.change }
  ]);
});

// Mock order book
app.get('/api/orderbook/:pair', (req, res) => {
  const { pair } = req.params;
  const basePrice = mockPrices[pair.replace('USDT', '')]?.price || 1000;

  const bids = [];
  const asks = [];

  for (let i = 0; i < 10; i++) {
    bids.push({
      price: basePrice * (1 - (i + 1) * 0.001),
      quantity: Math.random() * 10,
      total: 0
    });
    asks.push({
      price: basePrice * (1 + (i + 1) * 0.001),
      quantity: Math.random() * 10,
      total: 0
    });
  }

  res.json({ bids, asks });
});

// Mock place order
app.post('/api/order', (req, res) => {
  const { pair, side, type, quantity, price } = req.body;

  res.json({
    success: true,
    orderId: Math.random().toString(36).substr(2, 9),
    pair,
    side,
    type,
    quantity,
    price,
    status: 'filled',
    timestamp: new Date().toISOString()
  });
});

// Mock order history
app.get('/api/orders', (req, res) => {
  res.json([
    {
      id: '1',
      pair: 'BTCUSDT',
      side: 'buy',
      type: 'market',
      quantity: 0.001,
      price: 45000,
      status: 'filled',
      timestamp: new Date(Date.now() - 3600000).toISOString()
    },
    {
      id: '2',
      pair: 'ETHUSDT',
      side: 'sell',
      type: 'limit',
      quantity: 0.5,
      price: 2800,
      status: 'filled',
      timestamp: new Date(Date.now() - 7200000).toISOString()
    }
  ]);
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 CryptoNest Exchange API running on port ${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`💰 Prices: http://localhost:${PORT}/api/prices`);
  console.log(`📈 Trading pairs: http://localhost:${PORT}/api/pairs`);
});

// Graceful shutdown
process.on('SIGTERM', async () => {
  logger.info('SIGTERM received, shutting down gracefully');
  
  // Close server
  server.close(() => {
    logger.info('HTTP server closed');
  });
  
  // Close database connection
  await prisma.$disconnect();
  logger.info('Database connection closed');
  
  // Close Redis connection
  redis.disconnect();
  logger.info('Redis connection closed');
  
  process.exit(0);
});

process.on('SIGINT', async () => {
  logger.info('SIGINT received, shutting down gracefully');
  
  // Close server
  server.close(() => {
    logger.info('HTTP server closed');
  });
  
  // Close database connection
  await prisma.$disconnect();
  logger.info('Database connection closed');
  
  // Close Redis connection
  redis.disconnect();
  logger.info('Redis connection closed');
  
  process.exit(0);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Start server
const PORT = process.env.PORT || 3001;
server.listen(PORT, () => {
  logger.info(`🚀 WALLETH Backend Server running on port ${PORT}`);
  logger.info(`📊 Environment: ${process.env.NODE_ENV || 'development'}`);
  logger.info(`🔗 WebSocket server active`);
  logger.info(`💾 Database connected`);
  logger.info(`🔴 Redis connected`);
});

export { app, server, io };
