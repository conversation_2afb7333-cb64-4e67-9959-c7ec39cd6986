{"version": 3, "file": "RFC3966.test.js", "names": ["describe", "it", "expect", "formatRFC3966", "number", "to", "should", "equal", "ext", "parseRFC3966", "deep"], "sources": ["../../source/helpers/RFC3966.test.js"], "sourcesContent": ["import { parseRFC3966, formatRFC3966 } from './RFC3966.js'\r\n\r\ndescribe('RFC3966', () => {\r\n\tit('should format', () => {\r\n\t\texpect(() => formatRFC3966({ number: '123' })).to.throw('expects \"number\" to be in E.164 format')\r\n\t\tformatRFC3966({}).should.equal('')\r\n\t\tformatRFC3966({ number: '+78005553535' }).should.equal('tel:+78005553535')\r\n\t\tformatRFC3966({ number: '+78005553535', ext: '123' }).should.equal('tel:+78005553535;ext=123')\r\n\t})\r\n\r\n\tit('should parse', () => {\r\n\t\tparseRFC3966('tel:+78005553535').should.deep.equal({\r\n\t\t\tnumber : '+78005553535'\r\n\t\t})\r\n\r\n\t\tparseRFC3966('tel:+78005553535;ext=123').should.deep.equal({\r\n\t\t\tnumber : '+78005553535',\r\n\t\t\text    : '123'\r\n\t\t})\r\n\r\n\t\t// With `phone-context`\r\n\t\tparseRFC3966('tel:8005553535;ext=123;phone-context=+7').should.deep.equal({\r\n\t\t\tnumber : '+78005553535',\r\n\t\t\text    : '123'\r\n\t\t})\r\n\r\n\t\t// \"Domain contexts\" are ignored\r\n\t\tparseRFC3966('tel:8005553535;ext=123;phone-context=www.leningrad.spb.ru').should.deep.equal({\r\n\t\t\tnumber : '8005553535',\r\n\t\t\text    : '123'\r\n\t\t})\r\n\r\n\t\t// Not a viable phone number.\r\n\t\tparseRFC3966('tel:3').should.deep.equal({})\r\n\t})\r\n})\r\n"], "mappings": ";;AAAA;;AAEAA,QAAQ,CAAC,SAAD,EAAY,YAAM;EACzBC,EAAE,CAAC,eAAD,EAAkB,YAAM;IACzBC,MAAM,CAAC;MAAA,OAAM,IAAAC,kBAAA,EAAc;QAAEC,MAAM,EAAE;MAAV,CAAd,CAAN;IAAA,CAAD,CAAN,CAA+CC,EAA/C,UAAwD,wCAAxD;IACA,IAAAF,kBAAA,EAAc,EAAd,EAAkBG,MAAlB,CAAyBC,KAAzB,CAA+B,EAA/B;IACA,IAAAJ,kBAAA,EAAc;MAAEC,MAAM,EAAE;IAAV,CAAd,EAA0CE,MAA1C,CAAiDC,KAAjD,CAAuD,kBAAvD;IACA,IAAAJ,kBAAA,EAAc;MAAEC,MAAM,EAAE,cAAV;MAA0BI,GAAG,EAAE;IAA/B,CAAd,EAAsDF,MAAtD,CAA6DC,KAA7D,CAAmE,0BAAnE;EACA,CALC,CAAF;EAOAN,EAAE,CAAC,cAAD,EAAiB,YAAM;IACxB,IAAAQ,iBAAA,EAAa,kBAAb,EAAiCH,MAAjC,CAAwCI,IAAxC,CAA6CH,KAA7C,CAAmD;MAClDH,MAAM,EAAG;IADyC,CAAnD;IAIA,IAAAK,iBAAA,EAAa,0BAAb,EAAyCH,MAAzC,CAAgDI,IAAhD,CAAqDH,KAArD,CAA2D;MAC1DH,MAAM,EAAG,cADiD;MAE1DI,GAAG,EAAM;IAFiD,CAA3D,EALwB,CAUxB;;IACA,IAAAC,iBAAA,EAAa,yCAAb,EAAwDH,MAAxD,CAA+DI,IAA/D,CAAoEH,KAApE,CAA0E;MACzEH,MAAM,EAAG,cADgE;MAEzEI,GAAG,EAAM;IAFgE,CAA1E,EAXwB,CAgBxB;;IACA,IAAAC,iBAAA,EAAa,2DAAb,EAA0EH,MAA1E,CAAiFI,IAAjF,CAAsFH,KAAtF,CAA4F;MAC3FH,MAAM,EAAG,YADkF;MAE3FI,GAAG,EAAM;IAFkF,CAA5F,EAjBwB,CAsBxB;;IACA,IAAAC,iBAAA,EAAa,OAAb,EAAsBH,MAAtB,CAA6BI,IAA7B,CAAkCH,KAAlC,CAAwC,EAAxC;EACA,CAxBC,CAAF;AAyBA,CAjCO,CAAR"}