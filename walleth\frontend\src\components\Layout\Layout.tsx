import React, { useState, useEffect } from 'react';
import { Outlet, useLocation } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import {
  Box,
  AppBar,
  Toolbar,
  Drawer,
  IconButton,
  Typography,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  Menu as MenuIcon,
  Close as CloseIcon,
} from '@mui/icons-material';

// Components
import Sidebar from './Sidebar';
import Header from './Header';
import ConnectionStatus from '../UI/ConnectionStatus';

// Store
import { RootState } from '@/types';
import { setSidebarOpen } from '@/store/slices/uiSlice';

const DRAWER_WIDTH = 280;

const Layout: React.FC = () => {
  const dispatch = useDispatch();
  const location = useLocation();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  const { sidebarOpen } = useSelector((state: RootState) => state.ui);
  const { user } = useSelector((state: RootState) => state.auth);
  
  // Close sidebar on mobile when route changes
  useEffect(() => {
    if (isMobile && sidebarOpen) {
      dispatch(setSidebarOpen(false));
    }
  }, [location.pathname, isMobile, dispatch, sidebarOpen]);

  // Handle sidebar toggle
  const handleSidebarToggle = () => {
    dispatch(setSidebarOpen(!sidebarOpen));
  };

  // Determine if current route should show sidebar
  const shouldShowSidebar = !location.pathname.includes('/auth');

  return (
    <Box sx={{ display: 'flex', height: '100vh' }}>
      {/* App Bar */}
      <AppBar
        position="fixed"
        sx={{
          zIndex: theme.zIndex.drawer + 1,
          backgroundColor: 'background.paper',
          borderBottom: '1px solid',
          borderColor: 'divider',
        }}
      >
        <Toolbar sx={{ justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            {shouldShowSidebar && (
              <IconButton
                color="inherit"
                aria-label="toggle sidebar"
                edge="start"
                onClick={handleSidebarToggle}
                sx={{ mr: 2 }}
              >
                {sidebarOpen && !isMobile ? <CloseIcon /> : <MenuIcon />}
              </IconButton>
            )}
            
            <Typography
              variant="h6"
              noWrap
              component="div"
              sx={{
                fontWeight: 700,
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                fontSize: '1.5rem',
                letterSpacing: '0.5px',
              }}
            >
              🏠 CryptoNest
            </Typography>
          </Box>

          <Header />
        </Toolbar>
      </AppBar>

      {/* Sidebar */}
      {shouldShowSidebar && (
        <>
          {/* Desktop Sidebar */}
          {!isMobile && (
            <Drawer
              variant="persistent"
              anchor="left"
              open={sidebarOpen}
              sx={{
                width: sidebarOpen ? DRAWER_WIDTH : 0,
                flexShrink: 0,
                '& .MuiDrawer-paper': {
                  width: DRAWER_WIDTH,
                  boxSizing: 'border-box',
                  backgroundColor: 'background.paper',
                  borderRight: '1px solid',
                  borderColor: 'divider',
                  mt: '64px', // Height of AppBar
                },
              }}
            >
              <Sidebar />
            </Drawer>
          )}

          {/* Mobile Sidebar */}
          {isMobile && (
            <Drawer
              variant="temporary"
              anchor="left"
              open={sidebarOpen}
              onClose={handleSidebarToggle}
              ModalProps={{
                keepMounted: true, // Better open performance on mobile
              }}
              sx={{
                '& .MuiDrawer-paper': {
                  width: DRAWER_WIDTH,
                  boxSizing: 'border-box',
                  backgroundColor: 'background.paper',
                  borderRight: '1px solid',
                  borderColor: 'divider',
                },
              }}
            >
              <Box sx={{ mt: '64px' }}>
                <Sidebar />
              </Box>
            </Drawer>
          )}
        </>
      )}

      {/* Main Content */}
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          display: 'flex',
          flexDirection: 'column',
          mt: '64px', // Height of AppBar
          ml: shouldShowSidebar && sidebarOpen && !isMobile ? 0 : 0,
          transition: theme.transitions.create(['margin'], {
            easing: theme.transitions.easing.sharp,
            duration: theme.transitions.duration.leavingScreen,
          }),
          overflow: 'hidden',
        }}
      >
        {/* Connection Status */}
        <ConnectionStatus />
        
        {/* Page Content */}
        <Box
          sx={{
            flexGrow: 1,
            display: 'flex',
            flexDirection: 'column',
            overflow: 'hidden',
          }}
        >
          <Outlet />
        </Box>
      </Box>
    </Box>
  );
};

export default Layout;
