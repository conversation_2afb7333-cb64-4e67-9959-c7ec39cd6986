import { ValidationOptions } from '../ValidationOptions';
export declare const IS_INSTANCE = "isInstance";
/**
 * Checks if the value is an instance of the specified object.
 */
export declare function isInstance(object: unknown, targetTypeConstructor: new (...args: any[]) => any): boolean;
/**
 * Checks if the value is an instance of the specified object.
 */
export declare function IsInstance(targetType: new (...args: any[]) => any, validationOptions?: ValidationOptions): PropertyDecorator;
