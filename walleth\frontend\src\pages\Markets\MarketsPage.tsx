import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import {
  Box,
  Paper,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TableSortLabel,
  TextField,
  InputAdornment,
  Chip,
  Avatar,
  Grid,
  Card,
  CardContent,
  IconButton,
  Tabs,
  Tab,
} from '@mui/material';
import {
  Search as SearchIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon,
} from '@mui/icons-material';

// Store
import { RootState } from '@/types';
import { fetchTickers, fetchTradingPairs } from '@/store/slices/marketSlice';

type Order = 'asc' | 'desc';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`markets-tabpanel-${index}`}
      aria-labelledby={`markets-tab-${index}`}
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
}

const MarketsPage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  
  const { tickers, tradingPairs, isLoading } = useSelector((state: RootState) => state.market);
  
  const [searchTerm, setSearchTerm] = useState('');
  const [orderBy, setOrderBy] = useState<keyof any>('volume24h');
  const [order, setOrder] = useState<Order>('desc');
  const [tabValue, setTabValue] = useState(0);
  const [favorites, setFavorites] = useState<string[]>([]);

  useEffect(() => {
    dispatch(fetchTickers());
    dispatch(fetchTradingPairs());
  }, [dispatch]);

  const handleRequestSort = (property: keyof any) => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const toggleFavorite = (symbol: string) => {
    setFavorites(prev => 
      prev.includes(symbol) 
        ? prev.filter(s => s !== symbol)
        : [...prev, symbol]
    );
  };

  const formatPrice = (price: number) => {
    return price.toLocaleString('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 8,
    });
  };

  const formatVolume = (volume: number) => {
    if (volume >= 1e9) {
      return `$${(volume / 1e9).toFixed(2)}B`;
    } else if (volume >= 1e6) {
      return `$${(volume / 1e6).toFixed(2)}M`;
    } else if (volume >= 1e3) {
      return `$${(volume / 1e3).toFixed(2)}K`;
    }
    return `$${volume.toFixed(2)}`;
  };

  const formatChange = (change: number) => {
    const isPositive = change >= 0;
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
        {isPositive ? (
          <TrendingUpIcon sx={{ color: 'success.main', fontSize: 16 }} />
        ) : (
          <TrendingDownIcon sx={{ color: 'error.main', fontSize: 16 }} />
        )}
        <Typography
          variant="body2"
          sx={{ color: isPositive ? 'success.main' : 'error.main' }}
        >
          {isPositive ? '+' : ''}{change.toFixed(2)}%
        </Typography>
      </Box>
    );
  };

  const getFilteredTickers = () => {
    let filtered = Object.values(tickers);

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(ticker =>
        ticker.symbol.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply tab filter
    if (tabValue === 1) {
      // Favorites
      filtered = filtered.filter(ticker => favorites.includes(ticker.symbol));
    } else if (tabValue === 2) {
      // USDT pairs
      filtered = filtered.filter(ticker => ticker.symbol.endsWith('USDT'));
    } else if (tabValue === 3) {
      // BTC pairs
      filtered = filtered.filter(ticker => ticker.symbol.endsWith('BTC'));
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue = a[orderBy as keyof typeof a];
      let bValue = b[orderBy as keyof typeof b];

      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = (bValue as string).toLowerCase();
      }

      if (order === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    return filtered;
  };

  const getMarketStats = () => {
    const allTickers = Object.values(tickers);
    const totalVolume = allTickers.reduce((sum, ticker) => sum + ticker.volume24h, 0);
    const gainers = allTickers.filter(ticker => ticker.change24h > 0).length;
    const losers = allTickers.filter(ticker => ticker.change24h < 0).length;
    
    return { totalVolume, gainers, losers, total: allTickers.length };
  };

  const stats = getMarketStats();
  const filteredTickers = getFilteredTickers();

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom>
          Markets
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Real-time cryptocurrency market data and trading pairs
        </Typography>
      </Box>

      {/* Market Stats */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Total Markets
              </Typography>
              <Typography variant="h4" color="primary.main">
                {stats.total}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                24h Volume
              </Typography>
              <Typography variant="h4" color="info.main">
                {formatVolume(stats.totalVolume)}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Gainers
              </Typography>
              <Typography variant="h4" color="success.main">
                {stats.gainers}
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Losers
              </Typography>
              <Typography variant="h4" color="error.main">
                {stats.losers}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Search and Filters */}
      <Paper sx={{ mb: 3 }}>
        <Box sx={{ p: 2, borderBottom: '1px solid', borderColor: 'divider' }}>
          <TextField
            fullWidth
            placeholder="Search markets..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
            sx={{ maxWidth: 400 }}
          />
        </Box>

        <Tabs value={tabValue} onChange={handleTabChange}>
          <Tab label="All Markets" />
          <Tab label={`Favorites (${favorites.length})`} />
          <Tab label="USDT" />
          <Tab label="BTC" />
        </Tabs>
      </Paper>

      {/* Markets Table */}
      <TabPanel value={tabValue} index={tabValue}>
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>
                  <TableSortLabel
                    active={orderBy === 'symbol'}
                    direction={orderBy === 'symbol' ? order : 'asc'}
                    onClick={() => handleRequestSort('symbol')}
                  >
                    Market
                  </TableSortLabel>
                </TableCell>
                <TableCell align="right">
                  <TableSortLabel
                    active={orderBy === 'price'}
                    direction={orderBy === 'price' ? order : 'asc'}
                    onClick={() => handleRequestSort('price')}
                  >
                    Price
                  </TableSortLabel>
                </TableCell>
                <TableCell align="right">
                  <TableSortLabel
                    active={orderBy === 'change24h'}
                    direction={orderBy === 'change24h' ? order : 'asc'}
                    onClick={() => handleRequestSort('change24h')}
                  >
                    24h Change
                  </TableSortLabel>
                </TableCell>
                <TableCell align="right">
                  <TableSortLabel
                    active={orderBy === 'high24h'}
                    direction={orderBy === 'high24h' ? order : 'asc'}
                    onClick={() => handleRequestSort('high24h')}
                  >
                    24h High
                  </TableSortLabel>
                </TableCell>
                <TableCell align="right">
                  <TableSortLabel
                    active={orderBy === 'low24h'}
                    direction={orderBy === 'low24h' ? order : 'asc'}
                    onClick={() => handleRequestSort('low24h')}
                  >
                    24h Low
                  </TableSortLabel>
                </TableCell>
                <TableCell align="right">
                  <TableSortLabel
                    active={orderBy === 'volume24h'}
                    direction={orderBy === 'volume24h' ? order : 'asc'}
                    onClick={() => handleRequestSort('volume24h')}
                  >
                    24h Volume
                  </TableSortLabel>
                </TableCell>
                <TableCell align="center">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredTickers.map((ticker) => (
                <TableRow
                  key={ticker.symbol}
                  hover
                  sx={{ cursor: 'pointer' }}
                  onClick={() => navigate(`/trading/${ticker.symbol.toLowerCase()}`)}
                >
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Typography variant="body1" fontWeight={600} sx={{ mr: 1 }}>
                        {ticker.symbol}
                      </Typography>
                      {ticker.change24h > 5 && (
                        <Chip label="🔥" size="small" color="success" />
                      )}
                    </Box>
                  </TableCell>
                  <TableCell align="right">
                    <Typography variant="body1" fontWeight={600}>
                      {formatPrice(ticker.price)}
                    </Typography>
                  </TableCell>
                  <TableCell align="right">
                    {formatChange(ticker.change24h)}
                  </TableCell>
                  <TableCell align="right">
                    <Typography variant="body2">
                      {formatPrice(ticker.high24h)}
                    </Typography>
                  </TableCell>
                  <TableCell align="right">
                    <Typography variant="body2">
                      {formatPrice(ticker.low24h)}
                    </Typography>
                  </TableCell>
                  <TableCell align="right">
                    <Typography variant="body2">
                      {formatVolume(ticker.volume24h)}
                    </Typography>
                  </TableCell>
                  <TableCell align="center">
                    <IconButton
                      size="small"
                      onClick={(e) => {
                        e.stopPropagation();
                        toggleFavorite(ticker.symbol);
                      }}
                    >
                      {favorites.includes(ticker.symbol) ? (
                        <StarIcon color="warning" />
                      ) : (
                        <StarBorderIcon />
                      )}
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </TabPanel>

      {filteredTickers.length === 0 && (
        <Box
          sx={{
            textAlign: 'center',
            py: 8,
          }}
        >
          <Typography variant="h6" color="text.secondary">
            No markets found
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Try adjusting your search or filter criteria
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default MarketsPage;
