import express from 'express';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import speakeasy from 'speakeasy';
import { PrismaClient } from '@prisma/client';
import { body, validationResult } from 'express-validator';
import { asyncHand<PERSON> } from '../middleware/errorHandler';
import { ValidationError, AuthenticationError, ConflictError } from '../middleware/errorHandler';
import { logger } from '../utils/logger';
import { authMiddleware } from '../middleware/auth';

const router = express.Router();
const prisma = new PrismaClient();

// Validation rules
const registerValidation = [
  body('email').isEmail().normalizeEmail().withMessage('Valid email is required'),
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
  body('firstName').optional().isLength({ min: 1 }).withMessage('First name is required'),
  body('lastName').optional().isLength({ min: 1 }).withMessage('Last name is required'),
  body('referralCode').optional().isString()
];

const loginValidation = [
  body('email').isEmail().normalizeEmail().withMessage('Valid email is required'),
  body('password').notEmpty().withMessage('Password is required'),
  body('twoFactorCode').optional().isLength({ min: 6, max: 6 }).withMessage('2FA code must be 6 digits')
];

// Helper function to generate JWT tokens
const generateTokens = (userId: string, email: string, role: string) => {
  const accessToken = jwt.sign(
    { userId, email, role },
    process.env.JWT_SECRET || 'fallback-secret',
    { expiresIn: process.env.JWT_EXPIRES_IN || '7d' }
  );

  const refreshToken = jwt.sign(
    { userId, email, role },
    process.env.REFRESH_TOKEN_SECRET || 'fallback-refresh-secret',
    { expiresIn: process.env.REFRESH_TOKEN_EXPIRES_IN || '30d' }
  );

  return { accessToken, refreshToken };
};

// Register endpoint
router.post('/register', registerValidation, asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError(errors.array().map(err => err.msg).join(', '));
  }

  const { email, password, firstName, lastName, referralCode } = req.body;

  // Check if user already exists
  const existingUser = await prisma.user.findUnique({
    where: { email }
  });

  if (existingUser) {
    throw new ConflictError('User with this email already exists');
  }

  // Validate referral code if provided
  let referrerId = null;
  if (referralCode) {
    const referrer = await prisma.user.findUnique({
      where: { referralCode }
    });
    if (!referrer) {
      throw new ValidationError('Invalid referral code');
    }
    referrerId = referrer.id;
  }

  // Hash password
  const hashedPassword = await bcrypt.hash(password, parseInt(process.env.BCRYPT_ROUNDS || '12'));

  // Generate unique referral code for new user
  const generateReferralCode = () => {
    return Math.random().toString(36).substring(2, 8).toUpperCase();
  };

  let newReferralCode = generateReferralCode();
  let isUnique = false;
  while (!isUnique) {
    const existing = await prisma.user.findUnique({
      where: { referralCode: newReferralCode }
    });
    if (!existing) {
      isUnique = true;
    } else {
      newReferralCode = generateReferralCode();
    }
  }

  // Create user
  const user = await prisma.user.create({
    data: {
      email,
      password: hashedPassword,
      firstName,
      lastName,
      referralCode: newReferralCode,
      referredBy: referrerId
    },
    select: {
      id: true,
      email: true,
      firstName: true,
      lastName: true,
      role: true,
      kycStatus: true,
      isEmailVerified: true,
      referralCode: true,
      createdAt: true
    }
  });

  // Generate tokens
  const { accessToken, refreshToken } = generateTokens(user.id, user.email, user.role);

  // Log registration
  logger.info(`New user registered: ${user.email}`, { userId: user.id });

  res.status(201).json({
    success: true,
    message: 'User registered successfully',
    data: {
      user,
      accessToken,
      refreshToken
    }
  });
}));

// Login endpoint
router.post('/login', loginValidation, asyncHandler(async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    throw new ValidationError(errors.array().map(err => err.msg).join(', '));
  }

  const { email, password, twoFactorCode } = req.body;
  const clientIP = req.ip;
  const userAgent = req.get('User-Agent') || '';

  // Find user
  const user = await prisma.user.findUnique({
    where: { email }
  });

  if (!user) {
    throw new AuthenticationError('Invalid email or password');
  }

  // Check password
  const isPasswordValid = await bcrypt.compare(password, user.password);
  if (!isPasswordValid) {
    // Log failed login attempt
    await prisma.loginHistory.create({
      data: {
        userId: user.id,
        ipAddress: clientIP,
        userAgent,
        success: false
      }
    });
    throw new AuthenticationError('Invalid email or password');
  }

  // Check if account is active
  if (user.status !== 'ACTIVE') {
    throw new AuthenticationError('Account is suspended or banned');
  }

  // Check 2FA if enabled
  if (user.is2FAEnabled) {
    if (!twoFactorCode) {
      throw new ValidationError('2FA code is required');
    }

    if (!user.twoFactorSecret) {
      throw new AuthenticationError('2FA not properly configured');
    }

    const verified = speakeasy.totp.verify({
      secret: user.twoFactorSecret,
      encoding: 'base32',
      token: twoFactorCode,
      window: 2
    });

    if (!verified) {
      throw new AuthenticationError('Invalid 2FA code');
    }
  }

  // Update last login
  await prisma.user.update({
    where: { id: user.id },
    data: {
      lastLoginAt: new Date(),
      lastLoginIP: clientIP
    }
  });

  // Log successful login
  await prisma.loginHistory.create({
    data: {
      userId: user.id,
      ipAddress: clientIP,
      userAgent,
      success: true
    }
  });

  // Generate tokens
  const { accessToken, refreshToken } = generateTokens(user.id, user.email, user.role);

  // Return user data (excluding sensitive information)
  const userData = {
    id: user.id,
    email: user.email,
    firstName: user.firstName,
    lastName: user.lastName,
    role: user.role,
    kycStatus: user.kycStatus,
    isEmailVerified: user.isEmailVerified,
    is2FAEnabled: user.is2FAEnabled,
    referralCode: user.referralCode,
    createdAt: user.createdAt
  };

  logger.info(`User logged in: ${user.email}`, { userId: user.id, ip: clientIP });

  res.json({
    success: true,
    message: 'Login successful',
    data: {
      user: userData,
      accessToken,
      refreshToken
    }
  });
}));

// Refresh token endpoint
router.post('/refresh', asyncHandler(async (req, res) => {
  const { refreshToken } = req.body;

  if (!refreshToken) {
    throw new ValidationError('Refresh token is required');
  }

  try {
    const decoded = jwt.verify(
      refreshToken,
      process.env.REFRESH_TOKEN_SECRET || 'fallback-refresh-secret'
    ) as any;

    // Verify user still exists and is active
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId }
    });

    if (!user || user.status !== 'ACTIVE') {
      throw new AuthenticationError('Invalid refresh token');
    }

    // Generate new tokens
    const { accessToken, refreshToken: newRefreshToken } = generateTokens(
      user.id,
      user.email,
      user.role
    );

    res.json({
      success: true,
      message: 'Tokens refreshed successfully',
      data: {
        accessToken,
        refreshToken: newRefreshToken
      }
    });
  } catch (error) {
    throw new AuthenticationError('Invalid refresh token');
  }
}));

// Logout endpoint
router.post('/logout', authMiddleware, asyncHandler(async (req, res) => {
  // In a production environment, you might want to blacklist the token
  // For now, we'll just return a success message
  
  logger.info(`User logged out: ${req.user?.email}`, { userId: req.user?.id });

  res.json({
    success: true,
    message: 'Logged out successfully'
  });
}));

// Get current user endpoint
router.get('/me', authMiddleware, asyncHandler(async (req, res) => {
  const user = await prisma.user.findUnique({
    where: { id: req.user!.id },
    select: {
      id: true,
      email: true,
      firstName: true,
      lastName: true,
      phone: true,
      dateOfBirth: true,
      country: true,
      role: true,
      kycStatus: true,
      isEmailVerified: true,
      isPhoneVerified: true,
      is2FAEnabled: true,
      referralCode: true,
      createdAt: true,
      updatedAt: true
    }
  });

  if (!user) {
    throw new AuthenticationError('User not found');
  }

  res.json({
    success: true,
    data: { user }
  });
}));

export default router;
