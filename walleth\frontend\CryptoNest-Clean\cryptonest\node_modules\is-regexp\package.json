{"name": "is-regexp", "version": "1.0.0", "description": "Check whether a variable is a regular expression", "license": "MIT", "repository": "sindresorhus/is-regexp", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js"], "keywords": ["regex", "regexp", "regular", "expression", "regular expression", "re", "check", "type", "is"], "devDependencies": {"mocha": "*"}}