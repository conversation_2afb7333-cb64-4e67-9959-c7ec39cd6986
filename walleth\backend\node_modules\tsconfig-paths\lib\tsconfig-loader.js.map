{"version": 3, "file": "tsconfig-loader.js", "sourceRoot": "", "sources": ["../src/tsconfig-loader.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA,2BAA6B;AAC7B,uBAAyB;AACzB,iEAAiE;AACjE,6BAAgC;AAChC,iEAAiE;AACjE,oCAAuC;AA8BvC,SAAgB,cAAc,CAAC,EAIR;QAHrB,MAAM,YAAA,EACN,GAAG,SAAA,EACH,gBAA0B,EAA1B,QAAQ,mBAAG,eAAe,KAAA;IAE1B,IAAM,eAAe,GAAG,MAAM,CAAC,iBAAiB,CAAC,CAAC;IAClD,IAAM,eAAe,GAAG,MAAM,CAAC,iBAAiB,CAAC,CAAC;IAElD,sEAAsE;IACtE,8DAA8D;IAC9D,IAAM,UAAU,GAAG,QAAQ,CAAC,GAAG,EAAE,eAAe,EAAE,eAAe,CAAC,CAAC;IACnE,OAAO,UAAU,CAAC;AACpB,CAAC;AAZD,wCAYC;AAED,SAAS,eAAe,CACtB,GAAW,EACX,QAAiB,EACjB,OAAgB;IAEhB,2FAA2F;IAE3F,IAAM,UAAU,GAAG,iBAAiB,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;IAEpD,IAAI,CAAC,UAAU,EAAE;QACf,OAAO;YACL,YAAY,EAAE,SAAS;YACvB,OAAO,EAAE,SAAS;YAClB,KAAK,EAAE,SAAS;SACjB,CAAC;KACH;IACD,IAAM,MAAM,GAAG,YAAY,CAAC,UAAU,CAAC,CAAC;IAExC,OAAO;QACL,YAAY,EAAE,UAAU;QACxB,OAAO,EACL,OAAO;YACP,CAAC,MAAM,IAAI,MAAM,CAAC,eAAe,IAAI,MAAM,CAAC,eAAe,CAAC,OAAO,CAAC;QACtE,KAAK,EAAE,MAAM,IAAI,MAAM,CAAC,eAAe,IAAI,MAAM,CAAC,eAAe,CAAC,KAAK;KACxE,CAAC;AACJ,CAAC;AAED,SAAS,iBAAiB,CAAC,GAAW,EAAE,QAAiB;IACvD,IAAI,QAAQ,EAAE;QACZ,IAAM,YAAY,GAAG,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE;YACvD,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,iBAAiB,CAAC;YAC3C,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;QAEhC,OAAO,YAAY,CAAC;KACrB;IAED,IAAI,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;KAC1B;IAED,IAAM,kBAAkB,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC;IAChD,OAAO,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;AAC3E,CAAC;AACD,SAAgB,eAAe,CAC7B,SAAiB,EACjB,WAAwD;IAAxD,4BAAA,EAAA,cAA0C,EAAE,CAAC,WAAW;IAExD,IAAM,KAAK,GAAG,WAAW,CAAC,SAAS,CAAC,CAAC;IACrC,IAAM,YAAY,GAAG,CAAC,eAAe,EAAE,eAAe,CAAC,CAAC;IACxD,KAA0B,UAAY,EAAZ,6BAAY,EAAZ,0BAAY,EAAZ,IAAY,EAAE;QAAnC,IAAM,WAAW,qBAAA;QACpB,IAAI,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE;YACrC,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;SAC1C;KACF;IAED,IAAM,eAAe,GAAG,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IAEhD,wBAAwB;IACxB,IAAI,SAAS,KAAK,eAAe,EAAE;QACjC,OAAO,SAAS,CAAC;KAClB;IAED,OAAO,eAAe,CAAC,eAAe,EAAE,WAAW,CAAC,CAAC;AACvD,CAAC;AApBD,0CAoBC;AAED,SAAgB,YAAY,CAC1B,cAAsB;AACtB,qCAAqC;AACrC,UAAqD,EACrD,YACmC;IAFnC,2BAAA,EAAA,aAAwC,EAAE,CAAC,UAAU;IACrD,6BAAA,EAAA,yBAA8C,QAAgB;QAC5D,OAAA,EAAE,CAAC,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC;IAAjC,CAAiC;IAEnC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE;QAC/B,OAAO,SAAS,CAAC;KAClB;IAED,IAAM,YAAY,GAAG,YAAY,CAAC,cAAc,CAAC,CAAC;IAClD,IAAM,WAAW,GAAG,QAAQ,CAAC,YAAY,CAAC,CAAC;IAC3C,IAAI,MAAgB,CAAC;IACrB,IAAI;QACF,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;KACnC;IAAC,OAAO,CAAC,EAAE;QACV,MAAM,IAAI,KAAK,CAAC,UAAG,cAAc,2BAAiB,CAAC,CAAC,OAAO,CAAE,CAAC,CAAC;KAChE;IAED,IAAI,cAAc,GAAG,MAAM,CAAC,OAAO,CAAC;IACpC,IAAI,cAAc,EAAE;QAClB,IAAI,IAAI,SAAU,CAAC;QAEnB,IAAI,KAAK,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE;YACjC,IAAI,GAAG,cAAc,CAAC,MAAM,CAC1B,UAAC,QAAQ,EAAE,qBAAqB;gBAC9B,OAAA,cAAc,CACZ,QAAQ,EACR,uBAAuB,CACrB,cAAc,EACd,qBAAqB,EACrB,UAAU,EACV,YAAY,CACb,CACF;YARD,CAQC,EACH,EAAE,CACH,CAAC;SACH;aAAM;YACL,IAAI,GAAG,uBAAuB,CAC5B,cAAc,EACd,cAAc,EACd,UAAU,EACV,YAAY,CACb,CAAC;SACH;QAED,OAAO,cAAc,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;KACrC;IACD,OAAO,MAAM,CAAC;AAChB,CAAC;AAlDD,oCAkDC;AAED;;;GAGG;AACH,SAAS,uBAAuB,CAC9B,cAAsB,EACtB,mBAA2B;AAC3B,qCAAqC;AACrC,UAAqC,EACrC,YAA0C;;IAE1C,IACE,OAAO,mBAAmB,KAAK,QAAQ;QACvC,mBAAmB,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAC3C;QACA,mBAAmB,IAAI,OAAO,CAAC;KAChC;IACD,IAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;IAChD,IAAI,kBAAkB,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,mBAAmB,CAAC,CAAC;IACpE,IACE,mBAAmB,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACvC,mBAAmB,CAAC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;QACvC,CAAC,UAAU,CAAC,kBAAkB,CAAC,EAC/B;QACA,kBAAkB,GAAG,IAAI,CAAC,IAAI,CAC5B,UAAU,EACV,cAAc,EACd,mBAAmB,CACpB,CAAC;KACH;IAED,IAAM,MAAM,GACV,YAAY,CAAC,kBAAkB,EAAE,UAAU,EAAE,YAAY,CAAC,IAAI,EAAE,CAAC;IAEnE,mEAAmE;IACnE,mFAAmF;IACnF,IAAI,MAAA,MAAM,CAAC,eAAe,0CAAE,OAAO,EAAE;QACnC,IAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC;QACrD,MAAM,CAAC,eAAe,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,CACxC,UAAU,EACV,MAAM,CAAC,eAAe,CAAC,OAAO,CAC/B,CAAC;KACH;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,cAAc,CACrB,IAA0B,EAC1B,MAA4B;IAE5B,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;IAClB,MAAM,GAAG,MAAM,IAAI,EAAE,CAAC;IAEtB,sCACK,IAAI,GACJ,MAAM,KACT,eAAe,wBACV,IAAI,CAAC,eAAe,GACpB,MAAM,CAAC,eAAe,KAE3B;AACJ,CAAC"}