/* CryptoNest Custom Theme Variables */
:root {
  /* Primary Gradient Colors */
  --cn-primary-50: #f3f4ff;
  --cn-primary-100: #e8ebff;
  --cn-primary-200: #d1d9ff;
  --cn-primary-300: #b4c2ff;
  --cn-primary-400: #94a7ff;
  --cn-primary-500: #667eea;
  --cn-primary-600: #4c63d2;
  --cn-primary-700: #3b4fb8;
  --cn-primary-800: #2d3e9e;
  --cn-primary-900: #1f2d84;

  /* Secondary Gradient Colors */
  --cn-secondary-50: #f8f5ff;
  --cn-secondary-100: #f1ebff;
  --cn-secondary-200: #e3d7ff;
  --cn-secondary-300: #d1bfff;
  --cn-secondary-400: #b39dff;
  --cn-secondary-500: #764ba2;
  --cn-secondary-600: #512da8;
  --cn-secondary-700: #3f1a78;
  --cn-secondary-800: #2e0854;
  --cn-secondary-900: #1d0030;

  /* Tertiary Gradient Colors */
  --cn-tertiary-50: #fff5ff;
  --cn-tertiary-100: #ffe8ff;
  --cn-tertiary-200: #ffd1ff;
  --cn-tertiary-300: #ffb4ff;
  --cn-tertiary-400: #ff93ff;
  --cn-tertiary-500: #f093fb;
  --cn-tertiary-600: #e91e63;
  --cn-tertiary-700: #c2185b;
  --cn-tertiary-800: #ad1457;
  --cn-tertiary-900: #880e4f;

  /* Gradient Combinations */
  --cn-gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --cn-gradient-secondary: linear-gradient(135deg, #764ba2 0%, #f093fb 100%);
  --cn-gradient-tertiary: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  --cn-gradient-background: linear-gradient(135deg, #0f0c29 0%, #24243e 50%, #302b63 100%);
  --cn-gradient-card: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
  --cn-gradient-button: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --cn-gradient-button-hover: linear-gradient(135deg, #4c63d2 0%, #512da8 100%);
  --cn-gradient-text: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);

  /* Background Colors */
  --cn-bg-default: #0f0c29;
  --cn-bg-paper: rgba(26, 26, 46, 0.95);
  --cn-bg-card: rgba(26, 26, 46, 0.8);
  --cn-bg-overlay: rgba(15, 12, 41, 0.9);

  /* Text Colors */
  --cn-text-primary: #ffffff;
  --cn-text-secondary: rgba(255, 255, 255, 0.8);
  --cn-text-disabled: rgba(255, 255, 255, 0.5);
  --cn-text-hint: rgba(255, 255, 255, 0.6);

  /* Border and Divider Colors */
  --cn-divider: rgba(255, 255, 255, 0.15);
  --cn-border: rgba(255, 255, 255, 0.12);

  /* Trading Colors */
  --cn-trading-buy: #4caf50;
  --cn-trading-sell: #f44336;
  --cn-trading-buy-gradient: linear-gradient(135deg, #4caf50 0%, #8bc34a 100%);
  --cn-trading-sell-gradient: linear-gradient(135deg, #f44336 0%, #e91e63 100%);
  --cn-trading-neutral: #9e9e9e;

  /* Status Colors */
  --cn-success: #4caf50;
  --cn-error: #f44336;
  --cn-warning: #ff9800;
  --cn-info: #2196f3;

  /* Chart Colors */
  --cn-chart-bullish: #4caf50;
  --cn-chart-bearish: #f44336;
  --cn-chart-volume: #667eea;
  --cn-chart-grid: rgba(255, 255, 255, 0.1);
  --cn-chart-crosshair: #667eea;

  /* Shadow and Effects */
  --cn-shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
  --cn-shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);
  --cn-shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.3);
  --cn-shadow-xl: 0 12px 48px rgba(0, 0, 0, 0.4);

  /* Border Radius */
  --cn-radius-sm: 8px;
  --cn-radius-md: 12px;
  --cn-radius-lg: 16px;
  --cn-radius-xl: 24px;

  /* Spacing */
  --cn-space-xs: 4px;
  --cn-space-sm: 8px;
  --cn-space-md: 16px;
  --cn-space-lg: 24px;
  --cn-space-xl: 32px;
  --cn-space-2xl: 48px;

  /* Typography */
  --cn-font-family: 'Inter', 'Roboto', 'Helvetica', 'Arial', sans-serif;
  --cn-font-size-xs: 0.75rem;
  --cn-font-size-sm: 0.875rem;
  --cn-font-size-base: 1rem;
  --cn-font-size-lg: 1.125rem;
  --cn-font-size-xl: 1.25rem;
  --cn-font-size-2xl: 1.5rem;
  --cn-font-size-3xl: 1.875rem;
  --cn-font-size-4xl: 2.25rem;

  /* Z-Index */
  --cn-z-dropdown: 1000;
  --cn-z-sticky: 1020;
  --cn-z-fixed: 1030;
  --cn-z-modal-backdrop: 1040;
  --cn-z-modal: 1050;
  --cn-z-popover: 1060;
  --cn-z-tooltip: 1070;
  --cn-z-toast: 1080;
}

/* CryptoNest Utility Classes */
.cn-gradient-text {
  background: var(--cn-gradient-text);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.cn-gradient-bg {
  background: var(--cn-gradient-background);
}

.cn-gradient-card {
  background: var(--cn-gradient-card);
}

.cn-gradient-button {
  background: var(--cn-gradient-button);
}

.cn-gradient-button:hover {
  background: var(--cn-gradient-button-hover);
}

.cn-glass-effect {
  background: var(--cn-bg-paper);
  backdrop-filter: blur(20px);
  border: 1px solid var(--cn-border);
}

.cn-trading-buy {
  color: var(--cn-trading-buy);
}

.cn-trading-sell {
  color: var(--cn-trading-sell);
}

.cn-shadow-glow {
  box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
}

.cn-shadow-glow-secondary {
  box-shadow: 0 0 20px rgba(118, 75, 162, 0.3);
}

.cn-shadow-glow-tertiary {
  box-shadow: 0 0 20px rgba(240, 147, 251, 0.3);
}

/* Animation Classes */
.cn-fade-in {
  animation: cnFadeIn 0.3s ease-in-out;
}

.cn-slide-up {
  animation: cnSlideUp 0.3s ease-out;
}

.cn-scale-in {
  animation: cnScaleIn 0.2s ease-out;
}

.cn-pulse {
  animation: cnPulse 2s infinite;
}

.cn-float {
  animation: cnFloat 3s ease-in-out infinite;
}

/* Keyframe Animations */
@keyframes cnFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes cnSlideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes cnScaleIn {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes cnPulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

@keyframes cnFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Responsive Breakpoints */
@media (max-width: 640px) {
  :root {
    --cn-space-md: 12px;
    --cn-space-lg: 16px;
    --cn-space-xl: 24px;
  }
}

@media (max-width: 768px) {
  :root {
    --cn-font-size-3xl: 1.5rem;
    --cn-font-size-4xl: 1.875rem;
  }
}

/* Dark Mode Enhancements */
@media (prefers-color-scheme: dark) {
  :root {
    --cn-bg-default: #0a0a0a;
    --cn-bg-paper: rgba(20, 20, 30, 0.95);
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  :root {
    --cn-border: rgba(255, 255, 255, 0.3);
    --cn-divider: rgba(255, 255, 255, 0.3);
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .cn-fade-in,
  .cn-slide-up,
  .cn-scale-in,
  .cn-pulse,
  .cn-float {
    animation: none;
  }
}
