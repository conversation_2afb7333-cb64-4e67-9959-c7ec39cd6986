// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User Management
model User {
  id                String    @id @default(cuid())
  email             String    @unique
  username          String?   @unique
  password          String
  firstName         String?
  lastName          String?
  phone             String?
  dateOfBirth       DateTime?
  country           String?
  isEmailVerified   Boolean   @default(false)
  isPhoneVerified   Boolean   @default(false)
  is2FAEnabled      Boolean   @default(false)
  twoFactorSecret   String?
  kycStatus         KYCStatus @default(PENDING)
  kycLevel          Int       @default(0)
  role              UserRole  @default(USER)
  status            UserStatus @default(ACTIVE)
  lastLoginAt       DateTime?
  lastLoginIP       String?
  referralCode      String?   @unique
  referredBy        String?
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  // Relations
  wallets           Wallet[]
  orders            Order[]
  trades            Trade[]
  deposits          Deposit[]
  withdrawals       Withdrawal[]
  kycDocuments      KYCDocument[]
  loginHistory      LoginHistory[]
  notifications     Notification[]
  apiKeys           ApiKey[]
  referrals         User[]    @relation("UserReferrals")
  referrer          User?     @relation("UserReferrals", fields: [referredBy], references: [id])

  @@map("users")
}

enum UserRole {
  USER
  ADMIN
  SUPER_ADMIN
}

enum UserStatus {
  ACTIVE
  SUSPENDED
  BANNED
  PENDING_VERIFICATION
}

enum KYCStatus {
  PENDING
  SUBMITTED
  APPROVED
  REJECTED
  EXPIRED
}

// KYC Documents
model KYCDocument {
  id          String      @id @default(cuid())
  userId      String
  type        DocumentType
  fileName    String
  filePath    String
  status      DocumentStatus @default(PENDING)
  rejectionReason String?
  submittedAt DateTime    @default(now())
  reviewedAt  DateTime?
  reviewedBy  String?

  user        User        @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("kyc_documents")
}

enum DocumentType {
  PASSPORT
  DRIVERS_LICENSE
  NATIONAL_ID
  PROOF_OF_ADDRESS
  SELFIE
}

enum DocumentStatus {
  PENDING
  APPROVED
  REJECTED
}

// Cryptocurrencies
model Currency {
  id              String    @id @default(cuid())
  symbol          String    @unique
  name            String
  decimals        Int       @default(8)
  isActive        Boolean   @default(true)
  isFiat          Boolean   @default(false)
  network         String?
  contractAddress String?
  iconUrl         String?
  minDeposit      Decimal   @default(0) @db.Decimal(20, 8)
  minWithdrawal   Decimal   @default(0) @db.Decimal(20, 8)
  withdrawalFee   Decimal   @default(0) @db.Decimal(20, 8)
  confirmations   Int       @default(6)
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relations
  wallets         Wallet[]
  tradingPairs1   TradingPair[] @relation("BaseCurrency")
  tradingPairs2   TradingPair[] @relation("QuoteCurrency")
  deposits        Deposit[]
  withdrawals     Withdrawal[]

  @@map("currencies")
}

// Trading Pairs
model TradingPair {
  id              String    @id @default(cuid())
  symbol          String    @unique // e.g., "BTCUSDT"
  baseCurrencyId  String
  quoteCurrencyId String
  isActive        Boolean   @default(true)
  minTradeAmount  Decimal   @default(0) @db.Decimal(20, 8)
  maxTradeAmount  Decimal   @default(0) @db.Decimal(20, 8)
  priceDecimals   Int       @default(8)
  amountDecimals  Int       @default(8)
  makerFee        Decimal   @default(0.001) @db.Decimal(10, 6)
  takerFee        Decimal   @default(0.001) @db.Decimal(10, 6)
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relations
  baseCurrency    Currency  @relation("BaseCurrency", fields: [baseCurrencyId], references: [id])
  quoteCurrency   Currency  @relation("QuoteCurrency", fields: [quoteCurrencyId], references: [id])
  orders          Order[]
  trades          Trade[]
  priceHistory    PriceHistory[]

  @@map("trading_pairs")
}

// Wallets
model Wallet {
  id              String    @id @default(cuid())
  userId          String
  currencyId      String
  address         String?
  balance         Decimal   @default(0) @db.Decimal(20, 8)
  lockedBalance   Decimal   @default(0) @db.Decimal(20, 8)
  isActive        Boolean   @default(true)
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relations
  user            User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  currency        Currency  @relation(fields: [currencyId], references: [id])
  deposits        Deposit[]
  withdrawals     Withdrawal[]

  @@unique([userId, currencyId])
  @@map("wallets")
}

// Orders
model Order {
  id              String      @id @default(cuid())
  userId          String
  tradingPairId   String
  type            OrderType
  side            OrderSide
  amount          Decimal     @db.Decimal(20, 8)
  price           Decimal?    @db.Decimal(20, 8)
  stopPrice       Decimal?    @db.Decimal(20, 8)
  filledAmount    Decimal     @default(0) @db.Decimal(20, 8)
  status          OrderStatus @default(PENDING)
  timeInForce     TimeInForce @default(GTC)
  expiresAt       DateTime?
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt

  // Relations
  user            User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  tradingPair     TradingPair @relation(fields: [tradingPairId], references: [id])
  trades          Trade[]

  @@map("orders")
}

enum OrderType {
  MARKET
  LIMIT
  STOP_LOSS
  TAKE_PROFIT
  STOP_LIMIT
}

enum OrderSide {
  BUY
  SELL
}

enum OrderStatus {
  PENDING
  PARTIAL_FILLED
  FILLED
  CANCELLED
  REJECTED
  EXPIRED
}

enum TimeInForce {
  GTC // Good Till Cancelled
  IOC // Immediate or Cancel
  FOK // Fill or Kill
}

// Trades
model Trade {
  id              String      @id @default(cuid())
  buyOrderId      String
  sellOrderId     String
  tradingPairId   String
  buyUserId       String
  sellUserId      String
  amount          Decimal     @db.Decimal(20, 8)
  price           Decimal     @db.Decimal(20, 8)
  buyerFee        Decimal     @db.Decimal(20, 8)
  sellerFee       Decimal     @db.Decimal(20, 8)
  createdAt       DateTime    @default(now())

  // Relations
  buyOrder        Order       @relation(fields: [buyOrderId], references: [id])
  sellOrder       Order       @relation(fields: [sellOrderId], references: [id])
  tradingPair     TradingPair @relation(fields: [tradingPairId], references: [id])
  buyer           User        @relation(fields: [buyUserId], references: [id])
  seller          User        @relation(fields: [sellUserId], references: [id])

  @@map("trades")
}

// Deposits
model Deposit {
  id              String        @id @default(cuid())
  userId          String
  walletId        String
  currencyId      String
  amount          Decimal       @db.Decimal(20, 8)
  txHash          String?
  address         String
  confirmations   Int           @default(0)
  requiredConfirmations Int     @default(6)
  status          DepositStatus @default(PENDING)
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  // Relations
  user            User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  wallet          Wallet        @relation(fields: [walletId], references: [id])
  currency        Currency      @relation(fields: [currencyId], references: [id])

  @@map("deposits")
}

enum DepositStatus {
  PENDING
  CONFIRMING
  COMPLETED
  FAILED
}

// Withdrawals
model Withdrawal {
  id              String          @id @default(cuid())
  userId          String
  walletId        String
  currencyId      String
  amount          Decimal         @db.Decimal(20, 8)
  fee             Decimal         @db.Decimal(20, 8)
  address         String
  txHash          String?
  status          WithdrawalStatus @default(PENDING)
  rejectionReason String?
  processedAt     DateTime?
  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt

  // Relations
  user            User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  wallet          Wallet          @relation(fields: [walletId], references: [id])
  currency        Currency        @relation(fields: [currencyId], references: [id])

  @@map("withdrawals")
}

enum WithdrawalStatus {
  PENDING
  APPROVED
  PROCESSING
  COMPLETED
  REJECTED
  FAILED
}

// Price History
model PriceHistory {
  id              String      @id @default(cuid())
  tradingPairId   String
  open            Decimal     @db.Decimal(20, 8)
  high            Decimal     @db.Decimal(20, 8)
  low             Decimal     @db.Decimal(20, 8)
  close           Decimal     @db.Decimal(20, 8)
  volume          Decimal     @db.Decimal(20, 8)
  timestamp       DateTime
  interval        String      // 1m, 5m, 15m, 1h, 4h, 1d, etc.

  // Relations
  tradingPair     TradingPair @relation(fields: [tradingPairId], references: [id])

  @@unique([tradingPairId, timestamp, interval])
  @@map("price_history")
}

// Login History
model LoginHistory {
  id          String    @id @default(cuid())
  userId      String
  ipAddress   String
  userAgent   String?
  location    String?
  success     Boolean
  createdAt   DateTime  @default(now())

  // Relations
  user        User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("login_history")
}

// Notifications
model Notification {
  id          String            @id @default(cuid())
  userId      String
  type        NotificationType
  title       String
  message     String
  isRead      Boolean           @default(false)
  data        Json?
  createdAt   DateTime          @default(now())

  // Relations
  user        User              @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notifications")
}

enum NotificationType {
  TRADE_EXECUTED
  ORDER_FILLED
  DEPOSIT_COMPLETED
  WITHDRAWAL_COMPLETED
  KYC_APPROVED
  KYC_REJECTED
  SECURITY_ALERT
  SYSTEM_MAINTENANCE
}

// API Keys
model ApiKey {
  id          String      @id @default(cuid())
  userId      String
  name        String
  key         String      @unique
  secret      String
  permissions Json        // Array of permissions
  isActive    Boolean     @default(true)
  lastUsedAt  DateTime?
  expiresAt   DateTime?
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt

  // Relations
  user        User        @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("api_keys")
}
