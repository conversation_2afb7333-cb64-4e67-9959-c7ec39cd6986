{"name": "upath", "description": "A proxy to `path`, replacing `\\` with `/` for all results & new methods to normalize & join keeping leading `./` and add, change, default, trim file extensions.", "version": "1.2.0", "homepage": "http://github.com/anodynos/upath/", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "license": "MIT", "keywords": ["path", "unix", "windows", "extension", "file extension", "replace extension", "change extension", "trim extension", "add extension", "default extension"], "repository": {"type": "git", "url": "git://github.com/anodynos/upath"}, "bugs": {"url": "http://github.com/anodynos/upath/issues", "email": "<EMAIL>"}, "main": "./build/code/upath.js", "types": "./upath.d.ts", "preferGlobal": false, "scripts": {"test": "grunt", "build": "grunt lib"}, "directories": {"doc": "./doc", "dist": "./build"}, "engines": {"node": ">=4", "yarn": "*"}, "devDependencies": {"chai": "~4.0.2", "coffee-script": "1.12.6", "grunt": "0.4.5", "grunt-contrib-watch": "^1.1.0", "grunt-urequire": "0.7.x", "lodash": "^4.17.15", "mocha": "~3.4.2", "uberscore": "0.0.19", "underscore.string": "^3.3.5", "urequire": "0.7.0-beta.33", "urequire-ab-specrunner": "^0.2.5", "urequire-rc-inject-version": "^0.1.6"}}