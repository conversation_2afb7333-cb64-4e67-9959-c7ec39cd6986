import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';
import { Decimal } from '@prisma/client/runtime/library';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // Create currencies
  console.log('📦 Creating currencies...');
  
  const currencies = [
    {
      symbol: 'BTC',
      name: 'Bitcoin',
      decimals: 8,
      isFiat: false,
      network: 'Bitcoin',
      minDeposit: new Decimal('0.0001'),
      minWithdrawal: new Decimal('0.001'),
      withdrawalFee: new Decimal('0.0005'),
      confirmations: 6,
      iconUrl: 'https://cryptologos.cc/logos/bitcoin-btc-logo.png'
    },
    {
      symbol: 'ETH',
      name: 'Ethereum',
      decimals: 18,
      isFiat: false,
      network: 'Ethereum',
      minDeposit: new Decimal('0.001'),
      minWithdrawal: new Decimal('0.01'),
      withdrawalFee: new Decimal('0.005'),
      confirmations: 12,
      iconUrl: 'https://cryptologos.cc/logos/ethereum-eth-logo.png'
    },
    {
      symbol: 'USDT',
      name: 'Tether USD',
      decimals: 6,
      isFiat: false,
      network: 'Ethereum',
      contractAddress: '******************************************',
      minDeposit: new Decimal('1'),
      minWithdrawal: new Decimal('10'),
      withdrawalFee: new Decimal('1'),
      confirmations: 12,
      iconUrl: 'https://cryptologos.cc/logos/tether-usdt-logo.png'
    },
    {
      symbol: 'BNB',
      name: 'Binance Coin',
      decimals: 18,
      isFiat: false,
      network: 'BSC',
      minDeposit: new Decimal('0.01'),
      minWithdrawal: new Decimal('0.1'),
      withdrawalFee: new Decimal('0.005'),
      confirmations: 15,
      iconUrl: 'https://cryptologos.cc/logos/bnb-bnb-logo.png'
    },
    {
      symbol: 'ADA',
      name: 'Cardano',
      decimals: 6,
      isFiat: false,
      network: 'Cardano',
      minDeposit: new Decimal('1'),
      minWithdrawal: new Decimal('10'),
      withdrawalFee: new Decimal('1'),
      confirmations: 20,
      iconUrl: 'https://cryptologos.cc/logos/cardano-ada-logo.png'
    },
    {
      symbol: 'SOL',
      name: 'Solana',
      decimals: 9,
      isFiat: false,
      network: 'Solana',
      minDeposit: new Decimal('0.01'),
      minWithdrawal: new Decimal('0.1'),
      withdrawalFee: new Decimal('0.01'),
      confirmations: 32,
      iconUrl: 'https://cryptologos.cc/logos/solana-sol-logo.png'
    },
    {
      symbol: 'DOT',
      name: 'Polkadot',
      decimals: 10,
      isFiat: false,
      network: 'Polkadot',
      minDeposit: new Decimal('0.1'),
      minWithdrawal: new Decimal('1'),
      withdrawalFee: new Decimal('0.1'),
      confirmations: 10,
      iconUrl: 'https://cryptologos.cc/logos/polkadot-new-dot-logo.png'
    },
    {
      symbol: 'LINK',
      name: 'Chainlink',
      decimals: 18,
      isFiat: false,
      network: 'Ethereum',
      contractAddress: '******************************************',
      minDeposit: new Decimal('0.1'),
      minWithdrawal: new Decimal('1'),
      withdrawalFee: new Decimal('0.5'),
      confirmations: 12,
      iconUrl: 'https://cryptologos.cc/logos/chainlink-link-logo.png'
    },
    {
      symbol: 'LTC',
      name: 'Litecoin',
      decimals: 8,
      isFiat: false,
      network: 'Litecoin',
      minDeposit: new Decimal('0.001'),
      minWithdrawal: new Decimal('0.01'),
      withdrawalFee: new Decimal('0.001'),
      confirmations: 6,
      iconUrl: 'https://cryptologos.cc/logos/litecoin-ltc-logo.png'
    },
    {
      symbol: 'BCH',
      name: 'Bitcoin Cash',
      decimals: 8,
      isFiat: false,
      network: 'Bitcoin Cash',
      minDeposit: new Decimal('0.001'),
      minWithdrawal: new Decimal('0.01'),
      withdrawalFee: new Decimal('0.001'),
      confirmations: 6,
      iconUrl: 'https://cryptologos.cc/logos/bitcoin-cash-bch-logo.png'
    }
  ];

  for (const currency of currencies) {
    await prisma.currency.upsert({
      where: { symbol: currency.symbol },
      update: currency,
      create: currency
    });
  }

  console.log(`✅ Created ${currencies.length} currencies`);

  // Create trading pairs
  console.log('📈 Creating trading pairs...');
  
  const tradingPairs = [
    { base: 'BTC', quote: 'USDT', minTrade: '0.00001', maxTrade: '100' },
    { base: 'ETH', quote: 'USDT', minTrade: '0.0001', maxTrade: '1000' },
    { base: 'BNB', quote: 'USDT', minTrade: '0.001', maxTrade: '10000' },
    { base: 'ADA', quote: 'USDT', minTrade: '1', maxTrade: '100000' },
    { base: 'SOL', quote: 'USDT', minTrade: '0.01', maxTrade: '10000' },
    { base: 'DOT', quote: 'USDT', minTrade: '0.1', maxTrade: '10000' },
    { base: 'LINK', quote: 'USDT', minTrade: '0.1', maxTrade: '10000' },
    { base: 'LTC', quote: 'USDT', minTrade: '0.001', maxTrade: '1000' },
    { base: 'BCH', quote: 'USDT', minTrade: '0.001', maxTrade: '1000' },
    { base: 'ETH', quote: 'BTC', minTrade: '0.0001', maxTrade: '100' },
    { base: 'BNB', quote: 'BTC', minTrade: '0.001', maxTrade: '1000' },
    { base: 'ADA', quote: 'BTC', minTrade: '1', maxTrade: '100000' }
  ];

  for (const pair of tradingPairs) {
    const baseCurrency = await prisma.currency.findUnique({
      where: { symbol: pair.base }
    });
    const quoteCurrency = await prisma.currency.findUnique({
      where: { symbol: pair.quote }
    });

    if (baseCurrency && quoteCurrency) {
      const symbol = `${pair.base}${pair.quote}`;
      await prisma.tradingPair.upsert({
        where: { symbol },
        update: {
          minTradeAmount: new Decimal(pair.minTrade),
          maxTradeAmount: new Decimal(pair.maxTrade)
        },
        create: {
          symbol,
          baseCurrencyId: baseCurrency.id,
          quoteCurrencyId: quoteCurrency.id,
          minTradeAmount: new Decimal(pair.minTrade),
          maxTradeAmount: new Decimal(pair.maxTrade),
          makerFee: new Decimal('0.001'), // 0.1%
          takerFee: new Decimal('0.001'), // 0.1%
          priceDecimals: 8,
          amountDecimals: 8
        }
      });
    }
  }

  console.log(`✅ Created ${tradingPairs.length} trading pairs`);

  // Create admin user
  console.log('👤 Creating admin user...');
  
  const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
  const adminPassword = process.env.ADMIN_PASSWORD || 'Admin123!@#';
  const hashedPassword = await bcrypt.hash(adminPassword, 12);

  const adminUser = await prisma.user.upsert({
    where: { email: adminEmail },
    update: {},
    create: {
      email: adminEmail,
      password: hashedPassword,
      firstName: 'Admin',
      lastName: 'User',
      role: 'SUPER_ADMIN',
      status: 'ACTIVE',
      isEmailVerified: true,
      kycStatus: 'APPROVED',
      kycLevel: 2,
      referralCode: 'ADMIN001'
    }
  });

  console.log(`✅ Created admin user: ${adminEmail}`);

  // Create demo user
  console.log('👤 Creating demo user...');
  
  const demoEmail = '<EMAIL>';
  const demoPassword = 'Demo123!@#';
  const hashedDemoPassword = await bcrypt.hash(demoPassword, 12);

  const demoUser = await prisma.user.upsert({
    where: { email: demoEmail },
    update: {},
    create: {
      email: demoEmail,
      password: hashedDemoPassword,
      firstName: 'Demo',
      lastName: 'User',
      role: 'USER',
      status: 'ACTIVE',
      isEmailVerified: true,
      kycStatus: 'APPROVED',
      kycLevel: 1,
      referralCode: 'DEMO001'
    }
  });

  console.log(`✅ Created demo user: ${demoEmail}`);

  // Create wallets for demo user
  console.log('💰 Creating demo wallets...');
  
  const demoBalances = [
    { symbol: 'BTC', balance: '1.5' },
    { symbol: 'ETH', balance: '10.0' },
    { symbol: 'USDT', balance: '50000.0' },
    { symbol: 'BNB', balance: '100.0' },
    { symbol: 'ADA', balance: '10000.0' },
    { symbol: 'SOL', balance: '500.0' },
    { symbol: 'DOT', balance: '1000.0' },
    { symbol: 'LINK', balance: '500.0' },
    { symbol: 'LTC', balance: '20.0' },
    { symbol: 'BCH', balance: '15.0' }
  ];

  for (const balance of demoBalances) {
    const currency = await prisma.currency.findUnique({
      where: { symbol: balance.symbol }
    });

    if (currency) {
      await prisma.wallet.upsert({
        where: {
          userId_currencyId: {
            userId: demoUser.id,
            currencyId: currency.id
          }
        },
        update: {
          balance: new Decimal(balance.balance)
        },
        create: {
          userId: demoUser.id,
          currencyId: currency.id,
          balance: new Decimal(balance.balance),
          lockedBalance: new Decimal('0')
        }
      });
    }
  }

  console.log(`✅ Created wallets for demo user`);

  // Create some sample price history
  console.log('📊 Creating sample price history...');
  
  const now = new Date();
  const intervals = ['1m', '5m', '15m', '1h', '4h', '1d'];
  const samplePairs = ['BTCUSDT', 'ETHUSDT', 'BNBUSDT'];

  for (const pairSymbol of samplePairs) {
    const tradingPair = await prisma.tradingPair.findUnique({
      where: { symbol: pairSymbol }
    });

    if (tradingPair) {
      for (const interval of intervals) {
        // Create 100 sample candles for each interval
        for (let i = 99; i >= 0; i--) {
          let timeOffset: number;
          switch (interval) {
            case '1m': timeOffset = i * 60 * 1000; break;
            case '5m': timeOffset = i * 5 * 60 * 1000; break;
            case '15m': timeOffset = i * 15 * 60 * 1000; break;
            case '1h': timeOffset = i * 60 * 60 * 1000; break;
            case '4h': timeOffset = i * 4 * 60 * 60 * 1000; break;
            case '1d': timeOffset = i * 24 * 60 * 60 * 1000; break;
            default: timeOffset = i * 60 * 1000;
          }

          const timestamp = new Date(now.getTime() - timeOffset);
          
          // Generate realistic OHLCV data
          const basePrice = pairSymbol === 'BTCUSDT' ? 45000 : 
                           pairSymbol === 'ETHUSDT' ? 3000 : 300;
          
          const volatility = 0.02; // 2% volatility
          const change = (Math.random() - 0.5) * 2 * volatility;
          const open = basePrice * (1 + change);
          const close = open * (1 + (Math.random() - 0.5) * 2 * volatility);
          const high = Math.max(open, close) * (1 + Math.random() * volatility);
          const low = Math.min(open, close) * (1 - Math.random() * volatility);
          const volume = Math.random() * 1000;

          await prisma.priceHistory.upsert({
            where: {
              tradingPairId_timestamp_interval: {
                tradingPairId: tradingPair.id,
                timestamp,
                interval
              }
            },
            update: {},
            create: {
              tradingPairId: tradingPair.id,
              timestamp,
              interval,
              open: new Decimal(open.toFixed(8)),
              high: new Decimal(high.toFixed(8)),
              low: new Decimal(low.toFixed(8)),
              close: new Decimal(close.toFixed(8)),
              volume: new Decimal(volume.toFixed(8))
            }
          });
        }
      }
    }
  }

  console.log(`✅ Created sample price history`);

  console.log('🎉 Database seeding completed successfully!');
  console.log('');
  console.log('📋 Summary:');
  console.log(`   • ${currencies.length} currencies created`);
  console.log(`   • ${tradingPairs.length} trading pairs created`);
  console.log(`   • Admin user: ${adminEmail} (password: ${adminPassword})`);
  console.log(`   • Demo user: ${demoEmail} (password: ${demoPassword})`);
  console.log('');
  console.log('🚀 You can now start the application!');
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
