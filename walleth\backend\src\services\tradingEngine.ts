import { PrismaClient, Order, OrderType, OrderSide, OrderStatus, TimeInForce } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';
import { logger } from '../utils/logger';
import { WebSocketService } from './websocket';

const prisma = new PrismaClient();

interface OrderBookEntry {
  price: Decimal;
  amount: Decimal;
  orderId: string;
  userId: string;
  timestamp: Date;
}

interface OrderBook {
  bids: OrderBookEntry[];
  asks: OrderBookEntry[];
}

interface TradeResult {
  buyOrderId: string;
  sellOrderId: string;
  amount: Decimal;
  price: Decimal;
  buyUserId: string;
  sellUserId: string;
  buyerFee: Decimal;
  sellerFee: Decimal;
}

export class TradingEngine {
  private orderBooks: Map<string, OrderBook> = new Map();
  private wsService?: WebSocketService;

  constructor(wsService?: WebSocketService) {
    this.wsService = wsService;
    this.initializeOrderBooks();
  }

  private async initializeOrderBooks(): Promise<void> {
    try {
      // Get all active trading pairs
      const tradingPairs = await prisma.tradingPair.findMany({
        where: { isActive: true }
      });

      // Initialize order books for each trading pair
      for (const pair of tradingPairs) {
        await this.loadOrderBook(pair.symbol);
      }

      logger.info('Trading engine initialized', { 
        tradingPairs: tradingPairs.length 
      });
    } catch (error) {
      logger.error('Failed to initialize trading engine:', error);
    }
  }

  private async loadOrderBook(tradingPairSymbol: string): Promise<void> {
    try {
      const orders = await prisma.order.findMany({
        where: {
          tradingPair: { symbol: tradingPairSymbol },
          status: 'PENDING',
          type: 'LIMIT'
        },
        orderBy: [
          { price: 'desc' }, // For bids (buy orders)
          { createdAt: 'asc' }
        ]
      });

      const bids: OrderBookEntry[] = [];
      const asks: OrderBookEntry[] = [];

      orders.forEach(order => {
        const entry: OrderBookEntry = {
          price: order.price!,
          amount: new Decimal(order.amount).minus(order.filledAmount),
          orderId: order.id,
          userId: order.userId,
          timestamp: order.createdAt
        };

        if (order.side === 'BUY') {
          bids.push(entry);
        } else {
          asks.push(entry);
        }
      });

      // Sort bids (highest price first) and asks (lowest price first)
      bids.sort((a, b) => b.price.comparedTo(a.price));
      asks.sort((a, b) => a.price.comparedTo(b.price));

      this.orderBooks.set(tradingPairSymbol, { bids, asks });
      
      logger.debug('Order book loaded', {
        tradingPair: tradingPairSymbol,
        bids: bids.length,
        asks: asks.length
      });
    } catch (error) {
      logger.error('Failed to load order book:', error);
    }
  }

  public async placeOrder(orderData: {
    userId: string;
    tradingPairId: string;
    tradingPairSymbol: string;
    type: OrderType;
    side: OrderSide;
    amount: Decimal;
    price?: Decimal;
    stopPrice?: Decimal;
    timeInForce?: TimeInForce;
  }): Promise<Order> {
    try {
      // Validate order
      await this.validateOrder(orderData);

      // Create order in database
      const order = await prisma.order.create({
        data: {
          userId: orderData.userId,
          tradingPairId: orderData.tradingPairId,
          type: orderData.type,
          side: orderData.side,
          amount: orderData.amount,
          price: orderData.price,
          stopPrice: orderData.stopPrice,
          timeInForce: orderData.timeInForce || 'GTC',
          status: 'PENDING'
        }
      });

      // Process order based on type
      if (orderData.type === 'MARKET') {
        await this.processMarketOrder(order, orderData.tradingPairSymbol);
      } else if (orderData.type === 'LIMIT') {
        await this.processLimitOrder(order, orderData.tradingPairSymbol);
      }

      // Notify user about order placement
      if (this.wsService) {
        this.wsService.notifyUserOrder(orderData.userId, order);
      }

      logger.info('Order placed', {
        orderId: order.id,
        userId: orderData.userId,
        type: orderData.type,
        side: orderData.side,
        amount: orderData.amount.toString(),
        price: orderData.price?.toString()
      });

      return order;
    } catch (error) {
      logger.error('Failed to place order:', error);
      throw error;
    }
  }

  private async validateOrder(orderData: any): Promise<void> {
    // Get trading pair details
    const tradingPair = await prisma.tradingPair.findUnique({
      where: { id: orderData.tradingPairId },
      include: {
        baseCurrency: true,
        quoteCurrency: true
      }
    });

    if (!tradingPair || !tradingPair.isActive) {
      throw new Error('Trading pair not found or inactive');
    }

    // Validate minimum trade amount
    if (orderData.amount.lessThan(tradingPair.minTradeAmount)) {
      throw new Error(`Minimum trade amount is ${tradingPair.minTradeAmount}`);
    }

    // Validate maximum trade amount
    if (tradingPair.maxTradeAmount.greaterThan(0) && 
        orderData.amount.greaterThan(tradingPair.maxTradeAmount)) {
      throw new Error(`Maximum trade amount is ${tradingPair.maxTradeAmount}`);
    }

    // Check user balance
    await this.validateUserBalance(orderData, tradingPair);
  }

  private async validateUserBalance(orderData: any, tradingPair: any): Promise<void> {
    let requiredCurrencyId: string;
    let requiredAmount: Decimal;

    if (orderData.side === 'BUY') {
      // For buy orders, need quote currency
      requiredCurrencyId = tradingPair.quoteCurrencyId;
      requiredAmount = orderData.amount.mul(orderData.price || new Decimal(0));
    } else {
      // For sell orders, need base currency
      requiredCurrencyId = tradingPair.baseCurrencyId;
      requiredAmount = orderData.amount;
    }

    const wallet = await prisma.wallet.findUnique({
      where: {
        userId_currencyId: {
          userId: orderData.userId,
          currencyId: requiredCurrencyId
        }
      }
    });

    if (!wallet) {
      throw new Error('Wallet not found');
    }

    const availableBalance = new Decimal(wallet.balance).minus(wallet.lockedBalance);
    if (availableBalance.lessThan(requiredAmount)) {
      throw new Error('Insufficient balance');
    }
  }

  private async processMarketOrder(order: Order, tradingPairSymbol: string): Promise<void> {
    const orderBook = this.orderBooks.get(tradingPairSymbol);
    if (!orderBook) {
      throw new Error('Order book not found');
    }

    let remainingAmount = new Decimal(order.amount);
    const trades: TradeResult[] = [];

    // Get opposite side orders
    const oppositeOrders = order.side === 'BUY' ? orderBook.asks : orderBook.bids;

    for (const bookEntry of oppositeOrders) {
      if (remainingAmount.lessThanOrEqualTo(0)) break;

      const tradeAmount = Decimal.min(remainingAmount, bookEntry.amount);
      const tradePrice = bookEntry.price;

      // Create trade
      const trade = await this.executeTrade({
        buyOrderId: order.side === 'BUY' ? order.id : bookEntry.orderId,
        sellOrderId: order.side === 'SELL' ? order.id : bookEntry.orderId,
        amount: tradeAmount,
        price: tradePrice,
        buyUserId: order.side === 'BUY' ? order.userId : bookEntry.userId,
        sellUserId: order.side === 'SELL' ? order.userId : bookEntry.userId,
        tradingPairSymbol
      });

      trades.push(trade);
      remainingAmount = remainingAmount.minus(tradeAmount);

      // Update order book entry
      bookEntry.amount = bookEntry.amount.minus(tradeAmount);
      if (bookEntry.amount.lessThanOrEqualTo(0)) {
        // Remove filled order from order book
        const index = oppositeOrders.indexOf(bookEntry);
        oppositeOrders.splice(index, 1);
      }
    }

    // Update order status
    const filledAmount = new Decimal(order.amount).minus(remainingAmount);
    const status = remainingAmount.lessThanOrEqualTo(0) ? 'FILLED' : 'PARTIAL_FILLED';

    await prisma.order.update({
      where: { id: order.id },
      data: {
        filledAmount,
        status
      }
    });

    // Broadcast order book update
    if (this.wsService) {
      this.wsService.broadcastOrderbookUpdate(tradingPairSymbol, this.getOrderBookSnapshot(tradingPairSymbol));
    }
  }

  private async processLimitOrder(order: Order, tradingPairSymbol: string): Promise<void> {
    const orderBook = this.orderBooks.get(tradingPairSymbol);
    if (!orderBook) {
      throw new Error('Order book not found');
    }

    let remainingAmount = new Decimal(order.amount);
    const trades: TradeResult[] = [];

    // Check if order can be matched immediately
    const oppositeOrders = order.side === 'BUY' ? orderBook.asks : orderBook.bids;
    
    for (const bookEntry of oppositeOrders) {
      if (remainingAmount.lessThanOrEqualTo(0)) break;

      // Check if prices match
      const canMatch = order.side === 'BUY' 
        ? order.price!.greaterThanOrEqualTo(bookEntry.price)
        : order.price!.lessThanOrEqualTo(bookEntry.price);

      if (!canMatch) break;

      const tradeAmount = Decimal.min(remainingAmount, bookEntry.amount);
      const tradePrice = bookEntry.price; // Use maker's price

      // Create trade
      const trade = await this.executeTrade({
        buyOrderId: order.side === 'BUY' ? order.id : bookEntry.orderId,
        sellOrderId: order.side === 'SELL' ? order.id : bookEntry.orderId,
        amount: tradeAmount,
        price: tradePrice,
        buyUserId: order.side === 'BUY' ? order.userId : bookEntry.userId,
        sellUserId: order.side === 'SELL' ? order.userId : bookEntry.userId,
        tradingPairSymbol
      });

      trades.push(trade);
      remainingAmount = remainingAmount.minus(tradeAmount);

      // Update order book entry
      bookEntry.amount = bookEntry.amount.minus(tradeAmount);
      if (bookEntry.amount.lessThanOrEqualTo(0)) {
        const index = oppositeOrders.indexOf(bookEntry);
        oppositeOrders.splice(index, 1);
      }
    }

    // Update order status and add to order book if not fully filled
    const filledAmount = new Decimal(order.amount).minus(remainingAmount);
    let status: OrderStatus = 'PENDING';
    
    if (filledAmount.greaterThan(0)) {
      status = remainingAmount.lessThanOrEqualTo(0) ? 'FILLED' : 'PARTIAL_FILLED';
    }

    await prisma.order.update({
      where: { id: order.id },
      data: {
        filledAmount,
        status
      }
    });

    // Add remaining order to order book
    if (remainingAmount.greaterThan(0)) {
      const bookEntry: OrderBookEntry = {
        price: order.price!,
        amount: remainingAmount,
        orderId: order.id,
        userId: order.userId,
        timestamp: order.createdAt
      };

      if (order.side === 'BUY') {
        orderBook.bids.push(bookEntry);
        orderBook.bids.sort((a, b) => b.price.comparedTo(a.price));
      } else {
        orderBook.asks.push(bookEntry);
        orderBook.asks.sort((a, b) => a.price.comparedTo(b.price));
      }
    }

    // Broadcast order book update
    if (this.wsService) {
      this.wsService.broadcastOrderbookUpdate(tradingPairSymbol, this.getOrderBookSnapshot(tradingPairSymbol));
    }
  }

  private async executeTrade(tradeData: {
    buyOrderId: string;
    sellOrderId: string;
    amount: Decimal;
    price: Decimal;
    buyUserId: string;
    sellUserId: string;
    tradingPairSymbol: string;
  }): Promise<TradeResult> {
    // Get trading pair for fee calculation
    const tradingPair = await prisma.tradingPair.findUnique({
      where: { symbol: tradeData.tradingPairSymbol }
    });

    if (!tradingPair) {
      throw new Error('Trading pair not found');
    }

    // Calculate fees
    const buyerFee = tradeData.amount.mul(tradeData.price).mul(tradingPair.takerFee);
    const sellerFee = tradeData.amount.mul(tradingPair.makerFee);

    // Create trade record
    const trade = await prisma.trade.create({
      data: {
        buyOrderId: tradeData.buyOrderId,
        sellOrderId: tradeData.sellOrderId,
        tradingPairId: tradingPair.id,
        buyUserId: tradeData.buyUserId,
        sellUserId: tradeData.sellUserId,
        amount: tradeData.amount,
        price: tradeData.price,
        buyerFee,
        sellerFee
      }
    });

    // Update user balances
    await this.updateUserBalances(tradeData, tradingPair, buyerFee, sellerFee);

    // Broadcast trade update
    if (this.wsService) {
      this.wsService.broadcastTradeUpdate(tradeData.tradingPairSymbol, trade);
      this.wsService.notifyUserTrade(tradeData.buyUserId, trade);
      this.wsService.notifyUserTrade(tradeData.sellUserId, trade);
    }

    logger.info('Trade executed', {
      tradeId: trade.id,
      tradingPair: tradeData.tradingPairSymbol,
      amount: tradeData.amount.toString(),
      price: tradeData.price.toString(),
      buyUserId: tradeData.buyUserId,
      sellUserId: tradeData.sellUserId
    });

    return {
      buyOrderId: tradeData.buyOrderId,
      sellOrderId: tradeData.sellOrderId,
      amount: tradeData.amount,
      price: tradeData.price,
      buyUserId: tradeData.buyUserId,
      sellUserId: tradeData.sellUserId,
      buyerFee,
      sellerFee
    };
  }

  private async updateUserBalances(
    tradeData: any,
    tradingPair: any,
    buyerFee: Decimal,
    sellerFee: Decimal
  ): Promise<void> {
    // Update buyer's balances
    await this.updateWalletBalance(
      tradeData.buyUserId,
      tradingPair.baseCurrencyId,
      tradeData.amount.minus(sellerFee), // Receive base currency minus fee
      'add'
    );

    await this.updateWalletBalance(
      tradeData.buyUserId,
      tradingPair.quoteCurrencyId,
      tradeData.amount.mul(tradeData.price).plus(buyerFee), // Pay quote currency plus fee
      'subtract'
    );

    // Update seller's balances
    await this.updateWalletBalance(
      tradeData.sellUserId,
      tradingPair.baseCurrencyId,
      tradeData.amount, // Pay base currency
      'subtract'
    );

    await this.updateWalletBalance(
      tradeData.sellUserId,
      tradingPair.quoteCurrencyId,
      tradeData.amount.mul(tradeData.price).minus(buyerFee), // Receive quote currency minus fee
      'add'
    );
  }

  private async updateWalletBalance(
    userId: string,
    currencyId: string,
    amount: Decimal,
    operation: 'add' | 'subtract'
  ): Promise<void> {
    const wallet = await prisma.wallet.findUnique({
      where: {
        userId_currencyId: {
          userId,
          currencyId
        }
      }
    });

    if (!wallet) {
      throw new Error('Wallet not found');
    }

    const newBalance = operation === 'add' 
      ? new Decimal(wallet.balance).plus(amount)
      : new Decimal(wallet.balance).minus(amount);

    await prisma.wallet.update({
      where: { id: wallet.id },
      data: { balance: newBalance }
    });

    // Notify user about wallet update
    if (this.wsService) {
      this.wsService.notifyUserWallet(userId, {
        currencyId,
        balance: newBalance,
        operation,
        amount
      });
    }
  }

  public getOrderBookSnapshot(tradingPairSymbol: string): any {
    const orderBook = this.orderBooks.get(tradingPairSymbol);
    if (!orderBook) return null;

    return {
      bids: orderBook.bids.slice(0, 20).map(entry => ({
        price: entry.price.toString(),
        amount: entry.amount.toString()
      })),
      asks: orderBook.asks.slice(0, 20).map(entry => ({
        price: entry.price.toString(),
        amount: entry.amount.toString()
      }))
    };
  }

  public async cancelOrder(orderId: string, userId: string): Promise<void> {
    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: { tradingPair: true }
    });

    if (!order) {
      throw new Error('Order not found');
    }

    if (order.userId !== userId) {
      throw new Error('Unauthorized');
    }

    if (order.status !== 'PENDING' && order.status !== 'PARTIAL_FILLED') {
      throw new Error('Order cannot be cancelled');
    }

    // Update order status
    await prisma.order.update({
      where: { id: orderId },
      data: { status: 'CANCELLED' }
    });

    // Remove from order book
    const orderBook = this.orderBooks.get(order.tradingPair.symbol);
    if (orderBook) {
      const orders = order.side === 'BUY' ? orderBook.bids : orderBook.asks;
      const index = orders.findIndex(entry => entry.orderId === orderId);
      if (index !== -1) {
        orders.splice(index, 1);
      }
    }

    // Broadcast order book update
    if (this.wsService) {
      this.wsService.broadcastOrderbookUpdate(order.tradingPair.symbol, this.getOrderBookSnapshot(order.tradingPair.symbol));
      this.wsService.notifyUserOrder(userId, order);
    }

    logger.info('Order cancelled', { orderId, userId });
  }
}
