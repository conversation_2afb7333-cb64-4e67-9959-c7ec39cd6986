import { ValidationOptions } from '../ValidationOptions';
export declare const IS_EMPTY = "isEmpty";
/**
 * Checks if given value is empty (=== '', === null, === undefined).
 */
export declare function isEmpty(value: unknown): boolean;
/**
 * Checks if given value is empty (=== '', === null, === undefined).
 */
export declare function IsEmpty(validationOptions?: ValidationOptions): PropertyDecorator;
