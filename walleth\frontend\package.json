{"name": "cryptonest-frontend", "version": "1.0.0", "description": "CryptoNest - Your Secure Cryptocurrency Trading Haven Frontend", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,css,md}\"", "type-check": "tsc --noEmit"}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.1", "@mui/material": "^5.15.1", "@mui/x-charts": "^6.18.1", "@mui/x-data-grid": "^6.18.1", "@mui/x-date-pickers": "^6.18.1", "@reduxjs/toolkit": "^2.0.1", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "axios": "^1.6.2", "date-fns": "^3.0.6", "lightweight-charts": "^4.1.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-redux": "^9.0.4", "react-router-dom": "^6.20.1", "recharts": "^2.8.0", "socket.io-client": "^4.7.4", "styled-components": "^6.1.6"}, "devDependencies": {"@types/node": "^20.10.5", "@types/styled-components": "^5.1.34", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "prettier": "^3.1.1", "typescript": "^5.2.2", "vite": "^5.0.8"}, "engines": {"node": ">=18.0.0"}}