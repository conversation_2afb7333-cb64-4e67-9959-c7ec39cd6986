import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  Box,
  Grid,
  Paper,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Tabs,
  Tab,
  Card,
  CardContent,
  Avatar,
  Tooltip,
} from '@mui/material';
import {
  AccountBalanceWallet as WalletIcon,
  Add as DepositIcon,
  Remove as WithdrawIcon,
  History as HistoryIcon,
  ContentCopy as CopyIcon,
  Refresh as RefreshIcon,
  QrCode as QrCodeIcon,
} from '@mui/icons-material';

// Store
import { RootState } from '@/types';
import { fetchWallets, generateDepositAddress, requestWithdrawal, fetchTransactions } from '@/store/slices/walletSlice';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`wallet-tabpanel-${index}`}
      aria-labelledby={`wallet-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const WalletPage: React.FC = () => {
  const dispatch = useDispatch();
  const { wallets, transactions, isLoading } = useSelector((state: RootState) => state.wallet);
  
  const [tabValue, setTabValue] = useState(0);
  const [depositDialog, setDepositDialog] = useState(false);
  const [withdrawDialog, setWithdrawDialog] = useState(false);
  const [selectedCurrency, setSelectedCurrency] = useState<any>(null);
  const [withdrawAmount, setWithdrawAmount] = useState('');
  const [withdrawAddress, setWithdrawAddress] = useState('');

  useEffect(() => {
    dispatch(fetchWallets());
    dispatch(fetchTransactions());
  }, [dispatch]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleDepositClick = (currency: any) => {
    setSelectedCurrency(currency);
    setDepositDialog(true);
  };

  const handleWithdrawClick = (currency: any) => {
    setSelectedCurrency(currency);
    setWithdrawDialog(true);
  };

  const handleGenerateAddress = async () => {
    if (selectedCurrency) {
      await dispatch(generateDepositAddress(selectedCurrency.symbol));
    }
  };

  const handleWithdraw = async () => {
    if (selectedCurrency && withdrawAmount && withdrawAddress) {
      await dispatch(requestWithdrawal({
        currencySymbol: selectedCurrency.symbol,
        amount: withdrawAmount,
        address: withdrawAddress,
      }));
      setWithdrawDialog(false);
      setWithdrawAmount('');
      setWithdrawAddress('');
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const formatBalance = (balance: string) => {
    const num = parseFloat(balance);
    return num.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 8,
    });
  };

  const getTotalBalance = () => {
    return wallets.reduce((total, wallet) => {
      // This would need real price conversion in a real app
      return total + parseFloat(wallet.balance);
    }, 0);
  };

  const getTransactionStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'success';
      case 'PENDING':
      case 'CONFIRMING':
        return 'warning';
      case 'FAILED':
      case 'REJECTED':
        return 'error';
      default:
        return 'default';
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" gutterBottom>
          Wallet
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Manage your cryptocurrency balances and transactions
        </Typography>
      </Box>

      {/* Portfolio Overview */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar sx={{ bgcolor: 'primary.main', mr: 2 }}>
                  <WalletIcon />
                </Avatar>
                <Typography variant="h6">Total Portfolio</Typography>
              </Box>
              <Typography variant="h4" color="primary.main">
                ${getTotalBalance().toLocaleString()}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Estimated value in USD
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Active Assets
              </Typography>
              <Typography variant="h4" color="success.main">
                {wallets.filter(w => parseFloat(w.balance) > 0).length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Currencies with balance
              </Typography>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recent Transactions
              </Typography>
              <Typography variant="h4" color="info.main">
                {transactions.length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total transactions
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs value={tabValue} onChange={handleTabChange}>
          <Tab label="Balances" />
          <Tab label="Transaction History" />
        </Tabs>
      </Paper>

      {/* Balances Tab */}
      <TabPanel value={tabValue} index={0}>
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Asset</TableCell>
                <TableCell align="right">Total Balance</TableCell>
                <TableCell align="right">Available</TableCell>
                <TableCell align="right">Locked</TableCell>
                <TableCell align="center">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {wallets.map((wallet) => (
                <TableRow key={wallet.currency.symbol}>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      {wallet.currency.iconUrl && (
                        <Avatar
                          src={wallet.currency.iconUrl}
                          sx={{ width: 32, height: 32, mr: 2 }}
                        />
                      )}
                      <Box>
                        <Typography variant="body1" fontWeight={600}>
                          {wallet.currency.symbol}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {wallet.currency.name}
                        </Typography>
                      </Box>
                    </Box>
                  </TableCell>
                  <TableCell align="right">
                    <Typography variant="body1" fontWeight={600}>
                      {formatBalance(wallet.balance)}
                    </Typography>
                  </TableCell>
                  <TableCell align="right">
                    <Typography variant="body2" color="success.main">
                      {formatBalance(wallet.availableBalance)}
                    </Typography>
                  </TableCell>
                  <TableCell align="right">
                    <Typography variant="body2" color="warning.main">
                      {formatBalance(wallet.lockedBalance)}
                    </Typography>
                  </TableCell>
                  <TableCell align="center">
                    <Box sx={{ display: 'flex', gap: 1, justifyContent: 'center' }}>
                      <Tooltip title="Deposit">
                        <IconButton
                          size="small"
                          color="primary"
                          onClick={() => handleDepositClick(wallet.currency)}
                        >
                          <DepositIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Withdraw">
                        <IconButton
                          size="small"
                          color="secondary"
                          onClick={() => handleWithdrawClick(wallet.currency)}
                          disabled={parseFloat(wallet.availableBalance) <= 0}
                        >
                          <WithdrawIcon />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </TabPanel>

      {/* Transaction History Tab */}
      <TabPanel value={tabValue} index={1}>
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Type</TableCell>
                <TableCell>Asset</TableCell>
                <TableCell align="right">Amount</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Date</TableCell>
                <TableCell>TX Hash</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {transactions.map((transaction) => (
                <TableRow key={transaction.id}>
                  <TableCell>
                    <Chip
                      label={transaction.type}
                      color={transaction.type === 'DEPOSIT' ? 'success' : 'warning'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      {transaction.currency.iconUrl && (
                        <Avatar
                          src={transaction.currency.iconUrl}
                          sx={{ width: 24, height: 24, mr: 1 }}
                        />
                      )}
                      {transaction.currency.symbol}
                    </Box>
                  </TableCell>
                  <TableCell align="right">
                    {formatBalance(transaction.amount)}
                  </TableCell>
                  <TableCell>
                    <Chip
                      label={transaction.status}
                      color={getTransactionStatusColor(transaction.status)}
                      size="small"
                      variant="outlined"
                    />
                  </TableCell>
                  <TableCell>
                    {new Date(transaction.createdAt).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    {transaction.txHash ? (
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Typography variant="caption" sx={{ mr: 1 }}>
                          {transaction.txHash.substring(0, 8)}...
                        </Typography>
                        <IconButton
                          size="small"
                          onClick={() => copyToClipboard(transaction.txHash!)}
                        >
                          <CopyIcon fontSize="small" />
                        </IconButton>
                      </Box>
                    ) : (
                      '-'
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      </TabPanel>

      {/* Deposit Dialog */}
      <Dialog open={depositDialog} onClose={() => setDepositDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          Deposit {selectedCurrency?.symbol}
        </DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Send {selectedCurrency?.symbol} to the address below. 
            Minimum deposit: {selectedCurrency?.minDeposit} {selectedCurrency?.symbol}
          </Typography>
          
          {selectedCurrency?.address ? (
            <Box>
              <Typography variant="subtitle2" gutterBottom>
                Deposit Address:
              </Typography>
              <Box
                sx={{
                  p: 2,
                  backgroundColor: 'action.hover',
                  borderRadius: 1,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between',
                  mb: 2,
                }}
              >
                <Typography variant="body2" sx={{ wordBreak: 'break-all' }}>
                  {selectedCurrency.address}
                </Typography>
                <IconButton onClick={() => copyToClipboard(selectedCurrency.address)}>
                  <CopyIcon />
                </IconButton>
              </Box>
              
              <Box sx={{ textAlign: 'center' }}>
                <Button
                  startIcon={<QrCodeIcon />}
                  variant="outlined"
                  size="small"
                >
                  Show QR Code
                </Button>
              </Box>
            </Box>
          ) : (
            <Box sx={{ textAlign: 'center' }}>
              <Button
                variant="contained"
                onClick={handleGenerateAddress}
                startIcon={<RefreshIcon />}
              >
                Generate Deposit Address
              </Button>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDepositDialog(false)}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* Withdraw Dialog */}
      <Dialog open={withdrawDialog} onClose={() => setWithdrawDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          Withdraw {selectedCurrency?.symbol}
        </DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Available balance: {selectedCurrency && wallets.find(w => w.currency.symbol === selectedCurrency.symbol)?.availableBalance} {selectedCurrency?.symbol}
          </Typography>
          
          <TextField
            fullWidth
            label="Withdrawal Address"
            value={withdrawAddress}
            onChange={(e) => setWithdrawAddress(e.target.value)}
            sx={{ mb: 2 }}
          />
          
          <TextField
            fullWidth
            label="Amount"
            type="number"
            value={withdrawAmount}
            onChange={(e) => setWithdrawAmount(e.target.value)}
            helperText={`Minimum: ${selectedCurrency?.minWithdrawal} ${selectedCurrency?.symbol}, Fee: ${selectedCurrency?.withdrawalFee} ${selectedCurrency?.symbol}`}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setWithdrawDialog(false)}>Cancel</Button>
          <Button
            onClick={handleWithdraw}
            variant="contained"
            disabled={!withdrawAmount || !withdrawAddress}
          >
            Withdraw
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default WalletPage;
