import { Request, Response, NextFunction } from 'express';
import { Prisma } from '@prisma/client';
import { logger } from '../utils/logger';

export interface AppError extends Error {
  statusCode?: number;
  isOperational?: boolean;
}

export class CustomError extends Error implements AppError {
  statusCode: number;
  isOperational: boolean;

  constructor(message: string, statusCode: number = 500, isOperational: boolean = true) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    
    Error.captureStackTrace(this, this.constructor);
  }
}

// Specific error classes
export class ValidationError extends CustomError {
  constructor(message: string) {
    super(message, 400);
  }
}

export class AuthenticationError extends CustomError {
  constructor(message: string = 'Authentication failed') {
    super(message, 401);
  }
}

export class AuthorizationError extends CustomError {
  constructor(message: string = 'Access denied') {
    super(message, 403);
  }
}

export class NotFoundError extends CustomError {
  constructor(message: string = 'Resource not found') {
    super(message, 404);
  }
}

export class ConflictError extends CustomError {
  constructor(message: string = 'Resource conflict') {
    super(message, 409);
  }
}

export class TooManyRequestsError extends CustomError {
  constructor(message: string = 'Too many requests') {
    super(message, 429);
  }
}

export class InternalServerError extends CustomError {
  constructor(message: string = 'Internal server error') {
    super(message, 500);
  }
}

// Handle Prisma errors
const handlePrismaError = (error: Prisma.PrismaClientKnownRequestError): CustomError => {
  switch (error.code) {
    case 'P2002':
      // Unique constraint violation
      const field = error.meta?.target as string[] | undefined;
      const fieldName = field ? field[0] : 'field';
      return new ConflictError(`${fieldName} already exists`);
    
    case 'P2025':
      // Record not found
      return new NotFoundError('Record not found');
    
    case 'P2003':
      // Foreign key constraint violation
      return new ValidationError('Invalid reference to related record');
    
    case 'P2014':
      // Required relation violation
      return new ValidationError('Required relation is missing');
    
    case 'P2021':
      // Table does not exist
      return new InternalServerError('Database table does not exist');
    
    case 'P2022':
      // Column does not exist
      return new InternalServerError('Database column does not exist');
    
    default:
      logger.error('Unhandled Prisma error:', error);
      return new InternalServerError('Database operation failed');
  }
};

// Handle validation errors
const handleValidationError = (error: any): CustomError => {
  if (error.errors && Array.isArray(error.errors)) {
    const messages = error.errors.map((err: any) => err.message).join(', ');
    return new ValidationError(messages);
  }
  return new ValidationError(error.message || 'Validation failed');
};

// Main error handler middleware
export const errorHandler = (
  error: Error | AppError,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  let customError: CustomError;

  // Handle different types of errors
  if (error instanceof CustomError) {
    customError = error;
  } else if (error instanceof Prisma.PrismaClientKnownRequestError) {
    customError = handlePrismaError(error);
  } else if (error instanceof Prisma.PrismaClientValidationError) {
    customError = new ValidationError('Invalid data provided');
  } else if (error.name === 'ValidationError') {
    customError = handleValidationError(error);
  } else if (error.name === 'JsonWebTokenError') {
    customError = new AuthenticationError('Invalid token');
  } else if (error.name === 'TokenExpiredError') {
    customError = new AuthenticationError('Token expired');
  } else if (error.name === 'CastError') {
    customError = new ValidationError('Invalid ID format');
  } else {
    // Unknown error
    customError = new InternalServerError(
      process.env.NODE_ENV === 'production' 
        ? 'Something went wrong' 
        : error.message
    );
  }

  // Log error
  logger.error('Error occurred:', {
    message: customError.message,
    statusCode: customError.statusCode,
    stack: customError.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id,
    timestamp: new Date().toISOString()
  });

  // Send error response
  const response: any = {
    success: false,
    message: customError.message,
    statusCode: customError.statusCode
  };

  // Include stack trace in development
  if (process.env.NODE_ENV === 'development') {
    response.stack = customError.stack;
  }

  // Include error details for validation errors
  if (customError instanceof ValidationError && error.name === 'ValidationError') {
    response.errors = (error as any).errors;
  }

  res.status(customError.statusCode).json(response);
};

// Async error wrapper
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

// 404 handler
export const notFoundHandler = (req: Request, res: Response, next: NextFunction): void => {
  const error = new NotFoundError(`Route ${req.originalUrl} not found`);
  next(error);
};
