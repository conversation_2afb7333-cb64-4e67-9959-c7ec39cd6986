import React from 'react';
import { Box, CircularProgress, Typography, Fade } from '@mui/material';

interface LoadingScreenProps {
  message?: string;
  size?: number;
  fullScreen?: boolean;
}

const LoadingScreen: React.FC<LoadingScreenProps> = ({
  message = 'Loading...',
  size = 60,
  fullScreen = true,
}) => {
  const content = (
    <Fade in timeout={300}>
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          gap: 3,
          ...(fullScreen && {
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'background.default',
            zIndex: 9999,
          }),
        }}
      >
        {/* Logo */}
        <Typography
          variant="h3"
          sx={{
            fontWeight: 700,
            background: 'linear-gradient(45deg, #1976d2, #42a5f5)',
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            mb: 2,
          }}
        >
          🚀 WALLETH
        </Typography>

        {/* Loading Spinner */}
        <Box sx={{ position: 'relative', display: 'inline-flex' }}>
          <CircularProgress
            size={size}
            thickness={4}
            sx={{
              color: 'primary.main',
              animationDuration: '1.5s',
            }}
          />
          <Box
            sx={{
              top: 0,
              left: 0,
              bottom: 0,
              right: 0,
              position: 'absolute',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <Typography
              variant="caption"
              component="div"
              color="primary.main"
              sx={{ fontWeight: 600 }}
            >
              💰
            </Typography>
          </Box>
        </Box>

        {/* Loading Message */}
        <Typography
          variant="body1"
          color="text.secondary"
          sx={{
            textAlign: 'center',
            maxWidth: 300,
            animation: 'pulse 2s infinite',
            '@keyframes pulse': {
              '0%, 100%': { opacity: 1 },
              '50%': { opacity: 0.5 },
            },
          }}
        >
          {message}
        </Typography>

        {/* Loading Dots Animation */}
        <Box
          sx={{
            display: 'flex',
            gap: 0.5,
            '& .dot': {
              width: 8,
              height: 8,
              borderRadius: '50%',
              backgroundColor: 'primary.main',
              animation: 'bounce 1.4s infinite ease-in-out both',
            },
            '& .dot:nth-of-type(1)': { animationDelay: '-0.32s' },
            '& .dot:nth-of-type(2)': { animationDelay: '-0.16s' },
            '@keyframes bounce': {
              '0%, 80%, 100%': {
                transform: 'scale(0)',
              },
              '40%': {
                transform: 'scale(1)',
              },
            },
          }}
        >
          <Box className="dot" />
          <Box className="dot" />
          <Box className="dot" />
        </Box>
      </Box>
    </Fade>
  );

  return content;
};

export default LoadingScreen;
