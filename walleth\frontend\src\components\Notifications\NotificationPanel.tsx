import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  Box,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  IconButton,
  Button,
  Divider,
  Chip,
  Avatar,
} from '@mui/material';
import {
  TrendingUp as TradeIcon,
  AccountBalanceWallet as WalletIcon,
  Security as SecurityIcon,
  Info as InfoIcon,
  Warning as WarningIcon,
  CheckCircle as SuccessIcon,
  <PERSON>rror as ErrorIcon,
  MarkEmailRead as MarkReadIcon,
  Clear as ClearIcon,
} from '@mui/icons-material';
import { formatDistanceToNow } from 'date-fns';

// Store
import { RootState } from '@/types';
import { markNotificationAsRead, markAllNotificationsAsRead } from '@/store/slices/uiSlice';

// Services
import { userAPI } from '@/services/api';

interface NotificationPanelProps {
  onClose: () => void;
}

const NotificationPanel: React.FC<NotificationPanelProps> = ({ onClose }) => {
  const dispatch = useDispatch();
  const { notifications, unreadCount } = useSelector((state: RootState) => state.ui);

  useEffect(() => {
    // Fetch notifications when panel opens
    fetchNotifications();
  }, []);

  const fetchNotifications = async () => {
    try {
      const response = await userAPI.getNotifications(1, 20);
      // Update notifications in store
      // dispatch(setNotifications(response.data));
    } catch (error) {
      console.error('Failed to fetch notifications:', error);
    }
  };

  const handleMarkAsRead = async (notificationId: string) => {
    try {
      await userAPI.markNotificationAsRead(notificationId);
      dispatch(markNotificationAsRead(notificationId));
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
    }
  };

  const handleMarkAllAsRead = async () => {
    try {
      await userAPI.markAllNotificationsAsRead();
      dispatch(markAllNotificationsAsRead());
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error);
    }
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'TRADE_EXECUTED':
      case 'ORDER_FILLED':
        return <TradeIcon />;
      case 'DEPOSIT_COMPLETED':
      case 'WITHDRAWAL_COMPLETED':
        return <WalletIcon />;
      case 'KYC_APPROVED':
        return <SuccessIcon />;
      case 'KYC_REJECTED':
        return <ErrorIcon />;
      case 'SECURITY_ALERT':
        return <SecurityIcon />;
      default:
        return <InfoIcon />;
    }
  };

  const getNotificationColor = (type: string) => {
    switch (type) {
      case 'TRADE_EXECUTED':
      case 'ORDER_FILLED':
        return 'primary';
      case 'DEPOSIT_COMPLETED':
      case 'KYC_APPROVED':
        return 'success';
      case 'WITHDRAWAL_COMPLETED':
        return 'info';
      case 'KYC_REJECTED':
      case 'SECURITY_ALERT':
        return 'error';
      default:
        return 'default';
    }
  };

  const formatNotificationTime = (createdAt: string) => {
    try {
      return formatDistanceToNow(new Date(createdAt), { addSuffix: true });
    } catch {
      return 'Unknown time';
    }
  };

  if (notifications.length === 0) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <InfoIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
        <Typography variant="h6" gutterBottom>
          No Notifications
        </Typography>
        <Typography variant="body2" color="text.secondary">
          You're all caught up! New notifications will appear here.
        </Typography>
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box sx={{ p: 2, borderBottom: '1px solid', borderColor: 'divider' }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
          <Typography variant="h6">
            Notifications
          </Typography>
          <IconButton size="small" onClick={onClose}>
            <ClearIcon />
          </IconButton>
        </Box>
        
        {unreadCount > 0 && (
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Chip
              size="small"
              label={`${unreadCount} unread`}
              color="primary"
              variant="outlined"
            />
            <Button
              size="small"
              startIcon={<MarkReadIcon />}
              onClick={handleMarkAllAsRead}
            >
              Mark all read
            </Button>
          </Box>
        )}
      </Box>

      {/* Notifications List */}
      <List sx={{ maxHeight: 400, overflow: 'auto', p: 0 }}>
        {notifications.map((notification, index) => (
          <React.Fragment key={notification.id}>
            <ListItem
              sx={{
                alignItems: 'flex-start',
                backgroundColor: notification.isRead ? 'transparent' : 'action.hover',
                '&:hover': {
                  backgroundColor: 'action.selected',
                },
              }}
            >
              <ListItemIcon sx={{ mt: 1 }}>
                <Avatar
                  sx={{
                    width: 32,
                    height: 32,
                    bgcolor: `${getNotificationColor(notification.type)}.main`,
                  }}
                >
                  {getNotificationIcon(notification.type)}
                </Avatar>
              </ListItemIcon>
              
              <ListItemText
                primary={
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                    <Typography
                      variant="subtitle2"
                      sx={{
                        fontWeight: notification.isRead ? 400 : 600,
                        flex: 1,
                        mr: 1,
                      }}
                    >
                      {notification.title}
                    </Typography>
                    {!notification.isRead && (
                      <IconButton
                        size="small"
                        onClick={() => handleMarkAsRead(notification.id)}
                        sx={{ p: 0.5 }}
                      >
                        <MarkReadIcon fontSize="small" />
                      </IconButton>
                    )}
                  </Box>
                }
                secondary={
                  <Box>
                    <Typography
                      variant="body2"
                      color="text.secondary"
                      sx={{ mb: 0.5 }}
                    >
                      {notification.message}
                    </Typography>
                    <Typography
                      variant="caption"
                      color="text.secondary"
                    >
                      {formatNotificationTime(notification.createdAt)}
                    </Typography>
                  </Box>
                }
              />
            </ListItem>
            
            {index < notifications.length - 1 && <Divider />}
          </React.Fragment>
        ))}
      </List>

      {/* Footer */}
      <Box sx={{ p: 2, borderTop: '1px solid', borderColor: 'divider' }}>
        <Button
          fullWidth
          variant="outlined"
          onClick={() => {
            onClose();
            window.location.href = '/profile';
          }}
        >
          View All Notifications
        </Button>
      </Box>
    </Box>
  );
};

export default NotificationPanel;
