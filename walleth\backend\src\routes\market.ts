import express from 'express';
import { query, validationResult } from 'express-validator';
import { PrismaClient } from '@prisma/client';
import { asyncHandler } from '../middleware/errorHandler';
import { ValidationError, NotFoundError } from '../middleware/errorHandler';
import { optionalAuthMiddleware } from '../middleware/auth';
import { PriceService } from '../services/priceService';
import { TradingEngine } from '../services/tradingEngine';

const router = express.Router();
const prisma = new PrismaClient();
const priceService = new PriceService();
const tradingEngine = new TradingEngine();

// Validation rules
const getCandlestickValidation = [
  query('interval').isIn(['1m', '5m', '15m', '30m', '1h', '4h', '1d', '1w']).withMessage('Invalid interval'),
  query('limit').optional().isInt({ min: 1, max: 1000 }).withMessage('Limit must be between 1 and 1000')
];

const getTradesValidation = [
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100')
];

// Get all trading pairs
router.get('/pairs', 
  optionalAuthMiddleware,
  asyncHandler(async (req, res) => {
    const tradingPairs = await prisma.tradingPair.findMany({
      where: { isActive: true },
      include: {
        baseCurrency: {
          select: {
            symbol: true,
            name: true,
            decimals: true,
            iconUrl: true
          }
        },
        quoteCurrency: {
          select: {
            symbol: true,
            name: true,
            decimals: true,
            iconUrl: true
          }
        }
      },
      orderBy: { symbol: 'asc' }
    });

    const formattedPairs = tradingPairs.map(pair => ({
      symbol: pair.symbol,
      baseCurrency: pair.baseCurrency,
      quoteCurrency: pair.quoteCurrency,
      minTradeAmount: pair.minTradeAmount.toString(),
      maxTradeAmount: pair.maxTradeAmount.toString(),
      priceDecimals: pair.priceDecimals,
      amountDecimals: pair.amountDecimals,
      makerFee: pair.makerFee.toString(),
      takerFee: pair.takerFee.toString(),
      isActive: pair.isActive
    }));

    res.json({
      success: true,
      data: { pairs: formattedPairs }
    });
  })
);

// Get ticker data for all pairs or specific pair
router.get('/ticker/:symbol?',
  optionalAuthMiddleware,
  asyncHandler(async (req, res) => {
    const { symbol } = req.params;

    if (symbol) {
      // Get ticker for specific pair
      const tradingPair = await prisma.tradingPair.findUnique({
        where: { symbol: symbol.toUpperCase() }
      });

      if (!tradingPair) {
        throw new NotFoundError('Trading pair not found');
      }

      const priceData = priceService.getCurrentPrice(symbol.toUpperCase());
      const orderBookStats = await priceService.getOrderBookStats(symbol.toUpperCase());

      const ticker = {
        symbol: symbol.toUpperCase(),
        price: priceData?.price || 0,
        change24h: priceData?.change24h || 0,
        volume24h: priceData?.volume24h || 0,
        high24h: priceData?.high24h || 0,
        low24h: priceData?.low24h || 0,
        bestBid: orderBookStats.bestBid,
        bestAsk: orderBookStats.bestAsk,
        spread: orderBookStats.spread,
        spreadPercentage: orderBookStats.spreadPercentage,
        timestamp: priceData?.timestamp || Date.now()
      };

      res.json({
        success: true,
        data: { ticker }
      });
    } else {
      // Get ticker for all pairs
      const allPrices = priceService.getAllCurrentPrices();
      
      const tickers = await Promise.all(
        allPrices.map(async (priceData) => {
          const orderBookStats = await priceService.getOrderBookStats(priceData.symbol);
          
          return {
            symbol: priceData.symbol,
            price: priceData.price,
            change24h: priceData.change24h,
            volume24h: priceData.volume24h,
            high24h: priceData.high24h,
            low24h: priceData.low24h,
            bestBid: orderBookStats.bestBid,
            bestAsk: orderBookStats.bestAsk,
            spread: orderBookStats.spread,
            spreadPercentage: orderBookStats.spreadPercentage,
            timestamp: priceData.timestamp
          };
        })
      );

      res.json({
        success: true,
        data: { tickers }
      });
    }
  })
);

// Get order book for a trading pair
router.get('/orderbook/:symbol',
  optionalAuthMiddleware,
  asyncHandler(async (req, res) => {
    const { symbol } = req.params;
    const { depth = 20 } = req.query;

    const tradingPair = await prisma.tradingPair.findUnique({
      where: { symbol: symbol.toUpperCase() }
    });

    if (!tradingPair) {
      throw new NotFoundError('Trading pair not found');
    }

    const orderBook = tradingEngine.getOrderBookSnapshot(symbol.toUpperCase());

    if (!orderBook) {
      res.json({
        success: true,
        data: {
          symbol: symbol.toUpperCase(),
          bids: [],
          asks: [],
          timestamp: Date.now()
        }
      });
      return;
    }

    // Limit depth
    const limitedOrderBook = {
      symbol: symbol.toUpperCase(),
      bids: orderBook.bids.slice(0, Number(depth)),
      asks: orderBook.asks.slice(0, Number(depth)),
      timestamp: Date.now()
    };

    res.json({
      success: true,
      data: limitedOrderBook
    });
  })
);

// Get recent trades for a trading pair
router.get('/trades/:symbol',
  getTradesValidation,
  optionalAuthMiddleware,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError(errors.array().map(err => err.msg).join(', '));
    }

    const { symbol } = req.params;
    const { limit = 50 } = req.query;

    const tradingPair = await prisma.tradingPair.findUnique({
      where: { symbol: symbol.toUpperCase() }
    });

    if (!tradingPair) {
      throw new NotFoundError('Trading pair not found');
    }

    const trades = await prisma.trade.findMany({
      where: { tradingPairId: tradingPair.id },
      orderBy: { createdAt: 'desc' },
      take: Number(limit),
      select: {
        id: true,
        amount: true,
        price: true,
        createdAt: true,
        buyOrder: {
          select: { side: true }
        }
      }
    });

    const formattedTrades = trades.map(trade => ({
      id: trade.id,
      price: trade.price.toString(),
      amount: trade.amount.toString(),
      side: trade.buyOrder.side === 'BUY' ? 'buy' : 'sell',
      timestamp: trade.createdAt.getTime()
    }));

    res.json({
      success: true,
      data: {
        symbol: symbol.toUpperCase(),
        trades: formattedTrades
      }
    });
  })
);

// Get candlestick/OHLCV data
router.get('/klines/:symbol',
  getCandlestickValidation,
  optionalAuthMiddleware,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError(errors.array().map(err => err.msg).join(', '));
    }

    const { symbol } = req.params;
    const { interval, limit = 100 } = req.query;

    const tradingPair = await prisma.tradingPair.findUnique({
      where: { symbol: symbol.toUpperCase() }
    });

    if (!tradingPair) {
      throw new NotFoundError('Trading pair not found');
    }

    const candlestickData = await priceService.getCandlestickData(
      symbol.toUpperCase(),
      interval as string,
      Number(limit)
    );

    const formattedData = candlestickData.map(candle => ({
      timestamp: candle.timestamp,
      open: candle.open.toString(),
      high: candle.high.toString(),
      low: candle.low.toString(),
      close: candle.close.toString(),
      volume: candle.volume.toString()
    }));

    res.json({
      success: true,
      data: {
        symbol: symbol.toUpperCase(),
        interval: interval as string,
        klines: formattedData
      }
    });
  })
);

// Get 24hr statistics for all pairs
router.get('/stats/24hr',
  optionalAuthMiddleware,
  asyncHandler(async (req, res) => {
    const tradingPairs = await prisma.tradingPair.findMany({
      where: { isActive: true },
      select: { symbol: true }
    });

    const stats = await Promise.all(
      tradingPairs.map(async (pair) => {
        const priceData = priceService.getCurrentPrice(pair.symbol);
        const orderBookStats = await priceService.getOrderBookStats(pair.symbol);

        // Get 24h trade count
        const tradeCount = await prisma.trade.count({
          where: {
            tradingPair: { symbol: pair.symbol },
            createdAt: {
              gte: new Date(Date.now() - 24 * 60 * 60 * 1000)
            }
          }
        });

        return {
          symbol: pair.symbol,
          price: priceData?.price || 0,
          change24h: priceData?.change24h || 0,
          volume24h: priceData?.volume24h || 0,
          high24h: priceData?.high24h || 0,
          low24h: priceData?.low24h || 0,
          bestBid: orderBookStats.bestBid,
          bestAsk: orderBookStats.bestAsk,
          tradeCount,
          timestamp: priceData?.timestamp || Date.now()
        };
      })
    );

    res.json({
      success: true,
      data: { stats }
    });
  })
);

// Get market summary
router.get('/summary',
  optionalAuthMiddleware,
  asyncHandler(async (req, res) => {
    const [
      totalPairs,
      activePairs,
      totalTrades24h,
      totalVolume24h
    ] = await Promise.all([
      prisma.tradingPair.count(),
      prisma.tradingPair.count({ where: { isActive: true } }),
      prisma.trade.count({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000)
          }
        }
      }),
      prisma.trade.aggregate({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000)
          }
        },
        _sum: {
          amount: true
        }
      })
    ]);

    // Get top gainers and losers
    const allPrices = priceService.getAllCurrentPrices();
    const sortedByChange = allPrices.sort((a, b) => b.change24h - a.change24h);
    
    const topGainers = sortedByChange.slice(0, 5).map(price => ({
      symbol: price.symbol,
      price: price.price,
      change24h: price.change24h
    }));

    const topLosers = sortedByChange.slice(-5).reverse().map(price => ({
      symbol: price.symbol,
      price: price.price,
      change24h: price.change24h
    }));

    const summary = {
      totalPairs,
      activePairs,
      totalTrades24h,
      totalVolume24h: totalVolume24h._sum.amount?.toString() || '0',
      topGainers,
      topLosers,
      timestamp: Date.now()
    };

    res.json({
      success: true,
      data: { summary }
    });
  })
);

// Get currencies/assets
router.get('/currencies',
  optionalAuthMiddleware,
  asyncHandler(async (req, res) => {
    const currencies = await prisma.currency.findMany({
      where: { isActive: true },
      orderBy: { symbol: 'asc' },
      select: {
        id: true,
        symbol: true,
        name: true,
        decimals: true,
        isFiat: true,
        network: true,
        iconUrl: true,
        minDeposit: true,
        minWithdrawal: true,
        withdrawalFee: true,
        confirmations: true
      }
    });

    const formattedCurrencies = currencies.map(currency => ({
      ...currency,
      minDeposit: currency.minDeposit.toString(),
      minWithdrawal: currency.minWithdrawal.toString(),
      withdrawalFee: currency.withdrawalFee.toString()
    }));

    res.json({
      success: true,
      data: { currencies: formattedCurrencies }
    });
  })
);

export default router;
