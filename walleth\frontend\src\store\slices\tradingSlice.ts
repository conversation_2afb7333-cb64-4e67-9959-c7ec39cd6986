import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { TradingState, Order, Trade, PlaceOrderData } from '@/types';
import { tradingAPI } from '@/services/api';
import toast from 'react-hot-toast';

// Initial state
const initialState: TradingState = {
  orders: [],
  trades: [],
  orderHistory: [],
  tradeHistory: [],
  stats: {
    totalTrades: 0,
    totalVolume: '0',
    totalFees: {
      buyerFees: '0',
      sellerFees: '0',
    },
    activeOrders: 0,
  },
  isLoading: false,
  error: null,
};

// Async thunks
export const placeOrder = createAsyncThunk(
  'trading/placeOrder',
  async (orderData: PlaceOrderData, { rejectWithValue }) => {
    try {
      const response = await tradingAPI.placeOrder(orderData);
      toast.success('Order placed successfully!');
      return response.data.order;
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to place order';
      toast.error(message);
      return rejectWithValue(message);
    }
  }
);

export const cancelOrder = createAsyncThunk(
  'trading/cancelOrder',
  async (orderId: string, { rejectWithValue }) => {
    try {
      await tradingAPI.cancelOrder(orderId);
      toast.success('Order cancelled successfully!');
      return orderId;
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to cancel order';
      toast.error(message);
      return rejectWithValue(message);
    }
  }
);

export const fetchOrders = createAsyncThunk(
  'trading/fetchOrders',
  async (params?: { 
    tradingPair?: string; 
    status?: string; 
    side?: string; 
    page?: number; 
    limit?: number; 
  }, { rejectWithValue }) => {
    try {
      const response = await tradingAPI.getOrders(params);
      return response.data;
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to fetch orders';
      return rejectWithValue(message);
    }
  }
);

export const fetchOrderHistory = createAsyncThunk(
  'trading/fetchOrderHistory',
  async (params?: { 
    tradingPair?: string; 
    status?: string; 
    side?: string; 
    page?: number; 
    limit?: number; 
  }, { rejectWithValue }) => {
    try {
      const response = await tradingAPI.getOrders({
        ...params,
        status: 'FILLED,CANCELLED,REJECTED,EXPIRED',
      });
      return response.data;
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to fetch order history';
      return rejectWithValue(message);
    }
  }
);

export const fetchTrades = createAsyncThunk(
  'trading/fetchTrades',
  async (params?: { 
    tradingPair?: string; 
    page?: number; 
    limit?: number; 
  }, { rejectWithValue }) => {
    try {
      const response = await tradingAPI.getTrades(params);
      return response.data;
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to fetch trades';
      return rejectWithValue(message);
    }
  }
);

export const fetchTradeHistory = createAsyncThunk(
  'trading/fetchTradeHistory',
  async (params?: { 
    tradingPair?: string; 
    page?: number; 
    limit?: number; 
  }, { rejectWithValue }) => {
    try {
      const response = await tradingAPI.getTrades(params);
      return response.data;
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to fetch trade history';
      return rejectWithValue(message);
    }
  }
);

export const fetchTradingStats = createAsyncThunk(
  'trading/fetchTradingStats',
  async (_, { rejectWithValue }) => {
    try {
      const response = await tradingAPI.getTradingStats();
      return response.data.stats;
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to fetch trading stats';
      return rejectWithValue(message);
    }
  }
);

export const fetchOrderDetails = createAsyncThunk(
  'trading/fetchOrderDetails',
  async (orderId: string, { rejectWithValue }) => {
    try {
      const response = await tradingAPI.getOrderDetails(orderId);
      return response.data.order;
    } catch (error: any) {
      const message = error.response?.data?.message || 'Failed to fetch order details';
      return rejectWithValue(message);
    }
  }
);

// Trading slice
const tradingSlice = createSlice({
  name: 'trading',
  initialState,
  reducers: {
    updateOrder: (state, action: PayloadAction<Order>) => {
      const updatedOrder = action.payload;
      const index = state.orders.findIndex(order => order.id === updatedOrder.id);
      
      if (index !== -1) {
        state.orders[index] = updatedOrder;
      } else {
        // Add new order if not found
        state.orders.unshift(updatedOrder);
      }
      
      // Update stats
      state.stats.activeOrders = state.orders.filter(
        order => order.status === 'PENDING' || order.status === 'PARTIAL_FILLED'
      ).length;
    },
    
    addTrade: (state, action: PayloadAction<Trade>) => {
      const newTrade = action.payload;
      state.trades.unshift(newTrade);
      
      // Keep only last 100 trades
      if (state.trades.length > 100) {
        state.trades = state.trades.slice(0, 100);
      }
      
      // Update stats
      state.stats.totalTrades += 1;
    },
    
    removeOrder: (state, action: PayloadAction<string>) => {
      const orderId = action.payload;
      state.orders = state.orders.filter(order => order.id !== orderId);
      
      // Update stats
      state.stats.activeOrders = state.orders.filter(
        order => order.status === 'PENDING' || order.status === 'PARTIAL_FILLED'
      ).length;
    },
    
    clearError: (state) => {
      state.error = null;
    },
    
    resetTradingData: (state) => {
      state.orders = [];
      state.trades = [];
      state.orderHistory = [];
      state.tradeHistory = [];
      state.stats = {
        totalTrades: 0,
        totalVolume: '0',
        totalFees: {
          buyerFees: '0',
          sellerFees: '0',
        },
        activeOrders: 0,
      };
    },
    
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
  },
  extraReducers: (builder) => {
    // Place Order
    builder
      .addCase(placeOrder.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(placeOrder.fulfilled, (state, action) => {
        state.isLoading = false;
        state.orders.unshift(action.payload);
        state.stats.activeOrders += 1;
        state.error = null;
      })
      .addCase(placeOrder.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Cancel Order
    builder
      .addCase(cancelOrder.fulfilled, (state, action) => {
        const orderId = action.payload;
        const orderIndex = state.orders.findIndex(order => order.id === orderId);
        
        if (orderIndex !== -1) {
          state.orders[orderIndex].status = 'CANCELLED';
          state.stats.activeOrders = state.orders.filter(
            order => order.status === 'PENDING' || order.status === 'PARTIAL_FILLED'
          ).length;
        }
      })
      .addCase(cancelOrder.rejected, (state, action) => {
        state.error = action.payload as string;
      });

    // Fetch Orders
    builder
      .addCase(fetchOrders.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchOrders.fulfilled, (state, action) => {
        state.isLoading = false;
        state.orders = action.payload.orders;
        state.stats.activeOrders = action.payload.orders.filter(
          (order: Order) => order.status === 'PENDING' || order.status === 'PARTIAL_FILLED'
        ).length;
        state.error = null;
      })
      .addCase(fetchOrders.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch Order History
    builder
      .addCase(fetchOrderHistory.fulfilled, (state, action) => {
        state.orderHistory = action.payload.orders;
      });

    // Fetch Trades
    builder
      .addCase(fetchTrades.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchTrades.fulfilled, (state, action) => {
        state.isLoading = false;
        state.trades = action.payload.trades;
        state.error = null;
      })
      .addCase(fetchTrades.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Fetch Trade History
    builder
      .addCase(fetchTradeHistory.fulfilled, (state, action) => {
        state.tradeHistory = action.payload.trades;
      });

    // Fetch Trading Stats
    builder
      .addCase(fetchTradingStats.fulfilled, (state, action) => {
        state.stats = action.payload;
      });

    // Fetch Order Details
    builder
      .addCase(fetchOrderDetails.fulfilled, (state, action) => {
        const updatedOrder = action.payload;
        const index = state.orders.findIndex(order => order.id === updatedOrder.id);
        
        if (index !== -1) {
          state.orders[index] = updatedOrder;
        }
      });
  },
});

export const {
  updateOrder,
  addTrade,
  removeOrder,
  clearError,
  resetTradingData,
  setLoading,
} = tradingSlice.actions;

export default tradingSlice.reducer;
