<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="CryptoNest - Your Secure Cryptocurrency Trading Haven" />
    <meta name="keywords" content="cryptocurrency, exchange, trading, bitcoin, ethereum, blockchain, cryptonest" />
    <meta name="author" content="CryptoNest Team" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://cryptonest.com/" />
    <meta property="og:title" content="CryptoNest - Your Crypto Trading Haven" />
    <meta property="og:description" content="Secure and intuitive cryptocurrency exchange platform with advanced trading features" />
    <meta property="og:image" content="/og-image.png" />

    <!-- Twitter -->
    <meta property="twitter:card" content="summary_large_image" />
    <meta property="twitter:url" content="https://cryptonest.com/" />
    <meta property="twitter:title" content="CryptoNest - Your Crypto Trading Haven" />
    <meta property="twitter:description" content="Secure and intuitive cryptocurrency exchange platform with advanced trading features" />
    <meta property="twitter:image" content="/og-image.png" />

    <!-- Preconnect to external domains -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
    
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet" />
    
    <title>CryptoNest - Your Crypto Trading Haven</title>
    
    <style>
      /* Loading screen styles */
      #loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        font-family: 'Inter', sans-serif;
      }
      
      .loading-logo {
        font-size: 3rem;
        font-weight: 700;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 2rem;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
      }
      
      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 4px solid rgba(255,255,255,0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      .loading-text {
        color: white;
        margin-top: 1rem;
        font-size: 1rem;
        opacity: 0.9;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* Hide loading screen when app loads */
      .app-loaded #loading-screen {
        display: none;
      }
      
      /* Global styles */
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      body {
        font-family: 'Inter', sans-serif;
        background-color: #0a0e27;
        color: #ffffff;
        overflow-x: hidden;
      }
      
      #root {
        min-height: 100vh;
      }
      
      /* Scrollbar styles */
      ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
      }
      
      ::-webkit-scrollbar-track {
        background: #1a1a2e;
      }
      
      ::-webkit-scrollbar-thumb {
        background: #16213e;
        border-radius: 4px;
      }
      
      ::-webkit-scrollbar-thumb:hover {
        background: #1976d2;
      }
    </style>
  </head>
  <body>
    <!-- Loading Screen -->
    <div id="loading-screen">
      <div class="loading-logo">🏠 CryptoNest</div>
      <div class="loading-spinner"></div>
      <div class="loading-text">Loading your trading platform...</div>
    </div>
    
    <!-- React App Root -->
    <div id="root"></div>
    
    <script type="module" src="/src/main.tsx"></script>
    
    <script>
      // Hide loading screen when page is fully loaded
      window.addEventListener('load', function() {
        setTimeout(function() {
          document.body.classList.add('app-loaded');
        }, 1000);
      });
      
      // Service Worker Registration (for PWA support)
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', function() {
          navigator.serviceWorker.register('/sw.js')
            .then(function(registration) {
              console.log('SW registered: ', registration);
            })
            .catch(function(registrationError) {
              console.log('SW registration failed: ', registrationError);
            });
        });
      }
    </script>
  </body>
</html>
