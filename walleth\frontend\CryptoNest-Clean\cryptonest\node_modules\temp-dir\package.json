{"name": "temp-dir", "version": "2.0.0", "description": "Get the real path of the system temp directory", "license": "MIT", "repository": "sindresorhus/temp-dir", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=8"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["temp", "tmpdir", "os", "system", "real", "path", "realpath", "resolved", "temporary", "directory", "folder"], "devDependencies": {"ava": "^1.4.1", "proxyquire": "^2.1.0", "tsd": "^0.7.2", "xo": "^0.24.0"}}