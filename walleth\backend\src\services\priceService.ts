import { PrismaClient } from '@prisma/client';
import { Decimal } from '@prisma/client/runtime/library';
import { logger } from '../utils/logger';
import { WebSocketService } from './websocket';

const prisma = new PrismaClient();

interface PriceData {
  symbol: string;
  price: number;
  change24h: number;
  volume24h: number;
  high24h: number;
  low24h: number;
  timestamp: number;
}

interface CandlestickData {
  timestamp: number;
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

export class PriceService {
  private wsService?: WebSocketService;
  private priceUpdateInterval?: NodeJS.Timeout;
  private candlestickInterval?: NodeJS.Timeout;
  private currentPrices: Map<string, PriceData> = new Map();

  constructor(wsService?: WebSocketService) {
    this.wsService = wsService;
  }

  public startPriceUpdates(): void {
    // Update prices every 5 seconds
    this.priceUpdateInterval = setInterval(() => {
      this.updatePrices();
    }, 5000);

    // Generate candlestick data every minute
    this.candlestickInterval = setInterval(() => {
      this.generateCandlestickData();
    }, 60000);

    // Initial price update
    this.updatePrices();

    logger.info('Price service started');
  }

  public stopPriceUpdates(): void {
    if (this.priceUpdateInterval) {
      clearInterval(this.priceUpdateInterval);
    }
    if (this.candlestickInterval) {
      clearInterval(this.candlestickInterval);
    }
    logger.info('Price service stopped');
  }

  private async updatePrices(): Promise<void> {
    try {
      // Get all active trading pairs
      const tradingPairs = await prisma.tradingPair.findMany({
        where: { isActive: true },
        include: {
          baseCurrency: true,
          quoteCurrency: true
        }
      });

      for (const pair of tradingPairs) {
        await this.updatePairPrice(pair);
      }
    } catch (error) {
      logger.error('Failed to update prices:', error);
    }
  }

  private async updatePairPrice(tradingPair: any): Promise<void> {
    try {
      // Get latest trades for price calculation
      const latestTrades = await prisma.trade.findMany({
        where: {
          tradingPairId: tradingPair.id,
          createdAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
          }
        },
        orderBy: { createdAt: 'desc' },
        take: 100
      });

      if (latestTrades.length === 0) {
        // No recent trades, use mock data or external API
        await this.generateMockPrice(tradingPair);
        return;
      }

      // Calculate current price (last trade price)
      const currentPrice = parseFloat(latestTrades[0].price.toString());

      // Calculate 24h statistics
      const prices24h = latestTrades.map(trade => parseFloat(trade.price.toString()));
      const volumes24h = latestTrades.map(trade => parseFloat(trade.amount.toString()));

      const high24h = Math.max(...prices24h);
      const low24h = Math.min(...prices24h);
      const volume24h = volumes24h.reduce((sum, vol) => sum + vol, 0);

      // Calculate 24h change
      const price24hAgo = prices24h[prices24h.length - 1] || currentPrice;
      const change24h = ((currentPrice - price24hAgo) / price24hAgo) * 100;

      const priceData: PriceData = {
        symbol: tradingPair.symbol,
        price: currentPrice,
        change24h,
        volume24h,
        high24h,
        low24h,
        timestamp: Date.now()
      };

      // Store current price
      this.currentPrices.set(tradingPair.symbol, priceData);

      // Broadcast price update
      if (this.wsService) {
        this.wsService.broadcastTickerUpdate(tradingPair.symbol, priceData);
      }

      logger.debug('Price updated', {
        symbol: tradingPair.symbol,
        price: currentPrice,
        change24h: change24h.toFixed(2) + '%'
      });
    } catch (error) {
      logger.error(`Failed to update price for ${tradingPair.symbol}:`, error);
    }
  }

  private async generateMockPrice(tradingPair: any): Promise<void> {
    // Generate realistic mock prices for demonstration
    const basePrice = this.getBasePriceForPair(tradingPair.symbol);
    const volatility = 0.02; // 2% volatility
    
    const randomChange = (Math.random() - 0.5) * 2 * volatility;
    const currentPrice = basePrice * (1 + randomChange);
    
    const change24h = (Math.random() - 0.5) * 10; // Random change between -5% and +5%
    const volume24h = Math.random() * 1000000; // Random volume
    
    const priceData: PriceData = {
      symbol: tradingPair.symbol,
      price: currentPrice,
      change24h,
      volume24h,
      high24h: currentPrice * 1.05,
      low24h: currentPrice * 0.95,
      timestamp: Date.now()
    };

    this.currentPrices.set(tradingPair.symbol, priceData);

    if (this.wsService) {
      this.wsService.broadcastTickerUpdate(tradingPair.symbol, priceData);
    }
  }

  private getBasePriceForPair(symbol: string): number {
    // Mock base prices for common trading pairs
    const basePrices: { [key: string]: number } = {
      'BTCUSDT': 45000,
      'ETHUSDT': 3000,
      'BNBUSDT': 300,
      'ADAUSDT': 0.5,
      'SOLUSDT': 100,
      'DOTUSDT': 7,
      'LINKUSDT': 15,
      'LTCUSDT': 150,
      'BCHUSDT': 250,
      'XRPUSDT': 0.6
    };

    return basePrices[symbol] || 1;
  }

  private async generateCandlestickData(): Promise<void> {
    try {
      const tradingPairs = await prisma.tradingPair.findMany({
        where: { isActive: true }
      });

      const now = new Date();
      const oneMinuteAgo = new Date(now.getTime() - 60 * 1000);

      for (const pair of tradingPairs) {
        await this.generateCandlestickForPair(pair, oneMinuteAgo, now, '1m');
      }
    } catch (error) {
      logger.error('Failed to generate candlestick data:', error);
    }
  }

  private async generateCandlestickForPair(
    tradingPair: any,
    startTime: Date,
    endTime: Date,
    interval: string
  ): Promise<void> {
    try {
      // Get trades in the time period
      const trades = await prisma.trade.findMany({
        where: {
          tradingPairId: tradingPair.id,
          createdAt: {
            gte: startTime,
            lt: endTime
          }
        },
        orderBy: { createdAt: 'asc' }
      });

      if (trades.length === 0) {
        // No trades, use current price or generate mock data
        const currentPrice = this.currentPrices.get(tradingPair.symbol);
        if (currentPrice) {
          await this.saveCandlestickData(tradingPair.id, {
            timestamp: startTime.getTime(),
            open: currentPrice.price,
            high: currentPrice.price,
            low: currentPrice.price,
            close: currentPrice.price,
            volume: 0
          }, interval);
        }
        return;
      }

      // Calculate OHLCV data
      const prices = trades.map(trade => parseFloat(trade.price.toString()));
      const volumes = trades.map(trade => parseFloat(trade.amount.toString()));

      const candlestick: CandlestickData = {
        timestamp: startTime.getTime(),
        open: prices[0],
        high: Math.max(...prices),
        low: Math.min(...prices),
        close: prices[prices.length - 1],
        volume: volumes.reduce((sum, vol) => sum + vol, 0)
      };

      await this.saveCandlestickData(tradingPair.id, candlestick, interval);

      logger.debug('Candlestick generated', {
        symbol: tradingPair.symbol,
        interval,
        timestamp: startTime.toISOString(),
        ohlcv: candlestick
      });
    } catch (error) {
      logger.error(`Failed to generate candlestick for ${tradingPair.symbol}:`, error);
    }
  }

  private async saveCandlestickData(
    tradingPairId: string,
    candlestick: CandlestickData,
    interval: string
  ): Promise<void> {
    try {
      await prisma.priceHistory.upsert({
        where: {
          tradingPairId_timestamp_interval: {
            tradingPairId,
            timestamp: new Date(candlestick.timestamp),
            interval
          }
        },
        update: {
          open: new Decimal(candlestick.open),
          high: new Decimal(candlestick.high),
          low: new Decimal(candlestick.low),
          close: new Decimal(candlestick.close),
          volume: new Decimal(candlestick.volume)
        },
        create: {
          tradingPairId,
          timestamp: new Date(candlestick.timestamp),
          interval,
          open: new Decimal(candlestick.open),
          high: new Decimal(candlestick.high),
          low: new Decimal(candlestick.low),
          close: new Decimal(candlestick.close),
          volume: new Decimal(candlestick.volume)
        }
      });
    } catch (error) {
      logger.error('Failed to save candlestick data:', error);
    }
  }

  public async getCandlestickData(
    tradingPairSymbol: string,
    interval: string,
    limit: number = 100
  ): Promise<CandlestickData[]> {
    try {
      const tradingPair = await prisma.tradingPair.findUnique({
        where: { symbol: tradingPairSymbol }
      });

      if (!tradingPair) {
        throw new Error('Trading pair not found');
      }

      const priceHistory = await prisma.priceHistory.findMany({
        where: {
          tradingPairId: tradingPair.id,
          interval
        },
        orderBy: { timestamp: 'desc' },
        take: limit
      });

      return priceHistory.map(record => ({
        timestamp: record.timestamp.getTime(),
        open: parseFloat(record.open.toString()),
        high: parseFloat(record.high.toString()),
        low: parseFloat(record.low.toString()),
        close: parseFloat(record.close.toString()),
        volume: parseFloat(record.volume.toString())
      })).reverse();
    } catch (error) {
      logger.error('Failed to get candlestick data:', error);
      throw error;
    }
  }

  public getCurrentPrice(tradingPairSymbol: string): PriceData | null {
    return this.currentPrices.get(tradingPairSymbol) || null;
  }

  public getAllCurrentPrices(): PriceData[] {
    return Array.from(this.currentPrices.values());
  }

  public async getOrderBookStats(tradingPairSymbol: string): Promise<any> {
    try {
      const tradingPair = await prisma.tradingPair.findUnique({
        where: { symbol: tradingPairSymbol }
      });

      if (!tradingPair) {
        throw new Error('Trading pair not found');
      }

      // Get pending orders for spread calculation
      const buyOrders = await prisma.order.findMany({
        where: {
          tradingPairId: tradingPair.id,
          side: 'BUY',
          status: 'PENDING',
          type: 'LIMIT'
        },
        orderBy: { price: 'desc' },
        take: 1
      });

      const sellOrders = await prisma.order.findMany({
        where: {
          tradingPairId: tradingPair.id,
          side: 'SELL',
          status: 'PENDING',
          type: 'LIMIT'
        },
        orderBy: { price: 'asc' },
        take: 1
      });

      const bestBid = buyOrders[0]?.price ? parseFloat(buyOrders[0].price.toString()) : null;
      const bestAsk = sellOrders[0]?.price ? parseFloat(sellOrders[0].price.toString()) : null;
      
      let spread = null;
      let spreadPercentage = null;
      
      if (bestBid && bestAsk) {
        spread = bestAsk - bestBid;
        spreadPercentage = (spread / bestAsk) * 100;
      }

      return {
        bestBid,
        bestAsk,
        spread,
        spreadPercentage
      };
    } catch (error) {
      logger.error('Failed to get order book stats:', error);
      throw error;
    }
  }
}
