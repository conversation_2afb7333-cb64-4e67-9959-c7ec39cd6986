{"version": 3, "file": "redux-persist.min.js", "sources": ["../src/constants.js", "../src/stateReconciler/autoMergeLevel1.js", "../src/createPersistoid.js", "../src/getStoredState.js", "../src/purgeStoredState.js", "../src/persistReducer.js", "../node_modules/symbol-observable/es/index.js", "../node_modules/symbol-observable/es/ponyfill.js", "../node_modules/redux/es/redux.js", "../src/stateReconciler/autoMergeLevel2.js", "../src/persistStore.js", "../src/persistCombineReducers.js", "../src/createMigrate.js", "../src/createTransform.js"], "sourcesContent": ["// @flow\n\nexport const KEY_PREFIX = 'persist:'\nexport const FLUSH = 'persist/FLUSH'\nexport const REHYDRATE = 'persist/REHYDRATE'\nexport const PAUSE = 'persist/PAUSE'\nexport const PERSIST = 'persist/PERSIST'\nexport const PURGE = 'persist/PURGE'\nexport const REGISTER = 'persist/REGISTER'\nexport const DEFAULT_VERSION = -1\n", "// @flow\n\n/*\n  autoMergeLevel1: \n    - merges 1 level of substate\n    - skips substate if already modified\n*/\n\nimport type { PersistConfig } from '../types'\n\nexport default function autoMergeLevel1<State: Object>(\n  inboundState: State,\n  originalState: State,\n  reducedState: State,\n  { debug }: PersistConfig\n): State {\n  let newState = { ...reducedState }\n  // only rehydrate if inboundState exists and is an object\n  if (inboundState && typeof inboundState === 'object') {\n    Object.keys(inboundState).forEach(key => {\n      // ignore _persist data\n      if (key === '_persist') return\n      // if reducer modifies substate, skip auto rehydration\n      if (originalState[key] !== reducedState[key]) {\n        if (process.env.NODE_ENV !== 'production' && debug)\n          console.log(\n            'redux-persist/stateReconciler: sub state for key `%s` modified, skipping.',\n            key\n          )\n        return\n      }\n      // otherwise hard set the new value\n      newState[key] = inboundState[key]\n    })\n  }\n\n  if (\n    process.env.NODE_ENV !== 'production' &&\n    debug &&\n    inboundState &&\n    typeof inboundState === 'object'\n  )\n    console.log(\n      `redux-persist/stateReconciler: rehydrated keys '${Object.keys(\n        inboundState\n      ).join(', ')}'`\n    )\n\n  return newState\n}\n", "// @flow\n\nimport { KEY_PREFIX, REHYDRATE } from './constants'\n\nimport type { Persistoid, PersistConfig, Transform } from './types'\n\ntype IntervalID = any // @TODO remove once flow < 0.63 support is no longer required.\n\nexport default function createPersistoid(config: PersistConfig): Persistoid {\n  // defaults\n  const blacklist: ?Array<string> = config.blacklist || null\n  const whitelist: ?Array<string> = config.whitelist || null\n  const transforms = config.transforms || []\n  const throttle = config.throttle || 0\n  const storageKey = `${\n    config.keyPrefix !== undefined ? config.keyPrefix : KEY_PREFIX\n  }${config.key}`\n  const storage = config.storage\n  let serialize\n  if (config.serialize === false) {\n    serialize = x => x\n  } else if (typeof config.serialize === 'function') {\n    serialize = config.serialize\n  } else {\n    serialize = defaultSerialize\n  }\n  const writeFailHandler = config.writeFailHandler || null\n\n  // initialize stateful values\n  let lastState = {}\n  let stagedState = {}\n  let keysToProcess = []\n  let timeIterator: ?IntervalID = null\n  let writePromise = null\n\n  const update = (state: Object) => {\n    // add any changed keys to the queue\n    Object.keys(state).forEach(key => {\n      if (!passWhitelistBlacklist(key)) return // is keyspace ignored? noop\n      if (lastState[key] === state[key]) return // value unchanged? noop\n      if (keysToProcess.indexOf(key) !== -1) return // is key already queued? noop\n      keysToProcess.push(key) // add key to queue\n    })\n\n    //if any key is missing in the new state which was present in the lastState,\n    //add it for processing too\n    Object.keys(lastState).forEach(key => {\n      if (\n        state[key] === undefined &&\n        passWhitelistBlacklist(key) &&\n        keysToProcess.indexOf(key) === -1 &&\n        lastState[key] !== undefined\n      ) {\n        keysToProcess.push(key)\n      }\n    })\n\n    // start the time iterator if not running (read: throttle)\n    if (timeIterator === null) {\n      timeIterator = setInterval(processNextKey, throttle)\n    }\n\n    lastState = state\n  }\n\n  function processNextKey() {\n    if (keysToProcess.length === 0) {\n      if (timeIterator) clearInterval(timeIterator)\n      timeIterator = null\n      return\n    }\n\n    let key = keysToProcess.shift()\n    let endState = transforms.reduce((subState, transformer) => {\n      return transformer.in(subState, key, lastState)\n    }, lastState[key])\n\n    if (endState !== undefined) {\n      try {\n        stagedState[key] = serialize(endState)\n      } catch (err) {\n        console.error(\n          'redux-persist/createPersistoid: error serializing state',\n          err\n        )\n      }\n    } else {\n      //if the endState is undefined, no need to persist the existing serialized content\n      delete stagedState[key]\n    }\n\n    if (keysToProcess.length === 0) {\n      writeStagedState()\n    }\n  }\n\n  function writeStagedState() {\n    // cleanup any removed keys just before write.\n    Object.keys(stagedState).forEach(key => {\n      if (lastState[key] === undefined) {\n        delete stagedState[key]\n      }\n    })\n\n    writePromise = storage\n      .setItem(storageKey, serialize(stagedState))\n      .catch(onWriteFail)\n  }\n\n  function passWhitelistBlacklist(key) {\n    if (whitelist && whitelist.indexOf(key) === -1 && key !== '_persist')\n      return false\n    if (blacklist && blacklist.indexOf(key) !== -1) return false\n    return true\n  }\n\n  function onWriteFail(err) {\n    // @TODO add fail handlers (typically storage full)\n    if (writeFailHandler) writeFailHandler(err)\n    if (err && process.env.NODE_ENV !== 'production') {\n      console.error('Error storing data', err)\n    }\n  }\n\n  const flush = () => {\n    while (keysToProcess.length !== 0) {\n      processNextKey()\n    }\n    return writePromise || Promise.resolve()\n  }\n\n  // return `persistoid`\n  return {\n    update,\n    flush,\n  }\n}\n\n// @NOTE in the future this may be exposed via config\nfunction defaultSerialize(data) {\n  return JSON.stringify(data)\n}\n", "// @flow\n\nimport type { PersistConfig } from './types'\n\nimport { KEY_PREFIX } from './constants'\n\nexport default function getStoredState(\n  config: PersistConfig\n): Promise<Object | void> {\n  const transforms = config.transforms || []\n  const storageKey = `${\n    config.keyPrefix !== undefined ? config.keyPrefix : KEY_PREFIX\n  }${config.key}`\n  const storage = config.storage\n  const debug = config.debug\n  let deserialize\n  if (config.deserialize === false) {\n    deserialize = x => x\n  } else if (typeof config.deserialize === 'function') {\n    deserialize = config.deserialize\n  } else {\n    deserialize = defaultDeserialize\n  }\n  return storage.getItem(storageKey).then(serialized => {\n    if (!serialized) return undefined\n    else {\n      try {\n        let state = {}\n        let rawState = deserialize(serialized)\n        Object.keys(rawState).forEach(key => {\n          state[key] = transforms.reduceRight((subState, transformer) => {\n            return transformer.out(subState, key, rawState)\n          }, deserialize(rawState[key]))\n        })\n        return state\n      } catch (err) {\n        if (process.env.NODE_ENV !== 'production' && debug)\n          console.log(\n            `redux-persist/getStoredState: Error restoring data ${serialized}`,\n            err\n          )\n        throw err\n      }\n    }\n  })\n}\n\nfunction defaultDeserialize(serial) {\n  return JSON.parse(serial)\n}\n", "// @flow\n\nimport type { PersistConfig } from './types'\n\nimport { KEY_PREFIX } from './constants'\n\nexport default function purgeStoredState(config: PersistConfig) {\n  const storage = config.storage\n  const storageKey = `${\n    config.keyPrefix !== undefined ? config.keyPrefix : KEY_PREFIX\n  }${config.key}`\n  return storage.removeItem(storageKey, warnIfRemoveError)\n}\n\nfunction warnIfRemoveError(err) {\n  if (err && process.env.NODE_ENV !== 'production') {\n    console.error(\n      'redux-persist/purgeStoredState: Error purging data stored state',\n      err\n    )\n  }\n}\n", "// @flow\nimport {\n  FLUSH,\n  PAUSE,\n  PERSIST,\n  PURGE,\n  REH<PERSON><PERSON><PERSON><PERSON>,\n  DEFAULT_VERSION,\n} from './constants'\n\nimport type {\n  PersistConfig,\n  MigrationManifest,\n  PersistState,\n  Persistoid,\n} from './types'\n\nimport autoMergeLevel1 from './stateReconciler/autoMergeLevel1'\nimport createPersistoid from './createPersistoid'\nimport defaultGetStoredState from './getStoredState'\nimport purgeStoredState from './purgeStoredState'\n\ntype PersistPartial = { _persist: PersistState }\nconst DEFAULT_TIMEOUT = 5000\n/*\n  @TODO add validation / handling for:\n  - persisting a reducer which has nested _persist\n  - handling actions that fire before reydrate is called\n*/\nexport default function persistReducer<State: Object, Action: Object>(\n  config: PersistConfig,\n  baseReducer: (State, Action) => State\n): (State, Action) => State & PersistPartial {\n  if (process.env.NODE_ENV !== 'production') {\n    if (!config) throw new Error('config is required for persistReducer')\n    if (!config.key) throw new Error('key is required in persistor config')\n    if (!config.storage)\n      throw new Error(\n        \"redux-persist: config.storage is required. Try using one of the provided storage engines `import storage from 'redux-persist/lib/storage'`\"\n      )\n  }\n\n  const version =\n    config.version !== undefined ? config.version : DEFAULT_VERSION\n  const debug = config.debug || false\n  const stateReconciler =\n    config.stateReconciler === undefined\n      ? autoMergeLevel1\n      : config.stateReconciler\n  const getStoredState = config.getStoredState || defaultGetStoredState\n  const timeout =\n    config.timeout !== undefined ? config.timeout : DEFAULT_TIMEOUT\n  let _persistoid = null\n  let _purge = false\n  let _paused = true\n  const conditionalUpdate = state => {\n    // update the persistoid only if we are rehydrated and not paused\n    state._persist.rehydrated &&\n      _persistoid &&\n      !_paused &&\n      _persistoid.update(state)\n    return state\n  }\n\n  return (state: State, action: Action) => {\n    let { _persist, ...rest } = state || {}\n    // $FlowIgnore need to update State type\n    let restState: State = rest\n\n    if (action.type === PERSIST) {\n      let _sealed = false\n      let _rehydrate = (payload, err) => {\n        // dev warning if we are already sealed\n        if (process.env.NODE_ENV !== 'production' && _sealed)\n          console.error(\n            `redux-persist: rehydrate for \"${\n              config.key\n            }\" called after timeout.`,\n            payload,\n            err\n          )\n\n        // only rehydrate if we are not already sealed\n        if (!_sealed) {\n          action.rehydrate(config.key, payload, err)\n          _sealed = true\n        }\n      }\n      timeout &&\n        setTimeout(() => {\n          !_sealed &&\n            _rehydrate(\n              undefined,\n              new Error(\n                `redux-persist: persist timed out for persist key \"${\n                  config.key\n                }\"`\n              )\n            )\n        }, timeout)\n\n      // @NOTE PERSIST resumes if paused.\n      _paused = false\n\n      // @NOTE only ever create persistoid once, ensure we call it at least once, even if _persist has already been set\n      if (!_persistoid) _persistoid = createPersistoid(config)\n\n      // @NOTE PERSIST can be called multiple times, noop after the first\n      if (_persist) {\n        // We still need to call the base reducer because there might be nested\n        // uses of persistReducer which need to be aware of the PERSIST action\n        return {\n          ...baseReducer(restState, action),\n          _persist,\n        };\n      }\n\n      if (\n        typeof action.rehydrate !== 'function' ||\n        typeof action.register !== 'function'\n      )\n        throw new Error(\n          'redux-persist: either rehydrate or register is not a function on the PERSIST action. This can happen if the action is being replayed. This is an unexplored use case, please open an issue and we will figure out a resolution.'\n        )\n\n      action.register(config.key)\n\n      getStoredState(config).then(\n        restoredState => {\n          const migrate = config.migrate || ((s, v) => Promise.resolve(s))\n          migrate(restoredState, version).then(\n            migratedState => {\n              _rehydrate(migratedState)\n            },\n            migrateErr => {\n              if (process.env.NODE_ENV !== 'production' && migrateErr)\n                console.error('redux-persist: migration error', migrateErr)\n              _rehydrate(undefined, migrateErr)\n            }\n          )\n        },\n        err => {\n          _rehydrate(undefined, err)\n        }\n      )\n\n      return {\n        ...baseReducer(restState, action),\n        _persist: { version, rehydrated: false },\n      }\n    } else if (action.type === PURGE) {\n      _purge = true\n      action.result(purgeStoredState(config))\n      return {\n        ...baseReducer(restState, action),\n        _persist,\n      }\n    } else if (action.type === FLUSH) {\n      action.result(_persistoid && _persistoid.flush())\n      return {\n        ...baseReducer(restState, action),\n        _persist,\n      }\n    } else if (action.type === PAUSE) {\n      _paused = true\n    } else if (action.type === REHYDRATE) {\n      // noop on restState if purging\n      if (_purge)\n        return {\n          ...restState,\n          _persist: { ..._persist, rehydrated: true },\n        }\n\n      // @NOTE if key does not match, will continue to default else below\n      if (action.key === config.key) {\n        let reducedState = baseReducer(restState, action)\n        let inboundState = action.payload\n        // only reconcile state if stateReconciler and inboundState are both defined\n        let reconciledRest: State =\n          stateReconciler !== false && inboundState !== undefined\n            ? stateReconciler(inboundState, state, reducedState, config)\n            : reducedState\n\n        let newState = {\n          ...reconciledRest,\n          _persist: { ..._persist, rehydrated: true },\n        }\n        return conditionalUpdate(newState)\n      }\n    }\n\n    // if we have not already handled PERSIST, straight passthrough\n    if (!_persist) return baseReducer(state, action)\n\n    // run base reducer:\n    // is state modified ? return original : return updated\n    let newState = baseReducer(restState, action)\n    if (newState === restState) return state\n    return conditionalUpdate({ ...newState, _persist })\n  }\n}\n", "/* global window */\nimport ponyfill from './ponyfill.js';\n\nvar root;\n\nif (typeof self !== 'undefined') {\n  root = self;\n} else if (typeof window !== 'undefined') {\n  root = window;\n} else if (typeof global !== 'undefined') {\n  root = global;\n} else if (typeof module !== 'undefined') {\n  root = module;\n} else {\n  root = Function('return this')();\n}\n\nvar result = ponyfill(root);\nexport default result;\n", "export default function symbolObservablePonyfill(root) {\n\tvar result;\n\tvar Symbol = root.Symbol;\n\n\tif (typeof Symbol === 'function') {\n\t\tif (Symbol.observable) {\n\t\t\tresult = Symbol.observable;\n\t\t} else {\n\t\t\tresult = Symbol('observable');\n\t\t\tSymbol.observable = result;\n\t\t}\n\t} else {\n\t\tresult = '@@observable';\n\t}\n\n\treturn result;\n};\n", "import $$observable from 'symbol-observable';\n\n/**\n * These are private action types reserved by Redux.\n * For any unknown actions, you must return the current state.\n * If the current state is undefined, you must return the initial state.\n * Do not reference these action types directly in your code.\n */\nvar randomString = function randomString() {\n  return Math.random().toString(36).substring(7).split('').join('.');\n};\n\nvar ActionTypes = {\n  INIT: \"@@redux/INIT\" + randomString(),\n  REPLACE: \"@@redux/REPLACE\" + randomString(),\n  PROBE_UNKNOWN_ACTION: function PROBE_UNKNOWN_ACTION() {\n    return \"@@redux/PROBE_UNKNOWN_ACTION\" + randomString();\n  }\n};\n\n/**\n * @param {any} obj The object to inspect.\n * @returns {boolean} True if the argument appears to be a plain object.\n */\nfunction isPlainObject(obj) {\n  if (typeof obj !== 'object' || obj === null) return false;\n  var proto = obj;\n\n  while (Object.getPrototypeOf(proto) !== null) {\n    proto = Object.getPrototypeOf(proto);\n  }\n\n  return Object.getPrototypeOf(obj) === proto;\n}\n\n/**\n * Creates a Redux store that holds the state tree.\n * The only way to change the data in the store is to call `dispatch()` on it.\n *\n * There should only be a single store in your app. To specify how different\n * parts of the state tree respond to actions, you may combine several reducers\n * into a single reducer function by using `combineReducers`.\n *\n * @param {Function} reducer A function that returns the next state tree, given\n * the current state tree and the action to handle.\n *\n * @param {any} [preloadedState] The initial state. You may optionally specify it\n * to hydrate the state from the server in universal apps, or to restore a\n * previously serialized user session.\n * If you use `combineReducers` to produce the root reducer function, this must be\n * an object with the same shape as `combineReducers` keys.\n *\n * @param {Function} [enhancer] The store enhancer. You may optionally specify it\n * to enhance the store with third-party capabilities such as middleware,\n * time travel, persistence, etc. The only store enhancer that ships with Redux\n * is `applyMiddleware()`.\n *\n * @returns {Store} A Redux store that lets you read the state, dispatch actions\n * and subscribe to changes.\n */\n\nfunction createStore(reducer, preloadedState, enhancer) {\n  var _ref2;\n\n  if (typeof preloadedState === 'function' && typeof enhancer === 'function' || typeof enhancer === 'function' && typeof arguments[3] === 'function') {\n    throw new Error('It looks like you are passing several store enhancers to ' + 'createStore(). This is not supported. Instead, compose them ' + 'together to a single function.');\n  }\n\n  if (typeof preloadedState === 'function' && typeof enhancer === 'undefined') {\n    enhancer = preloadedState;\n    preloadedState = undefined;\n  }\n\n  if (typeof enhancer !== 'undefined') {\n    if (typeof enhancer !== 'function') {\n      throw new Error('Expected the enhancer to be a function.');\n    }\n\n    return enhancer(createStore)(reducer, preloadedState);\n  }\n\n  if (typeof reducer !== 'function') {\n    throw new Error('Expected the reducer to be a function.');\n  }\n\n  var currentReducer = reducer;\n  var currentState = preloadedState;\n  var currentListeners = [];\n  var nextListeners = currentListeners;\n  var isDispatching = false;\n  /**\n   * This makes a shallow copy of currentListeners so we can use\n   * nextListeners as a temporary list while dispatching.\n   *\n   * This prevents any bugs around consumers calling\n   * subscribe/unsubscribe in the middle of a dispatch.\n   */\n\n  function ensureCanMutateNextListeners() {\n    if (nextListeners === currentListeners) {\n      nextListeners = currentListeners.slice();\n    }\n  }\n  /**\n   * Reads the state tree managed by the store.\n   *\n   * @returns {any} The current state tree of your application.\n   */\n\n\n  function getState() {\n    if (isDispatching) {\n      throw new Error('You may not call store.getState() while the reducer is executing. ' + 'The reducer has already received the state as an argument. ' + 'Pass it down from the top reducer instead of reading it from the store.');\n    }\n\n    return currentState;\n  }\n  /**\n   * Adds a change listener. It will be called any time an action is dispatched,\n   * and some part of the state tree may potentially have changed. You may then\n   * call `getState()` to read the current state tree inside the callback.\n   *\n   * You may call `dispatch()` from a change listener, with the following\n   * caveats:\n   *\n   * 1. The subscriptions are snapshotted just before every `dispatch()` call.\n   * If you subscribe or unsubscribe while the listeners are being invoked, this\n   * will not have any effect on the `dispatch()` that is currently in progress.\n   * However, the next `dispatch()` call, whether nested or not, will use a more\n   * recent snapshot of the subscription list.\n   *\n   * 2. The listener should not expect to see all state changes, as the state\n   * might have been updated multiple times during a nested `dispatch()` before\n   * the listener is called. It is, however, guaranteed that all subscribers\n   * registered before the `dispatch()` started will be called with the latest\n   * state by the time it exits.\n   *\n   * @param {Function} listener A callback to be invoked on every dispatch.\n   * @returns {Function} A function to remove this change listener.\n   */\n\n\n  function subscribe(listener) {\n    if (typeof listener !== 'function') {\n      throw new Error('Expected the listener to be a function.');\n    }\n\n    if (isDispatching) {\n      throw new Error('You may not call store.subscribe() while the reducer is executing. ' + 'If you would like to be notified after the store has been updated, subscribe from a ' + 'component and invoke store.getState() in the callback to access the latest state. ' + 'See https://redux.js.org/api-reference/store#subscribe(listener) for more details.');\n    }\n\n    var isSubscribed = true;\n    ensureCanMutateNextListeners();\n    nextListeners.push(listener);\n    return function unsubscribe() {\n      if (!isSubscribed) {\n        return;\n      }\n\n      if (isDispatching) {\n        throw new Error('You may not unsubscribe from a store listener while the reducer is executing. ' + 'See https://redux.js.org/api-reference/store#subscribe(listener) for more details.');\n      }\n\n      isSubscribed = false;\n      ensureCanMutateNextListeners();\n      var index = nextListeners.indexOf(listener);\n      nextListeners.splice(index, 1);\n    };\n  }\n  /**\n   * Dispatches an action. It is the only way to trigger a state change.\n   *\n   * The `reducer` function, used to create the store, will be called with the\n   * current state tree and the given `action`. Its return value will\n   * be considered the **next** state of the tree, and the change listeners\n   * will be notified.\n   *\n   * The base implementation only supports plain object actions. If you want to\n   * dispatch a Promise, an Observable, a thunk, or something else, you need to\n   * wrap your store creating function into the corresponding middleware. For\n   * example, see the documentation for the `redux-thunk` package. Even the\n   * middleware will eventually dispatch plain object actions using this method.\n   *\n   * @param {Object} action A plain object representing “what changed”. It is\n   * a good idea to keep actions serializable so you can record and replay user\n   * sessions, or use the time travelling `redux-devtools`. An action must have\n   * a `type` property which may not be `undefined`. It is a good idea to use\n   * string constants for action types.\n   *\n   * @returns {Object} For convenience, the same action object you dispatched.\n   *\n   * Note that, if you use a custom middleware, it may wrap `dispatch()` to\n   * return something else (for example, a Promise you can await).\n   */\n\n\n  function dispatch(action) {\n    if (!isPlainObject(action)) {\n      throw new Error('Actions must be plain objects. ' + 'Use custom middleware for async actions.');\n    }\n\n    if (typeof action.type === 'undefined') {\n      throw new Error('Actions may not have an undefined \"type\" property. ' + 'Have you misspelled a constant?');\n    }\n\n    if (isDispatching) {\n      throw new Error('Reducers may not dispatch actions.');\n    }\n\n    try {\n      isDispatching = true;\n      currentState = currentReducer(currentState, action);\n    } finally {\n      isDispatching = false;\n    }\n\n    var listeners = currentListeners = nextListeners;\n\n    for (var i = 0; i < listeners.length; i++) {\n      var listener = listeners[i];\n      listener();\n    }\n\n    return action;\n  }\n  /**\n   * Replaces the reducer currently used by the store to calculate the state.\n   *\n   * You might need this if your app implements code splitting and you want to\n   * load some of the reducers dynamically. You might also need this if you\n   * implement a hot reloading mechanism for Redux.\n   *\n   * @param {Function} nextReducer The reducer for the store to use instead.\n   * @returns {void}\n   */\n\n\n  function replaceReducer(nextReducer) {\n    if (typeof nextReducer !== 'function') {\n      throw new Error('Expected the nextReducer to be a function.');\n    }\n\n    currentReducer = nextReducer; // This action has a similiar effect to ActionTypes.INIT.\n    // Any reducers that existed in both the new and old rootReducer\n    // will receive the previous state. This effectively populates\n    // the new state tree with any relevant data from the old one.\n\n    dispatch({\n      type: ActionTypes.REPLACE\n    });\n  }\n  /**\n   * Interoperability point for observable/reactive libraries.\n   * @returns {observable} A minimal observable of state changes.\n   * For more information, see the observable proposal:\n   * https://github.com/tc39/proposal-observable\n   */\n\n\n  function observable() {\n    var _ref;\n\n    var outerSubscribe = subscribe;\n    return _ref = {\n      /**\n       * The minimal observable subscription method.\n       * @param {Object} observer Any object that can be used as an observer.\n       * The observer object should have a `next` method.\n       * @returns {subscription} An object with an `unsubscribe` method that can\n       * be used to unsubscribe the observable from the store, and prevent further\n       * emission of values from the observable.\n       */\n      subscribe: function subscribe(observer) {\n        if (typeof observer !== 'object' || observer === null) {\n          throw new TypeError('Expected the observer to be an object.');\n        }\n\n        function observeState() {\n          if (observer.next) {\n            observer.next(getState());\n          }\n        }\n\n        observeState();\n        var unsubscribe = outerSubscribe(observeState);\n        return {\n          unsubscribe: unsubscribe\n        };\n      }\n    }, _ref[$$observable] = function () {\n      return this;\n    }, _ref;\n  } // When a store is created, an \"INIT\" action is dispatched so that every\n  // reducer returns their initial state. This effectively populates\n  // the initial state tree.\n\n\n  dispatch({\n    type: ActionTypes.INIT\n  });\n  return _ref2 = {\n    dispatch: dispatch,\n    subscribe: subscribe,\n    getState: getState,\n    replaceReducer: replaceReducer\n  }, _ref2[$$observable] = observable, _ref2;\n}\n\n/**\n * Prints a warning in the console if it exists.\n *\n * @param {String} message The warning message.\n * @returns {void}\n */\nfunction warning(message) {\n  /* eslint-disable no-console */\n  if (typeof console !== 'undefined' && typeof console.error === 'function') {\n    console.error(message);\n  }\n  /* eslint-enable no-console */\n\n\n  try {\n    // This error was thrown as a convenience so that if you enable\n    // \"break on all exceptions\" in your console,\n    // it would pause the execution at this line.\n    throw new Error(message);\n  } catch (e) {} // eslint-disable-line no-empty\n\n}\n\nfunction getUndefinedStateErrorMessage(key, action) {\n  var actionType = action && action.type;\n  var actionDescription = actionType && \"action \\\"\" + String(actionType) + \"\\\"\" || 'an action';\n  return \"Given \" + actionDescription + \", reducer \\\"\" + key + \"\\\" returned undefined. \" + \"To ignore an action, you must explicitly return the previous state. \" + \"If you want this reducer to hold no value, you can return null instead of undefined.\";\n}\n\nfunction getUnexpectedStateShapeWarningMessage(inputState, reducers, action, unexpectedKeyCache) {\n  var reducerKeys = Object.keys(reducers);\n  var argumentName = action && action.type === ActionTypes.INIT ? 'preloadedState argument passed to createStore' : 'previous state received by the reducer';\n\n  if (reducerKeys.length === 0) {\n    return 'Store does not have a valid reducer. Make sure the argument passed ' + 'to combineReducers is an object whose values are reducers.';\n  }\n\n  if (!isPlainObject(inputState)) {\n    return \"The \" + argumentName + \" has unexpected type of \\\"\" + {}.toString.call(inputState).match(/\\s([a-z|A-Z]+)/)[1] + \"\\\". Expected argument to be an object with the following \" + (\"keys: \\\"\" + reducerKeys.join('\", \"') + \"\\\"\");\n  }\n\n  var unexpectedKeys = Object.keys(inputState).filter(function (key) {\n    return !reducers.hasOwnProperty(key) && !unexpectedKeyCache[key];\n  });\n  unexpectedKeys.forEach(function (key) {\n    unexpectedKeyCache[key] = true;\n  });\n  if (action && action.type === ActionTypes.REPLACE) return;\n\n  if (unexpectedKeys.length > 0) {\n    return \"Unexpected \" + (unexpectedKeys.length > 1 ? 'keys' : 'key') + \" \" + (\"\\\"\" + unexpectedKeys.join('\", \"') + \"\\\" found in \" + argumentName + \". \") + \"Expected to find one of the known reducer keys instead: \" + (\"\\\"\" + reducerKeys.join('\", \"') + \"\\\". Unexpected keys will be ignored.\");\n  }\n}\n\nfunction assertReducerShape(reducers) {\n  Object.keys(reducers).forEach(function (key) {\n    var reducer = reducers[key];\n    var initialState = reducer(undefined, {\n      type: ActionTypes.INIT\n    });\n\n    if (typeof initialState === 'undefined') {\n      throw new Error(\"Reducer \\\"\" + key + \"\\\" returned undefined during initialization. \" + \"If the state passed to the reducer is undefined, you must \" + \"explicitly return the initial state. The initial state may \" + \"not be undefined. If you don't want to set a value for this reducer, \" + \"you can use null instead of undefined.\");\n    }\n\n    if (typeof reducer(undefined, {\n      type: ActionTypes.PROBE_UNKNOWN_ACTION()\n    }) === 'undefined') {\n      throw new Error(\"Reducer \\\"\" + key + \"\\\" returned undefined when probed with a random type. \" + (\"Don't try to handle \" + ActionTypes.INIT + \" or other actions in \\\"redux/*\\\" \") + \"namespace. They are considered private. Instead, you must return the \" + \"current state for any unknown actions, unless it is undefined, \" + \"in which case you must return the initial state, regardless of the \" + \"action type. The initial state may not be undefined, but can be null.\");\n    }\n  });\n}\n/**\n * Turns an object whose values are different reducer functions, into a single\n * reducer function. It will call every child reducer, and gather their results\n * into a single state object, whose keys correspond to the keys of the passed\n * reducer functions.\n *\n * @param {Object} reducers An object whose values correspond to different\n * reducer functions that need to be combined into one. One handy way to obtain\n * it is to use ES6 `import * as reducers` syntax. The reducers may never return\n * undefined for any action. Instead, they should return their initial state\n * if the state passed to them was undefined, and the current state for any\n * unrecognized action.\n *\n * @returns {Function} A reducer function that invokes every reducer inside the\n * passed object, and builds a state object with the same shape.\n */\n\n\nfunction combineReducers(reducers) {\n  var reducerKeys = Object.keys(reducers);\n  var finalReducers = {};\n\n  for (var i = 0; i < reducerKeys.length; i++) {\n    var key = reducerKeys[i];\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (typeof reducers[key] === 'undefined') {\n        warning(\"No reducer provided for key \\\"\" + key + \"\\\"\");\n      }\n    }\n\n    if (typeof reducers[key] === 'function') {\n      finalReducers[key] = reducers[key];\n    }\n  }\n\n  var finalReducerKeys = Object.keys(finalReducers); // This is used to make sure we don't warn about the same\n  // keys multiple times.\n\n  var unexpectedKeyCache;\n\n  if (process.env.NODE_ENV !== 'production') {\n    unexpectedKeyCache = {};\n  }\n\n  var shapeAssertionError;\n\n  try {\n    assertReducerShape(finalReducers);\n  } catch (e) {\n    shapeAssertionError = e;\n  }\n\n  return function combination(state, action) {\n    if (state === void 0) {\n      state = {};\n    }\n\n    if (shapeAssertionError) {\n      throw shapeAssertionError;\n    }\n\n    if (process.env.NODE_ENV !== 'production') {\n      var warningMessage = getUnexpectedStateShapeWarningMessage(state, finalReducers, action, unexpectedKeyCache);\n\n      if (warningMessage) {\n        warning(warningMessage);\n      }\n    }\n\n    var hasChanged = false;\n    var nextState = {};\n\n    for (var _i = 0; _i < finalReducerKeys.length; _i++) {\n      var _key = finalReducerKeys[_i];\n      var reducer = finalReducers[_key];\n      var previousStateForKey = state[_key];\n      var nextStateForKey = reducer(previousStateForKey, action);\n\n      if (typeof nextStateForKey === 'undefined') {\n        var errorMessage = getUndefinedStateErrorMessage(_key, action);\n        throw new Error(errorMessage);\n      }\n\n      nextState[_key] = nextStateForKey;\n      hasChanged = hasChanged || nextStateForKey !== previousStateForKey;\n    }\n\n    return hasChanged ? nextState : state;\n  };\n}\n\nfunction bindActionCreator(actionCreator, dispatch) {\n  return function () {\n    return dispatch(actionCreator.apply(this, arguments));\n  };\n}\n/**\n * Turns an object whose values are action creators, into an object with the\n * same keys, but with every function wrapped into a `dispatch` call so they\n * may be invoked directly. This is just a convenience method, as you can call\n * `store.dispatch(MyActionCreators.doSomething())` yourself just fine.\n *\n * For convenience, you can also pass an action creator as the first argument,\n * and get a dispatch wrapped function in return.\n *\n * @param {Function|Object} actionCreators An object whose values are action\n * creator functions. One handy way to obtain it is to use ES6 `import * as`\n * syntax. You may also pass a single function.\n *\n * @param {Function} dispatch The `dispatch` function available on your Redux\n * store.\n *\n * @returns {Function|Object} The object mimicking the original object, but with\n * every action creator wrapped into the `dispatch` call. If you passed a\n * function as `actionCreators`, the return value will also be a single\n * function.\n */\n\n\nfunction bindActionCreators(actionCreators, dispatch) {\n  if (typeof actionCreators === 'function') {\n    return bindActionCreator(actionCreators, dispatch);\n  }\n\n  if (typeof actionCreators !== 'object' || actionCreators === null) {\n    throw new Error(\"bindActionCreators expected an object or a function, instead received \" + (actionCreators === null ? 'null' : typeof actionCreators) + \". \" + \"Did you write \\\"import ActionCreators from\\\" instead of \\\"import * as ActionCreators from\\\"?\");\n  }\n\n  var boundActionCreators = {};\n\n  for (var key in actionCreators) {\n    var actionCreator = actionCreators[key];\n\n    if (typeof actionCreator === 'function') {\n      boundActionCreators[key] = bindActionCreator(actionCreator, dispatch);\n    }\n  }\n\n  return boundActionCreators;\n}\n\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}\n\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n\n  if (Object.getOwnPropertySymbols) {\n    keys.push.apply(keys, Object.getOwnPropertySymbols(object));\n  }\n\n  if (enumerableOnly) keys = keys.filter(function (sym) {\n    return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n  });\n  return keys;\n}\n\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n\n    if (i % 2) {\n      ownKeys(source, true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(source).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n\n  return target;\n}\n\n/**\n * Composes single-argument functions from right to left. The rightmost\n * function can take multiple arguments as it provides the signature for\n * the resulting composite function.\n *\n * @param {...Function} funcs The functions to compose.\n * @returns {Function} A function obtained by composing the argument functions\n * from right to left. For example, compose(f, g, h) is identical to doing\n * (...args) => f(g(h(...args))).\n */\nfunction compose() {\n  for (var _len = arguments.length, funcs = new Array(_len), _key = 0; _key < _len; _key++) {\n    funcs[_key] = arguments[_key];\n  }\n\n  if (funcs.length === 0) {\n    return function (arg) {\n      return arg;\n    };\n  }\n\n  if (funcs.length === 1) {\n    return funcs[0];\n  }\n\n  return funcs.reduce(function (a, b) {\n    return function () {\n      return a(b.apply(void 0, arguments));\n    };\n  });\n}\n\n/**\n * Creates a store enhancer that applies middleware to the dispatch method\n * of the Redux store. This is handy for a variety of tasks, such as expressing\n * asynchronous actions in a concise manner, or logging every action payload.\n *\n * See `redux-thunk` package as an example of the Redux middleware.\n *\n * Because middleware is potentially asynchronous, this should be the first\n * store enhancer in the composition chain.\n *\n * Note that each middleware will be given the `dispatch` and `getState` functions\n * as named arguments.\n *\n * @param {...Function} middlewares The middleware chain to be applied.\n * @returns {Function} A store enhancer applying the middleware.\n */\n\nfunction applyMiddleware() {\n  for (var _len = arguments.length, middlewares = new Array(_len), _key = 0; _key < _len; _key++) {\n    middlewares[_key] = arguments[_key];\n  }\n\n  return function (createStore) {\n    return function () {\n      var store = createStore.apply(void 0, arguments);\n\n      var _dispatch = function dispatch() {\n        throw new Error('Dispatching while constructing your middleware is not allowed. ' + 'Other middleware would not be applied to this dispatch.');\n      };\n\n      var middlewareAPI = {\n        getState: store.getState,\n        dispatch: function dispatch() {\n          return _dispatch.apply(void 0, arguments);\n        }\n      };\n      var chain = middlewares.map(function (middleware) {\n        return middleware(middlewareAPI);\n      });\n      _dispatch = compose.apply(void 0, chain)(store.dispatch);\n      return _objectSpread2({}, store, {\n        dispatch: _dispatch\n      });\n    };\n  };\n}\n\n/*\n * This is a dummy function to check if the function name has been altered by minification.\n * If the function has been minified and NODE_ENV !== 'production', warn the user.\n */\n\nfunction isCrushed() {}\n\nif (process.env.NODE_ENV !== 'production' && typeof isCrushed.name === 'string' && isCrushed.name !== 'isCrushed') {\n  warning('You are currently using minified code outside of NODE_ENV === \"production\". ' + 'This means that you are running a slower development build of Redux. ' + 'You can use loose-envify (https://github.com/zertosh/loose-envify) for browserify ' + 'or setting mode to production in webpack (https://webpack.js.org/concepts/mode/) ' + 'to ensure you have the correct code for your production build.');\n}\n\nexport { ActionTypes as __DO_NOT_USE__ActionTypes, applyMiddleware, bindActionCreators, combineReducers, compose, createStore };\n", "// @flow\n\n/*\n  autoMergeLevel2: \n    - merges 2 level of substate\n    - skips substate if already modified\n    - this is essentially redux-perist v4 behavior\n*/\n\nimport type { PersistConfig } from '../types'\n\nexport default function autoMergeLevel2<State: Object>(\n  inboundState: State,\n  originalState: State,\n  reducedState: State,\n  { debug }: PersistConfig\n): State {\n  let newState = { ...reducedState }\n  // only rehydrate if inboundState exists and is an object\n  if (inboundState && typeof inboundState === 'object') {\n    Object.keys(inboundState).forEach(key => {\n      // ignore _persist data\n      if (key === '_persist') return\n      // if reducer modifies substate, skip auto rehydration\n      if (originalState[key] !== reducedState[key]) {\n        if (process.env.NODE_ENV !== 'production' && debug)\n          console.log(\n            'redux-persist/stateReconciler: sub state for key `%s` modified, skipping.',\n            key\n          )\n        return\n      }\n      if (isPlainEnoughObject(reducedState[key])) {\n        // if object is plain enough shallow merge the new values (hence \"Level2\")\n        newState[key] = { ...newState[key], ...inboundState[key] }\n        return\n      }\n      // otherwise hard set\n      newState[key] = inboundState[key]\n    })\n  }\n\n  if (\n    process.env.NODE_ENV !== 'production' &&\n    debug &&\n    inboundState &&\n    typeof inboundState === 'object'\n  )\n    console.log(\n      `redux-persist/stateReconciler: rehydrated keys '${Object.keys(\n        inboundState\n      ).join(', ')}'`\n    )\n\n  return newState\n}\n\nfunction isPlainEnoughObject(o) {\n  return o !== null && !Array.isArray(o) && typeof o === 'object'\n}\n", "// @flow\n\nimport type {\n  Persistor,\n  PersistConfig,\n  PersistorOptions,\n  PersistorState,\n  MigrationManifest,\n  RehydrateAction,\n  RehydrateErrorType,\n} from './types'\n\nimport { createStore } from 'redux'\nimport { FLUSH, PAUSE, PERSIST, PURGE, REGISTER, REHYDRATE } from './constants'\n\ntype PendingRehydrate = [Object, RehydrateErrorType, PersistConfig]\ntype Persist = <R>(PersistConfig, MigrationManifest) => R => R\ntype CreatePersistor = Object => void\ntype BoostrappedCb = () => any\n\nconst initialState: PersistorState = {\n  registry: [],\n  bootstrapped: false,\n}\n\nconst persistorReducer = (state = initialState, action) => {\n  switch (action.type) {\n    case REGISTER:\n      return { ...state, registry: [...state.registry, action.key] }\n    case REHYDRATE:\n      let firstIndex = state.registry.indexOf(action.key)\n      let registry = [...state.registry]\n      registry.splice(firstIndex, 1)\n      return { ...state, registry, bootstrapped: registry.length === 0 }\n    default:\n      return state\n  }\n}\n\nexport default function persistStore(\n  store: Object,\n  options?: ?PersistorOptions,\n  cb?: BoostrappedCb\n): Persistor {\n  // help catch incorrect usage of passing PersistConfig in as PersistorOptions\n  if (process.env.NODE_ENV !== 'production') {\n    let optionsToTest: Object = options || {}\n    let bannedKeys = [\n      'blacklist',\n      'whitelist',\n      'transforms',\n      'storage',\n      'keyPrefix',\n      'migrate',\n    ]\n    bannedKeys.forEach(k => {\n      if (!!optionsToTest[k])\n        console.error(\n          `redux-persist: invalid option passed to persistStore: \"${k}\". You may be incorrectly passing persistConfig into persistStore, whereas it should be passed into persistReducer.`\n        )\n    })\n  }\n  let boostrappedCb = cb || false\n\n  let _pStore = createStore(\n    persistorReducer,\n    initialState,\n    options && options.enhancer ? options.enhancer : undefined\n  )\n  let register = (key: string) => {\n    _pStore.dispatch({\n      type: REGISTER,\n      key,\n    })\n  }\n\n  let rehydrate = (key: string, payload: Object, err: any) => {\n    let rehydrateAction = {\n      type: REHYDRATE,\n      payload,\n      err,\n      key,\n    }\n    // dispatch to `store` to rehydrate and `persistor` to track result\n    store.dispatch(rehydrateAction)\n    _pStore.dispatch(rehydrateAction)\n    if (boostrappedCb && persistor.getState().bootstrapped) {\n      boostrappedCb()\n      boostrappedCb = false\n    }\n  }\n\n  let persistor: Persistor = {\n    ..._pStore,\n    purge: () => {\n      let results = []\n      store.dispatch({\n        type: PURGE,\n        result: purgeResult => {\n          results.push(purgeResult)\n        },\n      })\n      return Promise.all(results)\n    },\n    flush: () => {\n      let results = []\n      store.dispatch({\n        type: FLUSH,\n        result: flushResult => {\n          results.push(flushResult)\n        },\n      })\n      return Promise.all(results)\n    },\n    pause: () => {\n      store.dispatch({\n        type: PAUSE,\n      })\n    },\n    persist: () => {\n      store.dispatch({ type: PERSIST, register, rehydrate })\n    },\n  }\n\n  if (!(options && options.manualPersist)){\n    persistor.persist()\n  }\n\n  return persistor\n}\n", "// @flow\n\nimport { combineReducers } from 'redux'\nimport persistReducer from './persistReducer'\nimport autoMergeLevel2 from './stateReconciler/autoMergeLevel2'\n\nimport type { PersistConfig } from './types'\n\ntype Reducers = {\n  [key: string]: Function,\n}\n\ntype Reducer = (state: Object, action: Object) => Object\n\n// combineReducers + persistReducer with stateReconciler defaulted to autoMergeLevel2\nexport default function persistCombineReducers(\n  config: PersistConfig,\n  reducers: Reducers\n): Reducer {\n  config.stateReconciler =\n    config.stateReconciler === undefined\n      ? autoMergeLevel2\n      : config.stateReconciler\n  return persistReducer(config, combineReducers(reducers))\n}\n", "// @flow\n\nimport { DEFAULT_VERSION } from './constants'\n\nimport type { PersistedState, MigrationManifest } from './types'\n\nexport default function createMigrate(\n  migrations: MigrationManifest,\n  config?: { debug: boolean }\n) {\n  let { debug } = config || {}\n  return function(\n    state: PersistedState,\n    currentVersion: number\n  ): Promise<PersistedState> {\n    if (!state) {\n      if (process.env.NODE_ENV !== 'production' && debug)\n        console.log('redux-persist: no inbound state, skipping migration')\n      return Promise.resolve(undefined)\n    }\n\n    let inboundVersion: number =\n      state._persist && state._persist.version !== undefined\n        ? state._persist.version\n        : DEFAULT_VERSION\n    if (inboundVersion === currentVersion) {\n      if (process.env.NODE_ENV !== 'production' && debug)\n        console.log('redux-persist: versions match, noop migration')\n      return Promise.resolve(state)\n    }\n    if (inboundVersion > currentVersion) {\n      if (process.env.NODE_ENV !== 'production')\n        console.error('redux-persist: downgrading version is not supported')\n      return Promise.resolve(state)\n    }\n\n    let migrationKeys = Object.keys(migrations)\n      .map(ver => parseInt(ver))\n      .filter(key => currentVersion >= key && key > inboundVersion)\n      .sort((a, b) => a - b)\n\n    if (process.env.NODE_ENV !== 'production' && debug)\n      console.log('redux-persist: migrationKeys', migrationKeys)\n    try {\n      let migratedState = migrationKeys.reduce((state, versionKey) => {\n        if (process.env.NODE_ENV !== 'production' && debug)\n          console.log(\n            'redux-persist: running migration for versionKey',\n            versionKey\n          )\n        return migrations[versionKey](state)\n      }, state)\n      return Promise.resolve(migratedState)\n    } catch (err) {\n      return Promise.reject(err)\n    }\n  }\n}\n", "// @flow\n\ntype TransformConfig = {\n  whitelist?: Array<string>,\n  blacklist?: Array<string>,\n}\n\nexport default function createTransform(\n  // @NOTE inbound: transform state coming from redux on its way to being serialized and stored\n  inbound: ?Function,\n  // @NOTE outbound: transform state coming from storage, on its way to be rehydrated into redux\n  outbound: ?Function,\n  config: TransformConfig = {}\n) {\n  let whitelist = config.whitelist || null\n  let blacklist = config.blacklist || null\n\n  function whitelistBlacklistCheck(key) {\n    if (whitelist && whitelist.indexOf(key) === -1) return true\n    if (blacklist && blacklist.indexOf(key) !== -1) return true\n    return false\n  }\n\n  return {\n    in: (state: Object, key: string, fullState: Object) =>\n      !whitelistBlacklistCheck(key) && inbound\n        ? inbound(state, key, fullState)\n        : state,\n    out: (state: Object, key: string, fullState: Object) =>\n      !whitelistBlacklistCheck(key) && outbound\n        ? outbound(state, key, fullState)\n        : state,\n  }\n}\n"], "names": ["KEY_PREFIX", "FLUSH", "REHYDRATE", "PAUSE", "PERSIST", "PURGE", "REGISTER", "DEFAULT_VERSION", "autoMergeLevel1", "inboundState", "originalState", "reducedState", "newState", "_typeof", "Object", "keys", "for<PERSON>ach", "key", "createPersistoid", "config", "serialize", "blacklist", "whitelist", "transforms", "throttle", "storageKey", "undefined", "keyPrefix", "storage", "x", "defaultSerialize", "writeFailHandler", "lastState", "stagedState", "keysToProcess", "timeIterator", "writePromise", "processNextKey", "length", "clearInterval", "shift", "endState", "reduce", "subState", "transformer", "in", "err", "console", "error", "setItem", "catch", "onWriteFail", "<PERSON><PERSON><PERSON><PERSON><PERSON>lacklist", "indexOf", "update", "state", "push", "setInterval", "flush", "Promise", "resolve", "data", "JSON", "stringify", "getStoredState", "deserialize", "defaultDeserialize", "getItem", "then", "serialized", "rawState", "reduceRight", "out", "serial", "parse", "purgeStoredState", "removeItem", "warnIfRemoveError", "DEFAULT_TIMEOUT", "persistReducer", "baseReducer", "version", "stateReconciler", "defaultGetStoredState", "timeout", "_persistoid", "_purge", "_paused", "conditionalUpdate", "_persist", "rehydrated", "action", "restState", "type", "_sealed", "_rehydrate", "payload", "rehydrate", "setTimeout", "Error", "register", "restoredState", "migrate", "s", "v", "migratedState", "migrateErr", "result", "root", "Symbol", "observable", "ponyfill", "self", "window", "global", "module", "Function", "randomString", "Math", "random", "toString", "substring", "split", "join", "ActionTypes", "INIT", "REPLACE", "PROBE_UNKNOWN_ACTION", "createStore", "reducer", "preloadedState", "enhancer", "_ref2", "arguments", "currentReducer", "currentState", "currentListeners", "nextListeners", "isDispatching", "ensureCanMutateNextListeners", "slice", "getState", "subscribe", "listener", "isSubscribed", "index", "splice", "dispatch", "obj", "proto", "getPrototypeOf", "isPlainObject", "listeners", "i", "replaceReducer", "nextReducer", "$$observable", "_ref", "outerSubscribe", "observer", "TypeError", "observeState", "next", "unsubscribe", "this", "getUndefinedStateErrorMessage", "actionType", "combineReducers", "reducers", "reducerKeys", "finalReducers", "shapeAssertionError", "final<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "assertReducerShape", "e", "has<PERSON><PERSON>ed", "nextState", "_i", "_key", "previousStateForKey", "nextStateForKey", "errorMessage", "autoMergeLevel2", "o", "Array", "isArray", "initialState", "registry", "bootstrapped", "persistorReducer", "firstIndex", "store", "options", "cb", "boostrappedCb", "_pStore", "rehydrateAction", "persistor", "purge", "results", "purgeResult", "all", "flushResult", "pause", "persist", "manualPersist", "migrations", "currentVersion", "inboundVersion", "migrationKeys", "map", "ver", "parseInt", "filter", "sort", "a", "b", "version<PERSON>ey", "reject", "inbound", "outbound", "whitelistBlacklist<PERSON>heck", "fullState"], "mappings": "owDAEO,IAAMA,EAAa,WACbC,EAAQ,gBACRC,EAAY,oBACZC,EAAQ,gBACRC,EAAU,kBACVC,EAAQ,gBACRC,EAAW,mBACXC,GAAmB,ECChC,SAAwBC,EACtBC,EACAC,EACAC,SAGIC,OAAgBD,UAEhBF,GAAwC,WAAxBI,EAAOJ,IACzBK,OAAOC,KAAKN,GAAcO,QAAQ,SAAAC,GAEpB,aAARA,GAEAP,EAAcO,KAASN,EAAaM,KASxCL,EAASK,GAAOR,EAAaQ,MAgB1BL,ECxCT,SAAwBM,EAAiBC,OAUnCC,EAREC,EAA4BF,EAAOE,WAAa,KAChDC,EAA4BH,EAAOG,WAAa,KAChDC,EAAaJ,EAAOI,eACpBC,EAAWL,EAAOK,UAAY,EAC9BC,iBACiBC,IAArBP,EAAOQ,UAA0BR,EAAOQ,UAAY3B,UACnDmB,EAAOF,KACJW,EAAUT,EAAOS,QAGrBR,GADuB,IAArBD,EAAOC,UACG,SAAAS,UAAKA,GACoB,mBAArBV,EAAOC,UACXD,EAAOC,UAEPU,MAERC,EAAmBZ,EAAOY,kBAAoB,KAGhDC,KACAC,KACAC,KACAC,EAA4B,KAC5BC,EAAe,cAgCVC,OACsB,IAAzBH,EAAcI,cACZH,GAAcI,cAAcJ,QAChCA,EAAe,UAIblB,EAAMiB,EAAcM,QACpBC,EAAWlB,EAAWmB,OAAO,SAACC,EAAUC,UACnCA,EAAYC,GAAGF,EAAU1B,EAAKe,IACpCA,EAAUf,YAEIS,IAAbe,MAEAR,EAAYhB,GAAOG,EAAUqB,GAC7B,MAAOK,GACPC,QAAQC,MACN,0DACAF,eAKGb,EAAYhB,GAGQ,IAAzBiB,EAAcI,SAOlBxB,OAAOC,KAAKkB,GAAajB,QAAQ,SAAAC,QACRS,IAAnBM,EAAUf,WACLgB,EAAYhB,KAIvBmB,EAAeR,EACZqB,QAAQxB,EAAYL,EAAUa,IAC9BiB,MAAMC,aAGFC,EAAuBnC,WAC1BK,IAAyC,IAA5BA,EAAU+B,QAAQpC,IAAuB,aAARA,MAE9CI,IAAyC,IAA5BA,EAAUgC,QAAQpC,aAI5BkC,EAAYL,GAEff,GAAkBA,EAAiBe,UAevCQ,OAlGa,SAACC,GAEdzC,OAAOC,KAAKwC,GAAOvC,QAAQ,SAAAC,GACpBmC,EAAuBnC,IACxBe,EAAUf,KAASsC,EAAMtC,KACO,IAAhCiB,EAAcmB,QAAQpC,IAC1BiB,EAAcsB,KAAKvC,KAKrBH,OAAOC,KAAKiB,GAAWhB,QAAQ,SAAAC,QAEZS,IAAf6B,EAAMtC,IACNmC,EAAuBnC,KACS,IAAhCiB,EAAcmB,QAAQpC,SACHS,IAAnBM,EAAUf,IAEViB,EAAcsB,KAAKvC,KAKF,OAAjBkB,IACFA,EAAesB,YAAYpB,EAAgBb,IAG7CQ,EAAYuB,GAwEZG,MAVY,gBACoB,IAAzBxB,EAAcI,QACnBD,WAEKD,GAAgBuB,QAAQC,YAWnC,SAAS9B,EAAiB+B,UACjBC,KAAKC,UAAUF,YCtIAG,EACtB7C,OAQI8C,EANE1C,EAAaJ,EAAOI,eACpBE,iBACiBC,IAArBP,EAAOQ,UAA0BR,EAAOQ,UAAY3B,UACnDmB,EAAOF,YAKRgD,GADyB,IAAvB9C,EAAO8C,YACK,SAAApC,UAAKA,GACoB,mBAAvBV,EAAO8C,YACT9C,EAAO8C,YAEPC,EARA/C,EAAOS,QAURuC,QAAQ1C,GAAY2C,KAAK,SAAAC,MACjCA,UAGGd,KACAe,EAAWL,EAAYI,UAC3BvD,OAAOC,KAAKuD,GAAUtD,QAAQ,SAAAC,GAC5BsC,EAAMtC,GAAOM,EAAWgD,YAAY,SAAC5B,EAAUC,UACtCA,EAAY4B,IAAI7B,EAAU1B,EAAKqD,IACrCL,EAAYK,EAASrD,OAEnBsC,EACP,MAAOT,SAMDA,KAMd,SAASoB,EAAmBO,UACnBX,KAAKY,MAAMD,YC1CIE,EAAiBxD,OACjCS,EAAUT,EAAOS,QACjBH,iBACiBC,IAArBP,EAAOQ,UAA0BR,EAAOQ,UAAY3B,UACnDmB,EAAOF,YACHW,EAAQgD,WAAWnD,EAAYoD,GAGxC,SAASA,EAAkB/B,GACrBA,MCQAgC,EAAkB,IAMxB,SAAwBC,EACtB5D,EACA6D,OAWMC,OACevD,IAAnBP,EAAO8D,QAAwB9D,EAAO8D,QAAU1E,EAE5C2E,OACuBxD,IAA3BP,EAAO+D,gBACH1E,EACAW,EAAO+D,gBACPlB,EAAiB7C,EAAO6C,gBAAkBmB,EAC1CC,OACe1D,IAAnBP,EAAOiE,QAAwBjE,EAAOiE,QAAUN,EAC9CO,EAAc,KACdC,GAAS,EACTC,GAAU,EACRC,EAAoB,SAAAjC,UAExBA,EAAMkC,SAASC,YACbL,IACCE,GACDF,EAAY/B,OAAOC,GACdA,UAGF,SAACA,EAAcoC,SACQpC,MAAtBkC,IAAAA,SAEFG,uBAEAD,EAAOE,OAASzF,EAAS,KACvB0F,GAAU,EACVC,EAAa,SAACC,EAASlD,GAYpBgD,IACHH,EAAOM,UAAU9E,EAAOF,IAAK+E,EAASlD,GACtCgD,GAAU,OAGdV,GACEc,WAAW,YACRJ,GACCC,OACErE,EACIyE,kEAEAhF,EAAOF,YAIdmE,GAGLG,GAAU,EAGLF,IAAaA,EAAcnE,EAAiBC,IAG7CsE,cAIGT,EAAYY,EAAWD,IAC1BF,SAAAA,OAK0B,mBAArBE,EAAOM,WACa,mBAApBN,EAAOS,SAEd,MAAUD,MACR,0OAGJR,EAAOS,SAASjF,EAAOF,KAEvB+C,EAAe7C,GAAQiD,KACrB,SAAAiC,IACkBlF,EAAOmF,SAAY,SAACC,EAAGC,UAAM7C,QAAQC,QAAQ2C,KACrDF,EAAepB,GAASb,KAC9B,SAAAqC,GACEV,EAAWU,IAEb,SAAAC,UAGahF,EAAWgF,MAI5B,SAAA5D,GACEiD,OAAWrE,EAAWoB,UAKrBkC,EAAYY,EAAWD,IAC1BF,UAAYR,QAAAA,EAASS,YAAY,KAE9B,GAAIC,EAAOE,OAASxF,SACzBiF,GAAS,EACTK,EAAOgB,OAAOhC,EAAiBxD,SAE1B6D,EAAYY,EAAWD,IAC1BF,SAAAA,IAEG,GAAIE,EAAOE,OAAS5F,SACzB0F,EAAOgB,OAAOtB,GAAeA,EAAY3B,cAEpCsB,EAAYY,EAAWD,IAC1BF,SAAAA,IAEG,GAAIE,EAAOE,OAAS1F,EACzBoF,GAAU,OACL,GAAII,EAAOE,OAAS3F,EAAW,IAEhCoF,EACF,YACKM,GACHH,cAAeA,GAAUC,YAAY,SAIrCC,EAAO1E,MAAQE,EAAOF,IAAK,KACzBN,EAAeqE,EAAYY,EAAWD,GACtClF,EAAekF,EAAOK,QAOtBpF,QAJkB,IAApBsE,QAA8CxD,IAAjBjB,EACzByE,EAAgBzE,EAAc8C,EAAO5C,EAAcQ,GACnDR,GAIJ8E,cAAeA,GAAUC,YAAY,aAEhCF,EAAkB5E,QAKxB6E,EAAU,OAAOT,EAAYzB,EAAOoC,OAIrC/E,EAAWoE,EAAYY,EAAWD,UAClC/E,IAAagF,EAAkBrC,EAC5BiC,OAAuB5E,GAAU6E,SAAAA,MCrL5C,IAAIkB,WCjB6CC,GAChD,IAAID,EACAE,EAASD,EAAKC,OAalB,MAXsB,mBAAXA,EACNA,EAAOC,WACVH,EAASE,EAAOC,YAEhBH,EAASE,EAAO,cAChBA,EAAOC,WAAaH,GAGrBA,EAAS,eAGHA,EDEKI,CAZO,oBAATC,KACFA,KACoB,oBAAXC,OACTA,OACoB,oBAAXC,OACTA,OACoB,oBAAXC,OACTA,OAEAC,SAAS,cAATA,IENLC,EAAe,WACjB,OAAOC,KAAKC,SAASC,SAAS,IAAIC,UAAU,GAAGC,MAAM,IAAIC,KAAK,MAG5DC,GACFC,KAAM,eAAiBR,IACvBS,QAAS,kBAAoBT,IAC7BU,qBAAsB,WACpB,MAAO,+BAAiCV,MA6C5C,SAASW,EAAYC,EAASC,EAAgBC,GAC5C,IAAIC,EAEJ,GAA8B,mBAAnBF,GAAqD,mBAAbC,GAA+C,mBAAbA,GAAmD,mBAAjBE,UAAU,GAC/H,MAAUlC,MAAM,uJAQlB,GAL8B,mBAAnB+B,QAAqD,IAAbC,IACjDA,EAAWD,EACXA,OAAiBxG,QAGK,IAAbyG,EAA0B,CACnC,GAAwB,mBAAbA,EACT,MAAUhC,MAAM,2CAGlB,OAAOgC,EAASH,EAATG,CAAsBF,EAASC,GAGxC,GAAuB,mBAAZD,EACT,MAAU9B,MAAM,0CAGlB,IAAImC,EAAiBL,EACjBM,EAAeL,EACfM,KACAC,EAAgBD,EAChBE,GAAgB,EASpB,SAASC,IACHF,IAAkBD,IACpBC,EAAgBD,EAAiBI,SAUrC,SAASC,IACP,GAAIH,EACF,MAAUvC,MAAM,wMAGlB,OAAOoC,EA2BT,SAASO,EAAUC,GACjB,GAAwB,mBAAbA,EACT,MAAU5C,MAAM,2CAGlB,GAAIuC,EACF,MAAUvC,MAAM,+TAGlB,IAAI6C,GAAe,EAGnB,OAFAL,IACAF,EAAcjF,KAAKuF,GACZ,WACL,GAAKC,EAAL,CAIA,GAAIN,EACF,MAAUvC,MAAM,oKAGlB6C,GAAe,EACfL,IACA,IAAIM,EAAQR,EAAcpF,QAAQ0F,GAClCN,EAAcS,OAAOD,EAAO,KA8BhC,SAASE,EAASxD,GAChB,IA7KJ,SAAuByD,GACrB,GAAmB,iBAARA,GAA4B,OAARA,EAAc,OAAO,EAGpD,IAFA,IAAIC,EAAQD,EAE4B,OAAjCtI,OAAOwI,eAAeD,IAC3BA,EAAQvI,OAAOwI,eAAeD,GAGhC,OAAOvI,OAAOwI,eAAeF,KAASC,EAqK/BE,CAAc5D,GACjB,MAAUQ,MAAM,2EAGlB,QAA2B,IAAhBR,EAAOE,KAChB,MAAUM,MAAM,sFAGlB,GAAIuC,EACF,MAAUvC,MAAM,sCAGlB,IACEuC,GAAgB,EAChBH,EAAeD,EAAeC,EAAc5C,WAE5C+C,GAAgB,EAKlB,IAFA,IAAIc,EAAYhB,EAAmBC,EAE1BgB,EAAI,EAAOD,EAAUlH,OAAdmH,EAAsBA,IAAK,EAEzCV,EADeS,EAAUC,MAI3B,OAAO9D,EA6ET,OAHAwD,GACEtD,KAAM+B,EAAYC,QAEbO,GACLe,SAAUA,EACVL,UAAWA,EACXD,SAAUA,EACVa,eAnEF,SAAwBC,GACtB,GAA2B,mBAAhBA,EACT,MAAUxD,MAAM,8CAGlBmC,EAAiBqB,EAKjBR,GACEtD,KAAM+B,EAAYE,aAyDb8B,GA9CT,WACE,IAAIC,EAEAC,EAAiBhB,EACrB,OAAOe,GASLf,UAAW,SAAmBiB,GAC5B,GAAwB,iBAAbA,GAAsC,OAAbA,EAClC,MAAM,IAAIC,UAAU,0CAGtB,SAASC,IACHF,EAASG,MACXH,EAASG,KAAKrB,KAMlB,OAFAoB,KAGEE,YAFgBL,EAAeG,OAK7BL,GAAgB,WACtB,OAAOQ,MACNP,GAcgCzB,EAGvC,SAuBSiC,EAA8BpJ,EAAK0E,GAC1C,IAAI2E,EAAa3E,GAAUA,EAAOE,KAElC,MAAO,UADiByE,GAAc,WAAqBA,EAAc,KAAQ,aAC3C,cAAiBrJ,EAAM,iLAgE/D,SAASsJ,EAAgBC,GAIvB,IAHA,IAAIC,EAAc3J,OAAOC,KAAKyJ,GAC1BE,KAEKjB,EAAI,EAAOgB,EAAYnI,OAAhBmH,EAAwBA,IAAK,CAC3C,IAAIxI,EAAMwJ,EAAYhB,GAQO,mBAAlBe,EAASvJ,KAClByJ,EAAczJ,GAAOuJ,EAASvJ,IAIlC,IASI0J,EATAC,EAAmB9J,OAAOC,KAAK2J,GAWnC,KA1FF,SAyB4BF,GAC1B1J,OAAOC,KAAKyJ,GAAUxJ,QAAQ,SAAUC,GACtC,IAAIgH,EAAUuC,EAASvJ,GAKvB,QAA4B,IAJTgH,OAAQvG,GACzBmE,KAAM+B,EAAYC,OAIlB,MAAU1B,MAAM,YAAelF,EAAM,iRAGvC,QAEO,IAFIgH,OAAQvG,GACjBmE,KAAM+B,EAAYG,yBAElB,MAAU5B,MAAM,YAAelF,EAAM,6EAAqF2G,EAAYC,KAAO,iTAoD/IgD,CAAmBH,GACnB,MAAOI,GACPH,EAAsBG,EAGxB,OAAO,SAAqBvH,EAAOoC,GAKjC,QAJc,IAAVpC,IACFA,MAGEoH,EACF,MAAMA,EAcR,IAXA,IAQII,GAAa,EACbC,KAEKC,EAAK,EAAQL,EAAiBtI,OAAtB2I,EAA8BA,IAAM,CACnD,IAAIC,EAAON,EAAiBK,GAExBE,EAAsB5H,EAAM2H,GAC5BE,GAAkBnD,EAFRyC,EAAcQ,IAEEC,EAAqBxF,GAEnD,QAA+B,IAApByF,EAAiC,CAC1C,IAAIC,EAAehB,EAA8Ba,EAAMvF,GACvD,MAAUQ,MAAMkF,GAGlBL,EAAUE,GAAQE,EAClBL,EAAaA,GAAcK,IAAoBD,EAGjD,OAAOJ,EAAaC,EAAYzH,GCzcpC,SAAwB+H,EACtB7K,EACAC,EACAC,SAGIC,OAAgBD,UAEhBF,GAAwC,WAAxBI,EAAOJ,IACzBK,OAAOC,KAAKN,GAAcO,QAAQ,SAAAC,GAqCtC,IAA6BsK,EAnCX,aAARtK,IAEAP,EAAcO,KAASN,EAAaM,KAcxCL,EAASK,GAoBA,QADcsK,EAzBC5K,EAAaM,KA0BnBuK,MAAMC,QAAQF,IAAmB,WAAb1K,EAAO0K,GApB7B9K,EAAaQ,QAJNL,EAASK,MAASR,EAAaQ,QAoBnDL,MClCH8K,GACJC,YACAC,cAAc,GAGVC,EAAmB,eAACtI,yDAAQmI,EAAc/F,gDACtCA,EAAOE,WACRvF,cACSiD,GAAOoI,qBAAcpI,EAAMoI,WAAUhG,EAAO1E,aACrDf,MACC4L,EAAavI,EAAMoI,SAAStI,QAAQsC,EAAO1E,KAC3C0K,IAAepI,EAAMoI,iBACzBA,EAASzC,OAAO4C,EAAY,QAChBvI,GAAOoI,SAAAA,EAAUC,aAAkC,IAApBD,EAASrJ,wBAE7CiB,gDCpBb,SACEpC,EACAqJ,UAEArJ,EAAO+D,qBACsBxD,IAA3BP,EAAO+D,gBACHoG,EACAnK,EAAO+D,gBACNH,EAAe5D,EAAQoJ,EAAgBC,oBDgBhD,SACEuB,EACAC,EACAC,OAoBIC,EAAgBD,IAAM,EAEtBE,EAAUnE,EACZ6D,EACAH,EACAM,GAAWA,EAAQ7D,SAAW6D,EAAQ7D,cAAWzG,GAE/C0E,EAAW,SAACnF,GACdkL,EAAQhD,UACNtD,KAAMvF,EACNW,IAAAA,KAIAgF,EAAY,SAAChF,EAAa+E,EAAiBlD,OACzCsJ,GACFvG,KAAM3F,EACN8F,QAAAA,EACAlD,IAAAA,EACA7B,IAAAA,GAGF8K,EAAM5C,SAASiD,GACfD,EAAQhD,SAASiD,GACbF,GAAiBG,EAAUxD,WAAW+C,eACxCM,IACAA,GAAgB,IAIhBG,OACCF,GACHG,MAAO,eACDC,YACJR,EAAM5C,UACJtD,KAAMxF,EACNsG,OAAQ,SAAA6F,GACND,EAAQ/I,KAAKgJ,MAGV7I,QAAQ8I,IAAIF,IAErB7I,MAAO,eACD6I,YACJR,EAAM5C,UACJtD,KAAM5F,EACN0G,OAAQ,SAAA+F,GACNH,EAAQ/I,KAAKkJ,MAGV/I,QAAQ8I,IAAIF,IAErBI,MAAO,WACLZ,EAAM5C,UACJtD,KAAM1F,KAGVyM,QAAS,WACPb,EAAM5C,UAAWtD,KAAMzF,EAASgG,SAAAA,EAAUH,UAAAA,cAIxC+F,GAAWA,EAAQa,eACvBR,EAAUO,UAGLP,4BEzHPS,EACA3L,UAGO,SACLoC,EACAwJ,OAEKxJ,SAGII,QAAQC,aAAQlC,OAGrBsL,EACFzJ,EAAMkC,eAAuC/D,IAA3B6B,EAAMkC,SAASR,QAC7B1B,EAAMkC,SAASR,QACf1E,KACFyM,IAAmBD,SAGdpJ,QAAQC,QAAQL,MAErByJ,EAAiBD,SAGZpJ,QAAQC,QAAQL,OAGrB0J,EAAgBnM,OAAOC,KAAK+L,GAC7BI,IAAI,SAAAC,UAAOC,SAASD,KACpBE,OAAO,SAAApM,UAAO8L,GAAkB9L,GAAOA,EAAM+L,IAC7CM,KAAK,SAACC,EAAGC,UAAMD,EAAIC,YAKhB/G,EAAgBwG,EAAcvK,OAAO,SAACa,EAAOkK,UAMxCX,EAAWW,GAAYlK,IAC7BA,UACII,QAAQC,QAAQ6C,GACvB,MAAO3D,UACAa,QAAQ+J,OAAO5K,iCC7C1B6K,EAEAC,OACAzM,4DAEIG,EAAYH,EAAOG,WAAa,KAChCD,EAAYF,EAAOE,WAAa,cAE3BwM,EAAwB5M,YAC3BK,IAAyC,IAA5BA,EAAU+B,QAAQpC,QAC/BI,IAAyC,IAA5BA,EAAUgC,QAAQpC,WAKnC4B,GAAI,SAACU,EAAetC,EAAa6M,UAC9BD,EAAwB5M,IAAQ0M,EAC7BA,EAAQpK,EAAOtC,EAAK6M,GACpBvK,GACNiB,IAAK,SAACjB,EAAetC,EAAa6M,UAC/BD,EAAwB5M,IAAQ2M,EAC7BA,EAASrK,EAAOtC,EAAK6M,GACrBvK"}