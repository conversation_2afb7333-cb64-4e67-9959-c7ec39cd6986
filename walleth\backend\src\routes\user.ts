import express from 'express';
import { body, validationResult } from 'express-validator';
import bcrypt from 'bcryptjs';
import speakeasy from 'speakeasy';
import { PrismaClient } from '@prisma/client';
import { asyncHandler } from '../middleware/errorHandler';
import { ValidationError, AuthenticationError, ConflictError } from '../middleware/errorHandler';
import { logger } from '../utils/logger';
import { NotificationService } from '../services/notificationService';

const router = express.Router();
const prisma = new PrismaClient();
const notificationService = new NotificationService();

// Validation rules
const updateProfileValidation = [
  body('firstName').optional().isLength({ min: 1 }).withMessage('First name cannot be empty'),
  body('lastName').optional().isLength({ min: 1 }).withMessage('Last name cannot be empty'),
  body('phone').optional().isMobilePhone('any').withMessage('Invalid phone number'),
  body('dateOfBirth').optional().isISO8601().withMessage('Invalid date format'),
  body('country').optional().isLength({ min: 2, max: 2 }).withMessage('Country must be 2-letter code')
];

const changePasswordValidation = [
  body('currentPassword').notEmpty().withMessage('Current password is required'),
  body('newPassword')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character')
];

// Get user profile
router.get('/profile',
  asyncHandler(async (req, res) => {
    const userId = req.user!.id;

    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        phone: true,
        dateOfBirth: true,
        country: true,
        role: true,
        kycStatus: true,
        kycLevel: true,
        isEmailVerified: true,
        isPhoneVerified: true,
        is2FAEnabled: true,
        referralCode: true,
        createdAt: true,
        updatedAt: true
      }
    });

    if (!user) {
      throw new AuthenticationError('User not found');
    }

    res.json({
      success: true,
      data: { user }
    });
  })
);

// Update user profile
router.put('/profile',
  updateProfileValidation,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError(errors.array().map(err => err.msg).join(', '));
    }

    const userId = req.user!.id;
    const { firstName, lastName, phone, dateOfBirth, country } = req.body;

    const updateData: any = {};
    if (firstName !== undefined) updateData.firstName = firstName;
    if (lastName !== undefined) updateData.lastName = lastName;
    if (phone !== undefined) updateData.phone = phone;
    if (dateOfBirth !== undefined) updateData.dateOfBirth = new Date(dateOfBirth);
    if (country !== undefined) updateData.country = country;

    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: updateData,
      select: {
        id: true,
        email: true,
        firstName: true,
        lastName: true,
        phone: true,
        dateOfBirth: true,
        country: true,
        role: true,
        kycStatus: true,
        isEmailVerified: true,
        isPhoneVerified: true,
        is2FAEnabled: true,
        referralCode: true,
        updatedAt: true
      }
    });

    logger.info('User profile updated', { userId });

    res.json({
      success: true,
      message: 'Profile updated successfully',
      data: { user: updatedUser }
    });
  })
);

// Change password
router.put('/password',
  changePasswordValidation,
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError(errors.array().map(err => err.msg).join(', '));
    }

    const userId = req.user!.id;
    const { currentPassword, newPassword } = req.body;

    // Get current user
    const user = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      throw new AuthenticationError('User not found');
    }

    // Verify current password
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
    if (!isCurrentPasswordValid) {
      throw new AuthenticationError('Current password is incorrect');
    }

    // Hash new password
    const hashedNewPassword = await bcrypt.hash(newPassword, parseInt(process.env.BCRYPT_ROUNDS || '12'));

    // Update password
    await prisma.user.update({
      where: { id: userId },
      data: { password: hashedNewPassword }
    });

    // Send notification
    await notificationService.sendNotification({
      userId,
      type: 'SECURITY_ALERT',
      title: 'Password Changed',
      message: 'Your password has been changed successfully.',
      sendEmail: true
    });

    logger.info('User password changed', { userId });

    res.json({
      success: true,
      message: 'Password changed successfully'
    });
  })
);

// Enable 2FA - Generate secret
router.post('/2fa/setup',
  asyncHandler(async (req, res) => {
    const userId = req.user!.id;

    const user = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      throw new AuthenticationError('User not found');
    }

    if (user.is2FAEnabled) {
      throw new ConflictError('2FA is already enabled');
    }

    // Generate secret
    const secret = speakeasy.generateSecret({
      name: `WALLETH (${user.email})`,
      issuer: 'WALLETH'
    });

    // Store temporary secret (not yet enabled)
    await prisma.user.update({
      where: { id: userId },
      data: { twoFactorSecret: secret.base32 }
    });

    res.json({
      success: true,
      message: '2FA setup initiated',
      data: {
        secret: secret.base32,
        qrCode: secret.otpauth_url,
        manualEntryKey: secret.base32
      }
    });
  })
);

// Verify and enable 2FA
router.post('/2fa/verify',
  body('token').isLength({ min: 6, max: 6 }).withMessage('Token must be 6 digits'),
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError(errors.array().map(err => err.msg).join(', '));
    }

    const userId = req.user!.id;
    const { token } = req.body;

    const user = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user || !user.twoFactorSecret) {
      throw new AuthenticationError('2FA setup not initiated');
    }

    // Verify token
    const verified = speakeasy.totp.verify({
      secret: user.twoFactorSecret,
      encoding: 'base32',
      token,
      window: 2
    });

    if (!verified) {
      throw new AuthenticationError('Invalid 2FA token');
    }

    // Enable 2FA
    await prisma.user.update({
      where: { id: userId },
      data: { is2FAEnabled: true }
    });

    // Send notification
    await notificationService.sendNotification({
      userId,
      type: 'SECURITY_ALERT',
      title: '2FA Enabled',
      message: 'Two-factor authentication has been enabled on your account.',
      sendEmail: true
    });

    logger.info('2FA enabled for user', { userId });

    res.json({
      success: true,
      message: '2FA enabled successfully'
    });
  })
);

// Disable 2FA
router.post('/2fa/disable',
  body('token').isLength({ min: 6, max: 6 }).withMessage('Token must be 6 digits'),
  body('password').notEmpty().withMessage('Password is required'),
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError(errors.array().map(err => err.msg).join(', '));
    }

    const userId = req.user!.id;
    const { token, password } = req.body;

    const user = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      throw new AuthenticationError('User not found');
    }

    if (!user.is2FAEnabled) {
      throw new ConflictError('2FA is not enabled');
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      throw new AuthenticationError('Invalid password');
    }

    // Verify 2FA token
    const verified = speakeasy.totp.verify({
      secret: user.twoFactorSecret!,
      encoding: 'base32',
      token,
      window: 2
    });

    if (!verified) {
      throw new AuthenticationError('Invalid 2FA token');
    }

    // Disable 2FA
    await prisma.user.update({
      where: { id: userId },
      data: {
        is2FAEnabled: false,
        twoFactorSecret: null
      }
    });

    // Send notification
    await notificationService.sendNotification({
      userId,
      type: 'SECURITY_ALERT',
      title: '2FA Disabled',
      message: 'Two-factor authentication has been disabled on your account.',
      sendEmail: true
    });

    logger.info('2FA disabled for user', { userId });

    res.json({
      success: true,
      message: '2FA disabled successfully'
    });
  })
);

// Get user notifications
router.get('/notifications',
  asyncHandler(async (req, res) => {
    const userId = req.user!.id;
    const { page = 1, limit = 20 } = req.query;

    const result = await notificationService.getUserNotifications(
      userId,
      Number(page),
      Number(limit)
    );

    res.json({
      success: true,
      data: result
    });
  })
);

// Mark notification as read
router.put('/notifications/:notificationId/read',
  asyncHandler(async (req, res) => {
    const userId = req.user!.id;
    const { notificationId } = req.params;

    await notificationService.markAsRead(userId, notificationId);

    res.json({
      success: true,
      message: 'Notification marked as read'
    });
  })
);

// Mark all notifications as read
router.put('/notifications/read-all',
  asyncHandler(async (req, res) => {
    const userId = req.user!.id;

    await notificationService.markAllAsRead(userId);

    res.json({
      success: true,
      message: 'All notifications marked as read'
    });
  })
);

// Get referral information
router.get('/referral',
  asyncHandler(async (req, res) => {
    const userId = req.user!.id;

    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        referralCode: true,
        referredBy: true
      }
    });

    if (!user) {
      throw new AuthenticationError('User not found');
    }

    // Get referral statistics
    const [referralCount, referrer] = await Promise.all([
      prisma.user.count({
        where: { referredBy: userId }
      }),
      user.referredBy ? prisma.user.findUnique({
        where: { id: user.referredBy },
        select: {
          email: true,
          firstName: true,
          lastName: true
        }
      }) : null
    ]);

    // Get referred users
    const referredUsers = await prisma.user.findMany({
      where: { referredBy: userId },
      select: {
        email: true,
        firstName: true,
        lastName: true,
        createdAt: true
      },
      orderBy: { createdAt: 'desc' },
      take: 10
    });

    res.json({
      success: true,
      data: {
        referralCode: user.referralCode,
        referralCount,
        referrer,
        referredUsers
      }
    });
  })
);

// Get login history
router.get('/login-history',
  asyncHandler(async (req, res) => {
    const userId = req.user!.id;
    const { page = 1, limit = 20 } = req.query;

    const skip = (Number(page) - 1) * Number(limit);

    const [loginHistory, total] = await Promise.all([
      prisma.loginHistory.findMany({
        where: { userId },
        orderBy: { createdAt: 'desc' },
        skip,
        take: Number(limit)
      }),
      prisma.loginHistory.count({
        where: { userId }
      })
    ]);

    res.json({
      success: true,
      data: {
        loginHistory,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit))
        }
      }
    });
  })
);

// Delete account (soft delete)
router.delete('/account',
  body('password').notEmpty().withMessage('Password is required'),
  body('confirmation').equals('DELETE').withMessage('Confirmation must be "DELETE"'),
  asyncHandler(async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      throw new ValidationError(errors.array().map(err => err.msg).join(', '));
    }

    const userId = req.user!.id;
    const { password } = req.body;

    const user = await prisma.user.findUnique({
      where: { id: userId }
    });

    if (!user) {
      throw new AuthenticationError('User not found');
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      throw new AuthenticationError('Invalid password');
    }

    // Check if user has active orders or pending withdrawals
    const [activeOrders, pendingWithdrawals] = await Promise.all([
      prisma.order.count({
        where: {
          userId,
          status: {
            in: ['PENDING', 'PARTIAL_FILLED']
          }
        }
      }),
      prisma.withdrawal.count({
        where: {
          userId,
          status: {
            in: ['PENDING', 'APPROVED', 'PROCESSING']
          }
        }
      })
    ]);

    if (activeOrders > 0 || pendingWithdrawals > 0) {
      throw new ValidationError('Cannot delete account with active orders or pending withdrawals');
    }

    // Soft delete user (change status to banned and anonymize data)
    await prisma.user.update({
      where: { id: userId },
      data: {
        status: 'BANNED',
        email: `deleted_${userId}@walleth.com`,
        firstName: null,
        lastName: null,
        phone: null,
        dateOfBirth: null,
        country: null,
        is2FAEnabled: false,
        twoFactorSecret: null
      }
    });

    logger.info('User account deleted', { userId });

    res.json({
      success: true,
      message: 'Account deleted successfully'
    });
  })
);

export default router;
